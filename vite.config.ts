import { defineConfig, loadEnv } from 'vite'
import { resolve } from 'node:path'
import vue from '@vitejs/plugin-vue'

import AutoImport from 'unplugin-auto-import/vite'
import compression from 'vite-plugin-compression'
import viteSrcReplace from './plugins/srcReplace'
import { visualizer } from 'rollup-plugin-visualizer'

import Inspect from 'vite-plugin-inspect'

// svg组件插件导入
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

import WindiCSS from 'vite-plugin-windicss'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  const { VITE_APP_AUTH, VITE_APP_PROXY_URL } = env
  let proxy = {}

  const urlObject = {}
  if (VITE_APP_PROXY_URL && VITE_APP_PROXY_URL !== '') {
    proxy = Object.assign(urlObject, {
      [VITE_APP_AUTH]: {
        target: VITE_APP_PROXY_URL,
        changeOrigin: true,
        secure: false,
        rewrite: (p: string) => p.replace(VITE_APP_AUTH, '')
      },
      '/test': {
        // target: "http://192.168.0.106:9999", //代理接口
        target: 'http://192.168.0.204:9021', //代理接口
        changeOrigin: true,
        secure: true,
        samesite: 'none',
        // rewrite: (path) => path.replace(/^\/auth/, '')
        rewrite: (p: string) => p.replace(RegExp('^\\' + '/test'), '')
      }
    })
  }
  // 配置别名
  return {
    resolve: {
      alias: {
        '@/': `${resolve(__dirname, 'src')}/`
      }
    },
    plugins: [
      vue(),
      viteSrcReplace(),
      WindiCSS(),
      // 自动导入方法
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
        eslintrc: {
          enabled: true
        },
        dts: true,
        vueTemplate: false
      }),
      //svg插件配置
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [resolve(process.cwd(), 'src/assets/icons')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]'
      }),
      // 打包体积预览
      visualizer({
        open: true
      }),
      // gzip压缩文件体积
      compression({
        algorithm: 'gzip', // 指定压缩算法为gzip,[ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
        ext: '.gz', // 指定压缩后的文件扩展名为.gz
        threshold: 10240, // 仅对文件大小大于threshold的文件进行压缩，默认为10KB
        deleteOriginFile: false, // 是否删除原始文件，默认为false
        filter: /\.(js|css|json|html|ico|svg)(\?.*)?$/i, // 匹配要压缩的文件的正则表达式，默认为匹配.js、.css、.json、.html、.ico和.svg文件
        compressionOptions: { level: 9 }, // 指定gzip压缩级别，默认为9（最高级别）
        verbose: true, //是否在控制台输出压缩结果
        disable: false //是否禁用插件
      }),
      Inspect()
    ],
    //scss预处理
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: "@import '@/styles/variables.scss';"
        }
      }
    },
    server: {
      hmr: true, // 启用热更新
      // 允许跨域
      cors: true,
      // 启动项目时自动打开
      open: false,
      port: 3000,
      proxy
    },
    base: '/serviceWeb/',
    build: {
      terserOptions: {
        compress: {
          //是否删除console
          drop_console: true,
          //是否删除debugger
          drop_debugger: true,
          //是否删除无用代码
          unused: true,
          //是否保留所有注释
          keep_fargs: false
        }
      },
      // 设置最终构建的浏览器兼容目标
      target: 'es2015',
      // 构建后是否生成 source map 文件
      sourcemap: false,
      //  chunk 大小警告的限制（以 kbs 为单位）
      chunkSizeWarningLimit: 2000,
      // 启用/禁用 gzip 压缩大小报告
      reportCompressedSize: false,
      // 自定义底层的 Rollup 打包配置
      minify: false,
      rollupOptions: {
        output: {
          // 最小化拆分包
          manualChunks(id) {
            if (id.includes('node_modules')) {
              // 通过拆分包的方式将所有来自node_modules的模块打包到单独的chunk中
              return id
                .toString()
                .split('node_modules/')[1]
                .split('/')[0]
                .toString()
            }
          },
          // 对代码分割中产生的 chunk 自定义命名
          chunkFileNames: 'static/js/[name]-[hash].js',
          // 指定 chunks 的入口文件模式
          entryFileNames: 'static/js/[name]-[hash].js',
          // 自定义构建结果中的静态资源名称
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          // 压缩 Rollup 产生的额外代码
          compact: true
        }
      }
    }
  }
})

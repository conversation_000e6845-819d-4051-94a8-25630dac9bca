{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "preserve",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "removeComments": true, // 是否移除 TypeScript 代码中的注释
    "noEmitHelpers": true, // 是否禁止生成 TypeScript 帮助函数
    "downlevelIteration": true, // 是否将 for...of 循环编译为适用于旧版 JavaScript 的代码
    "importHelpers": true // 是否使用模块化方式引入帮助函数
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/**/*.d.ts",
    "./*.d.ts",
    "./auto-imports.d.ts",
    "src/utils/wsclient.js"
  ],
  "exclude": ["dist", "node_modules"]
}

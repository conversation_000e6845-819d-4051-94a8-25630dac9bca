{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/wujie.d.ts", "./src/api/index.ts", "./src/api/addtask/index.ts", "./src/api/ai/index.ts", "./src/api/auth/index.ts", "./src/api/dict/index.ts", "./src/api/event/index.ts", "./src/api/file/index.ts", "./src/api/help/index.ts", "./src/api/home/<USER>", "./src/api/login/index.ts", "./src/api/logs/index.ts", "./src/api/manage/index.ts", "./src/api/manager/index.ts", "./src/api/map/index.ts", "./src/api/membermanagement/index.ts", "./src/api/menu/index.ts", "./src/api/message/index.ts", "./src/api/order/index.ts", "./src/api/servicepage/index.ts", "./src/api/upgrade/index.ts", "./src/api/user-organization/index.ts", "./src/api/videocenter/index.ts", "./src/api/weatherservice/index.ts", "./src/components/editor/types/@wangeditor/editor-for-vue.d.ts", "./src/components/logmodel/api/index.ts", "./src/config/nprogress/index.ts", "./src/directives/authority.ts", "./src/directives/index.ts", "./src/enmu/index.ts", "./src/hooks/login.ts", "./src/hooks/message.ts", "./src/hooks/tabledata.ts", "./src/router/index.ts", "./src/router/utils/getroutes.ts", "./src/store/index.ts", "./src/store/modules/app.ts", "./src/store/modules/message.ts", "./src/store/modules/route.ts", "./src/store/modules/user.ts", "./src/utils/tools.ts", "./src/utils/dayjs.ts", "./src/utils/enc.ts", "./src/utils/initsysdata.ts", "./src/utils/other.ts", "./src/utils/request.ts", "./src/utils/requestcs.ts", "./src/utils/storage.ts", "./src/utils/timertools.ts", "./src/utils/toast.ts", "./src/views/serverplatformdevice/types/leaflet.d.ts", "./src/app.vue", "./src/components/svgicon.vue", "./src/components/commonvideo/h264.vue", "./src/components/commonvideo/index.vue", "./src/components/commonvideo/jcvideo.vue", "./src/components/commonvideo/wsvideo.vue", "./src/components/datepicker/index.vue", "./src/components/deldialog/autodel.vue", "./src/components/deldialog/index.vue", "./src/components/dialogview/index.vue", "./src/components/editor/codeeditor.vue", "./src/components/elementtable/index.vue", "./src/components/lefttreelist/index.vue", "./src/components/lefttreelist/newindex.vue", "./src/components/logmodel/index.vue", "./src/components/modelview/index.vue", "./src/components/previewimg/index.vue", "./src/components/selectbydict/index.vue", "./src/components/treelist/index.vue", "./src/components/upload/uploadimg.vue", "./src/components/upload/index.vue", "./src/components/codeeditor/index.vue", "./src/components/exportout/index.vue", "./src/components/mapcom/index.vue", "./src/components/tabtransview/index.vue", "./src/components/tabtransview/table-box.vue", "./src/components/tabtransview/title-box.vue", "./src/components/tableview/index.vue", "./src/layout/index.vue", "./src/layout/components/layoutheader.vue", "./src/layout/components/menulistbox.vue", "./src/layout/components/submenulistbox.vue", "./src/views/home/<USER>", "./src/views/login/index.vue", "./src/views/error/notfound.vue", "./src/views/serverevent/eventdistribution.vue", "./src/views/serverevent/eventrecord.vue", "./src/views/servermessagecenter/logs.vue", "./src/views/servermessagecenter/read.vue", "./src/views/servermessagecenter/subscribe.vue", "./src/views/servermessagecenter/unread.vue", "./src/views/servermessagecenter/components/jiedianselect.vue", "./src/views/servermessagecenter/components/camerasubscription.vue", "./src/views/servermessagecenter/components/headinformation.vue", "./src/views/servermessagecenter/components/msginfo.vue", "./src/views/servermessagecenter/components/shuttletable.vue", "./src/views/serverpersonalcenter/appkeymanage.vue", "./src/views/serverpersonalcenter/baseinfo.vue", "./src/views/serverplatformapp/index.vue", "./src/views/serverplatformapp/components/addfrom.vue", "./src/views/serverplatformapp/components/addtask.vue", "./src/views/serverplatformapp/components/addtaskdia.vue", "./src/views/serverplatformapp/components/algorithmdetile.vue", "./src/views/serverplatformapp/components/appdetails.vue", "./src/views/serverplatformapp/components/bindingalgorithm.vue", "./src/views/serverplatformapp/components/channellist.vue", "./src/views/serverplatformapp/components/newtimeselect.vue", "./src/views/serverplatformapp/components/regionaldivision.vue", "./src/views/serverplatformapp/components/rundia.vue", "./src/views/serverplatformapp/components/selectalgorithm.vue", "./src/views/serverplatformapp/components/selectcamera.vue", "./src/views/serverplatformapp/components/selecttime.vue", "./src/views/serverplatformapp/components/selectvideo.vue", "./src/views/serverplatformapp/components/serverinfo.vue", "./src/views/serverplatformapp/components/timeplan.vue", "./src/views/serverplatformapp/components/videoinfo.vue", "./src/views/serverplatformdevice/devicedistribution.vue", "./src/views/serverplatformdevice/sensor.vue", "./src/views/serverplatformdevice/video.vue", "./src/views/serverplatformdevice/components/treelist.vue", "./src/views/serverplatformdevice/components/commonvideo.vue", "./src/views/serverplatformdevice/components/mapdialog.vue", "./src/views/serverplatformdevice/components/videopreview.vue", "./src/views/serverplatformhelp/index.vue", "./src/views/serverplatformhelp/components/helpmenutree.vue", "./src/views/serverplatformmembermanage/deptmanage.vue", "./src/views/serverplatformmembermanage/rolemanage.vue", "./src/views/serverplatformmembermanage/usermanage.vue", "./src/views/serverplatformmembermanage/components/addauth.vue", "./src/views/serverplatformmembermanage/components/assignsensor.vue", "./src/views/serverplatformmembermanage/components/assignservice.vue", "./src/views/serverplatformmembermanage/components/assignvideos.vue", "./src/views/serverplatformmembermanage/components/authlist.vue", "./src/views/serverplatformmembermanage/components/ownresauth.vue", "./src/views/serverplatformmembermanage/components/platformresauth.vue", "./src/views/serverplatformmembermanage/components/roleadd.vue", "./src/views/serverplatformmembermanage/components/setrole.vue", "./src/views/serverplatformmembermanage/components/useradd.vue", "./src/views/serverplatformordermanage/createorder.vue", "./src/views/serverplatformordermanage/myorder.vue", "./src/views/serverplatformordermanage/components/authorizedia.vue", "./src/views/serverplatformordermanage/components/reminderandreview.vue", "./src/views/serverplatformordermanage/components/uploadimg.vue", "./src/views/serverplatformservice/index.vue", "./src/views/serverplatformservice/components/algorithmdetile.vue", "./src/views/serverplatformservice/components/apidetails.vue", "./src/views/serverplatformservice/components/errorcode.vue", "./src/views/serverplatformservice/components/rundia.vue", "./src/views/serverplatformservice/components/servicedetails.vue", "./src/views/serverplatformservice/components/testcode.vue", "./src/views/upgrade/index.vue", "./auto-imports.d.ts"], "version": "5.7.3"}
import { nextTick } from 'vue'

// 权限按钮自定义指令
const permissionButton = {
  mounted(el: any, binding: any) {
    nextTick(() => {
      el.setAttribute('role', '')
      // 检查是否是最后一个按钮
      const parent = el.parentNode
      const siblings = Array.from(parent.children).filter((child: any) => {
        return child !== el && child.hasAttribute('role')
      })
      if (siblings.length < parent.children.length - 1) {
        el.classList.add('borr')
      }
    })
  }
}

export default permissionButton

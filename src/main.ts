import { createApp } from 'vue'

import router from './router'
import store from './store'

import 'virtual:svg-icons-register'

import '@/styles/main.scss'
import 'virtual:windi.css'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'

// 导入通用组件
import SvgIcon from '@/components/SvgIcon.vue'
import ElementTable from '@/components/ElementTable/index.vue'

// 注册指令
import installDirectives from './directives'

const app = createApp(App)

app.component('svg-icon', SvgIcon)
app.component('ElementTable', ElementTable)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
installDirectives(app)
app.use(store)
app.use(router)

app.use(ElementPlus)
app.mount('#app')

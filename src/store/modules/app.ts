import { defineStore } from 'pinia'
import { Local } from '@/utils/storage.js'

export const useAppStore = defineStore('counter', () => {
  // 用户信息
  const userInfo = ref(Local.get('userInfo') || {})

  // 用户名称
  const userName = ref(Local.get('name') || '')

  // 设置用户信息
  const setUserInfo = (value: any) => {
    userInfo.value = value
    Local.set('userInfo', value)
  }

  // 设置用户名称
  const setUserName = (value: any) => {
    userName.value = value
    Local.set('name', value)
  }

  // 是否需要修改密码方可登陆

  const needModifyPassword = ref(Local.get('needModifyPassword') || false)

  // 设置是否需要修改密码
  const setNeedModifyPassword = (value: any) => {
    Local.set('needModifyPassword', value)
    needModifyPassword.value = value
  }

  // 收起菜单
  return {
    userInfo,
    userName,
    needModifyPassword,
    setUserInfo,
    setUserName,
    setNeedModifyPassword
  }
})

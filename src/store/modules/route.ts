//路由
import { defineStore } from 'pinia'
import { Local } from '@/utils/storage'
import { removeNodesByPath } from '@/router/utils/getRoutes'
export const useRouteStore = defineStore('routes', () => {
  // 路由集合
  const routes = ref<Array<any>>([])

  // 设置路由
  const setRoutes = (data: any) => {
    routes.value = JSON.parse(JSON.stringify(data))
    // 获取用户是否为管理员
    const isAdmin = Local.get('isAdmin')
    if (isAdmin !== 0) {
      routes.value = removeNodesByPath(
        routes.value,
        '/serverPersonalCenter/appKeyManage'
      )
    }
    Local.set('routes', routes.value)
  }

  // 返回需要菜单的路由
  const haveMenuRoutes = computed(() => {
    const list =
      routes.value?.length > 0
        ? routes.value.filter(
            (item: any) => item.children && item.children.length > 0
          )
        : Local.get('routes')?.filter(
            (item: any) => item.children && item.children.length > 0
          )
    return list
  })

  // 路由下级菜单
  const ChildrenMenuRoutes = ref<Array<any>>([])

  // 获取下级菜单
  const getChildrenMenuRoute = (path: string) => {
    ChildrenMenuRoutes.value =
      haveMenuRoutes.value?.find((item: any) => item.path == path)?.children ||
      []
  }

  // 面包屑导航路由
  const breadcrumbRoutes = ref<string>('')

  // 保存面包屑导航
  const setBreadcrumbRoutes = (url: string) => {
    breadcrumbRoutes.value = url
  }
  // 递归获取目标目录
  const getTargetRoute = (arr: any[], path: string) => {
    let routes: any[] = []
    let haveRoute = arr?.find((v: any) => v.path == path)
    if (haveRoute) {
      routes.push(haveRoute)
    } else {
      let parentPath = path?.split('/').slice(0, -1).join('/')
      let parent = arr?.find((v: any) => v.path == parentPath)
      routes.push(parent)
      let node = getTargetRoute(parent.children, path)
      routes.push(...node)
    }
    return routes
  }

  // 获取面包屑导航
  const getBreadcrumbRoutes = computed(() => {
    if (!breadcrumbRoutes.value || breadcrumbRoutes.value == '/home') return
    return getTargetRoute(
      routes.value.length > 0 ? routes.value : Local.get('routes'),
      breadcrumbRoutes.value
    )
  })

  //是否获取用户信息
  const isGetUserInfo = ref<boolean>(false)

  //修改用户信息状态
  const setUserInfoState = (flag: boolean) => {
    isGetUserInfo.value = flag
  }

  //把数据返给外面用
  return {
    routes,
    isGetUserInfo,
    haveMenuRoutes,
    ChildrenMenuRoutes,
    getBreadcrumbRoutes,
    setUserInfoState,
    setRoutes,
    getChildrenMenuRoute,
    setBreadcrumbRoutes
  }
})

import { defineStore } from 'pinia'

export const useMessageStore = defineStore('message', () => {
  const checkedDeviceList = ref<any[]>([])

  // 设置已选列表
  const setCheckedDeviceList = (data: any[]) => {
    if (data.length == 0) {
      checkedDeviceList.value.length = 0
      return
    }
    data.forEach((item: any) => {
      if (!checkedDeviceList.value.find((v) => v.id == item.id)) {
        checkedDeviceList.value.push(item)
      }
    })
  }

  // 删除已选列表项
  const deleteCheckedDeviceList = (ids: any[]) => {
    ids.forEach((item: any) => {
      let index = checkedDeviceList.value.findIndex((v) => v.id == item)
      checkedDeviceList.value.splice(index, 1)
    })
  }

  return {
    checkedDeviceList,
    setCheckedDeviceList,
    deleteCheckedDeviceList
  }
})

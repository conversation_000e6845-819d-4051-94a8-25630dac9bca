<script setup lang="ts">
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import initSysData from '@/utils/initSysData';
import { restartAllIntervals } from '@/utils/timerTools';
const locale = zhCn


const isRouterActive = ref(true)
provide('reload', () => {
  isRouterActive.value = false
  nextTick(() => {
    isRouterActive.value = true
  })
})

const $router = useRouter()
const $route = useRoute()
if (window.$wujie) {
  window.$wujie?.bus.$on("routeToLogin", (path: string) => {
    // if (path == '/servicePlatform/login'){
    $router.push({ path: '/' })
    // }
  });

  window.$wujie?.bus.$on("refreshApp", (name: string, flag: boolean) => {
    if (name === 'servicePlatform') {
      initSysData()
      if (!flag) {
        restartAllIntervals()
        window.$wujie?.bus.$emit("refreshAppDone", 'servicePlatform')
      }

    }
  });
}

</script>

<template>
  <div id="app">
    <el-config-provider :locale="locale">
      <router-view></router-view>
    </el-config-provider>
  </div>
</template>

<style scoped></style>

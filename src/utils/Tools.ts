export function autoAdapt(id: string = 'layout_container') {
  const base = window.ontouchstart === undefined ? 1080 : 800
  const rate = Math.min(window.innerWidth / base, window.innerHeight / base, 1)
  // const body = document.getElementsByTagName('body')[0];
  const body = document.getElementById(id) as HTMLBodyElement

  body.style.transform = `scale(${rate})`
  body.style.transformOrigin = 'center center'
  body.style.width = window.innerWidth / rate + 'px'
  body.style.height = window.innerHeight / rate + 'px'
  // 默认本来是 abs
  body.style.position = 'fixed'
  // body.style.overflow = 'hidden';
  body.style.top = (window.innerHeight - window.innerHeight / rate) / 2 + 'px'
  body.style.left = (window.innerWidth - window.innerWidth / rate) / 2 + 'px'
}

/**
 * 根据提供的值找出树组件中与之匹配的节点
 *
 * @param {*} tree 树形态数据
 * @param {*} value 匹配的值
 * @param {*} key 匹配的Key
 * @returns {*} 匹配的节点或者 undefined 如果没有匹配的节点
 */
export function findTreeNode(tree:any, value:any, key:any) {
  for (const node of tree) {
    if (node[key] === value) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const result:any = findTreeNode(node.children, value, key);
      if (result) {
        return result;
      }
    }
  }
  return undefined; // 明确表示未找到
}


// 根据传入的 值, 筛选出树结构中不符合的项
/**
 *
 * @param {*} treeData 树组件数据
 * @param {*} key 要筛选的key
 * @param {*} valueToExclude 要排除的值
 * @returns
 */
export const filterTreeByValue = (treeData:any, key:any, valueToExclude:any) => {
  console.log(key, valueToExclude);
  // 递归函数用于遍历树结构
  function traverseAndFilter(node:any) {
    // 如果当前节点的值与要排除的值相等，则从树中删除该节点
    if (node[key] == valueToExclude) {
      return null; // 返回 null 表示删除该节点
    }

    // 如果当前节点有子节点，则递归地过滤它们
    if (node.children) {
      node.children = node.children.flatMap((child:any) => {
        const filteredChild = traverseAndFilter(child);
        return filteredChild !== null ? [filteredChild] : [];
      });

      // 如果当前节点没有子节点了，则删除 children 属性
      if (!node.children.length) {
        delete node.children;
      }
    }

    return node; // 返回当前节点
  }

  // 开始遍历整个树
  return treeData.flatMap((rootNode:any) => {
    const filteredRoot = traverseAndFilter(rootNode);
    return filteredRoot !== null ? [filteredRoot] : [];
  });
};

// 生成时间断
export function generateTimeIntervals(startHour: any, endHour: any) {
  const intervals = [];

  const startSeconds = startHour * 3600;
  const endSeconds = endHour * 3600;

  for (let from = startSeconds; from < endSeconds; from += 3600) {
    const to = from + 3600;
    intervals.push({
      from: from.toString(),
      to: to.toString(),
    });
  }

  return intervals;
}


export function getTimeInterval(hour:any) {
  // 一小时的秒数
  const secondsInAnHour = 3600;

  const weekDayStart = (hour - 1) * secondsInAnHour;

  // 计算指定小时的时间段
  const from = weekDayStart;
  const to = from + secondsInAnHour;

  // 返回结果
  return {
    from: from.toString(),
    to: to.toString(),
  };
}

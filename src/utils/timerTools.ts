// 保存原始的 setInterval 和 clearInterval
const originalSetInterval = window.setInterval;
const originalClearInterval = window.clearInterval;

// 用于存储所有的定时器信息
const intervals = [] as any[];

// 重写 setInterval，保存每次创建的定时器信息
window.setInterval = function (callback: any, delay: any, ...args: any) {
    const id = originalSetInterval(callback, delay, ...args);
    intervals.push({ id, callback, delay, args, active: true }); // 添加 active 标记
    return id;
} as any;

// 重写 clearInterval，确保定时器在清除时从数组中标记为非活动
window.clearInterval = function (id: any) {
    const index = intervals.findIndex(interval => interval.id === id);
    if (index !== -1) {
        intervals[index].active = false; // 标记为非活动
    }
    originalClearInterval(id);
};

// 暂停所有的定时器
export function pauseAllIntervals() {
    intervals.forEach(interval => {
        if (interval.active) { // 只暂停活动的定时器
            originalClearInterval(interval.id);
            interval.active = false; // 标记为非活动
        }
    });
}

// 只重启未启动的定时器
export function restartAllIntervals() {
    intervals.forEach(interval => {
        if (!interval.active) { // 只重启非活动的定时器
            const newId = originalSetInterval(interval.callback, interval.delay, ...interval.args);
            interval.id = newId; // 更新定时器的 ID
            interval.active = true; // 标记为活动
        }
    });
}

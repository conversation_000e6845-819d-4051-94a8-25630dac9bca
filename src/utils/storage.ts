/**
 * window.localStorage 浏览器永久缓存
 * @method set 设置永久缓存
 * @method get 获取永久缓存
 * @method remove 移除永久缓存
 * @method clear 移除全部永久缓存
 */
export const Local = {
  // 查看 v2.4.3版本更新日志
  // setKey(key) {
  // 	// @ts-ignore
  // 	return `${__NEXT_NAME__}:${key}`;
  // },
  // 设置永久缓存
  set(key: any, val: any) {
    window.localStorage.setItem(key, JSON.stringify(val))
  },
  // 获取永久缓存
  get(key: any) {
    let json = window.localStorage.getItem(key)
    return JSON.parse(json as string)
  },
  // 移除永久缓存
  remove(key: any) {
    window.localStorage.removeItem(key)
  },
  // 移除全部永久缓存
  clear() {
    window.localStorage.clear()
  }
}

function WsStreamClient(
  canvas,
  successCallback = null,
  errorCallback = null,
  closeCallback = null
) {
  this._ws = null
  this._decoderReady = false
  this._WsSocketPacket = null
  this._videoFrames = []
  this._videoCanvas = canvas

  this._prevWidth = 0
  this._prevHeight = 0
  this._bitrate = 0
  this._recvTotalframe = 0
  this._recvTotalBytes = 0
  this._prevTimestamp = 0

  this._onVideoInfo = null

  this._successCallback = successCallback //成功回调函数
  this._errorCallback = errorCallback //错误回调函数
  this._closeCallback = closeCallback //关闭回调函数
}

WsStreamClient.prototype.isSupport = function () {
  if (!('VideoDecoder' in window)) {
    alert('VideoDecoder API unsupport.')
    return false
  }
  return true
}

//视频信息回调....
WsStreamClient.prototype.onVideoInfo = function (fn) {
  this._onVideoInfo = fn
}

WsStreamClient.prototype.liveview = function (
  proxyip,
  proxyport,
  token,
  security = true
) {
  this.shutdown()

  this._decoder = new VideoDecoder({
    error: (event) => {
      //处理编码异常
      this._decoder = null
      this.shutdown()
      console.error('videoDecoder错误:', event)
    },
    //处理编码块
    //创建编码器，并指定输出，输出函数一旦确定后无法更改
    output: (videoFrame) => {
      //{ resizeQuality: "pixelated" }
      createImageBitmap(videoFrame).then((img) => {
        this._videoFrames.push({
          img,
          duration: videoFrame.duration,
          timestamp: videoFrame.timestamp
        })
        videoFrame.close()
        this._draw()
      })
    }
  })

  this._ws = new WebSocket(
    `${security ? 'wss' : 'ws'}://${proxyip}:${proxyport}/liveview/${token}`
  )
  this._ws.binaryType = 'arraybuffer'
  this._ws.addEventListener(
    'open',
    ((event) => {
      console.log('websocket open!!!')
      //发送一个测试数据....
      /*
		setInterval(() => {
			this._ws.send(
				JSON.stringify({
					"cmd": "start",
					"type": "liveview",
					"intval": 100,
					"data": {}
				}));
		}, 2000);
		*/
      /*
		this._ws.send(
			JSON.stringify({
				"cmd": "start",
				"type": "liveview",
				"intval": 100,
				"data": {}
			}));
		*/
    }).bind(this)
  )

  this._ws.addEventListener('error', (error) => {
    console.error('websocket error:', error)
    this._errorCallback(error)
  })

  this._ws.addEventListener('close', (closeEvent) => {
    console.log('websocket closed!!!')
    this._closeCallback()
  })

  this._ws.addEventListener('message', (event) => {
    let bytes = new Uint8Array(event.data)
    let lenBytes = bytes.subarray(0, 2)
    let headLen = new Uint32Array(lenBytes)[0]
    let headBytes = bytes.subarray(2, headLen + 2)
    let bodyBytes = bytes.subarray(headLen + 2, bytes.length - 8)
    // let bodyBytes = bytes.subarray(headLen + 2);
    const strJson = new TextDecoder().decode(headBytes)
    let packagekey = JSON.parse(strJson)
    packagekey['body'] = bodyBytes
    this._onHandWsPacket(packagekey)
  })
}
/**
 *
 * @param {string} proxyip		//流媒体地址
 * @param {number} proxyport	//流媒体端口...
 * @param {string} dev_ip 		//设备地址...
 * @param {number} dev_port 	//设备端口...
 * @param {string} dev_user 	//设备用户名...
 * @param {string} dev_pwd 		//设备密码...
 * @param {string} dev_type		//设备类型...
 * @param {number} dev_ch_no 	//设备通道号...
 * @param {number} dev_stream_id//设备码流号...
 */
/*
WsStreamClient.prototype.liveview = function (proxyip, proxyport, dev_ip, dev_port, dev_user, dev_pwd, dev_type, dev_ch_no, dev_stream_id) {
	this.shutdown();

	this._ws = new WebSocket(`ws://${proxyip}:${proxyport}/liveview`);
	this._ws.binaryType = 'arraybuffer';
	this._ws.addEventListener('open', ((event) => {
		//测试用.... 请求一个测试流....
		//const buffer = new TextEncoder().encode();
		//ws.send(new TextEncoder().encode(JSON.stringify({ method: "liveview", param: { ch_no: 1, stream_id: 0 } })));//发送Utf8Array
		console.log(`websocket<${proxyip}:${proxyport}}> connected!!!`);
		if (!this._ws) {
			return;
		}
		const device_info = {
			address: dev_ip,
			port: dev_port,
			user: dev_user,
			pwd: dev_pwd,
			type: dev_type,
			ch_no: dev_ch_no,
			stream_id: dev_stream_id
		};
		this._ws.send(JSON.stringify({ method: "liveview", param: device_info }));

	}).bind(this));
	this._ws.addEventListener("error", (error) => {
		console.error("websocket error:", error);
	});
	this._ws.addEventListener("close", (closeEvent) => {
		console.log("websocket closed!!!");
	});
	this._ws.addEventListener('message', (event) => {
		let bytes = new Uint8Array(event.data);
		let lenBytes = bytes.subarray(0, 2);
		let headLen = new Uint32Array(lenBytes)[0];
		let headBytes = bytes.subarray(2, headLen + 2);
		let bodyBytes = bytes.subarray(headLen + 2);
		const strJson = new TextDecoder().decode(headBytes);
		let package = JSON.parse(strJson);
		package["body"] = bodyBytes;
		this._onHandWsPacket(package);
	});
}
*/
/**
 * 断开视频连接....
 */
WsStreamClient.prototype.shutdown = function () {
  if (this._ws) {
    this._ws.close()
    this._ws = null
  }
  if (this._decoder && this._decoder.state != 'closed') {
    this._decoder.reset()
  }
  this._decoder = null
  if (this._videoFrames) {
    this._videoFrames.length = 0
  }
  this._decoderReady = false
  if (this._videoCanvas) {
    this._videoCanvas.width = this._videoCanvas.width
  }
}

//下面是私有函数...
WsStreamClient.prototype._onHandWsPacket = function (packet) {
  const { type, ...other } = packet
  switch (type) {
    case 'video':
      this._feed(other)
      break
    case 'msg':
      break
    default:
      break
  }
}

WsStreamClient.prototype._feed = function (videoPacket) {
  const { iframe, tstamp, body } = videoPacket
  if (parseInt(iframe) > 0) {
    //i帧到了
    let { codec, width, height } = videoPacket

    if (!this._decoderReady) {
      // 第一次  配置解码器...
      if (!this._decoderReady) {
        let hasAudio = false
        this._decoder.configure({ codec: codec, width, height, hasAudio })
        this._decoderReady = true
      }
    }

    if (this._onVideoInfo != null) {
      let type = ''
      if (codec.includes('avc1.')) {
        //H265
        type = 'H264'
      } else if (codec.includes('hev1.')) {
        //H264
        type = 'H265'
      }
      const passTimes = (Date.now() - this._prevTimestamp) / 1000
      const bitrate = Math.round(this._recvTotalBytes / passTimes / 1024)
      const fps = Math.round(this._recvTotalframe / passTimes)
      const info = {
        w: this._prevWidth,
        h: this._prevHeight,
        bitrate,
        fps,
        type
      }

      console.log(info)
      this._onVideoInfo(info)

      this._prevTimestamp = Date.now()
      this._recvTotalBytes = 0
      this._recvTotalframe = 0
    }
  }
  this._recvTotalBytes += body.byteLength
  this._recvTotalframe++
  //解码出现困难....????
  if (this._videoFrames.length > 200) {
    if (!iframe) {
      return
    }
  }
  //const { body, tstamp } = videoPacket;
  const chunk = new EncodedVideoChunk({
    timestamp: tstamp, //视频帧的时间戳
    type: iframe ? 'key' : 'delta', //是否是关键帧
    data: body //视频数据
    // AVCDecoderConfigurationRecord（AVC Squence Header），包含H.264解码相关信息，如果是avc格式，需要提供，
    // 具体的解释需要去了解H.264 解码器avcC
    // description: getExtradata(),
  })

  this._decoder.decode(chunk)
}

WsStreamClient.prototype._draw = function () {
  if (this._videoFrames.length <= 0) {
    return
  }
  const img = this._videoFrames.shift()
  if (!img) {
    return
  }
  const { img: image } = img
  //const canvas = document.getElementById("canvas");
  if (this._prevWidth != image.width || this._prevHeight != image.height) {
    this._prevWidth = image.width
    this._prevHeight = image.height
    this._videoCanvas.width = image.width
    this._videoCanvas.height = image.height
  }

  const ctx = this._videoCanvas.getContext('2d')

  ctx.drawImage(image, 0, 0, this._videoCanvas.width, this._videoCanvas.height)
  requestAnimationFrame(this._draw.bind(this))

  // console.log('画面绘制完成', Date.now())
  this._successCallback()
}

export default WsStreamClient

/**
 * 加密算法
 */
import * as CryptoJS from 'crypto-js'

//加密方法
export function encrypt(str: string): any {
  const encPassword = encryption(str, import.meta.env.VITE_PWD_ENC_KEY)
  return encPassword
}

//解密方法
export function decrypt(str: string): any {
  const originData = decryption(
    str.toString(),
    import.meta.env.VITE_PWD_ENC_KEY
  )
  return originData
}

/**
 *  解密
 * @param {*} params 参数列表
 * @returns 明文
 */
export function decryption(src: string, keyWord: string): string {
  const key = CryptoJS.enc.Utf8.parse(keyWord)
  // 解密逻辑
  var decryptd = CryptoJS.AES.decrypt(src, key, {
    iv: key,
    mode: CryptoJS.mode.CFB,
    padding: CryptoJS.pad.NoPadding
  })

  return decryptd.toString(CryptoJS.enc.Utf8)
}

/**
 *加密处理
 */
export function encryption(src: string, keyWord: string): string {
  const key = CryptoJS.enc.Utf8.parse(keyWord)
  // 加密
  var encrypted = CryptoJS.AES.encrypt(src, key, {
    iv: key,
    mode: CryptoJS.mode.CFB,
    padding: CryptoJS.pad.NoPadding
  })
  return encrypted.toString()
}

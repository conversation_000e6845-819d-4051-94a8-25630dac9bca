import { ElMessage, MessageParams } from 'element-plus'
/**
 *
 * @param msg 提示的内容
 * @param type 提示的类型
 */
function showToast(msg: any, type: string, className = '') {
  const params = {
    message: msg,
    type,
    customClass: className
  } as MessageParams
  ElMessage(params)
}

function success(msg: string) {
  showToast(msg, 'success')
}
function warning(msg: string) {
  showToast(msg, 'warning')
}
function info(msg: string) {
  showToast(msg, 'info')
}
function error(msg: string, className: string) {
  showToast(msg, 'error', className)
}

export { showToast, success, warning, info, error }

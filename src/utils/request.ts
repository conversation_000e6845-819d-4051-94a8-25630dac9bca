import axios, {
  AxiosInstance,
  InternalAxiosRequestConfig,
  AxiosResponse
} from 'axios'
import { useMessage } from '@/hooks/message'

import router from '@/router'

class myRequset {
  instance: AxiosInstance
  constructor(url: string) {
    const instance = axios.create({
      baseURL: import.meta.env.VITE_APP_PREFIX + url
      // timeout: 30000,
    } as any)

    // 请求拦截器，可以在发送请求前进行处理
    instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig<any>) => {
        let token = localStorage.getItem('token')
        let tenant_id = JSON.parse(
          localStorage.getItem('userInfo') as string
        )?.tenant_id
        if (!!token && config.url != `/auth/oauth2/token`) {
          config.headers.authorization = 'Bearer ' + token
          config.headers['Tenant-Id'] = tenant_id
        }
        return config
      },
      (error) => {
        // 处理请求错误
        return Promise.reject(error)
      }
    )

    // 响应拦截器，可以在接收响应后进行处理
    instance.interceptors.response.use(
      async (response: AxiosResponse<any>) => {
        if (response.config!.url!.includes('/auth/oauth2/token')) {
          localStorage.setItem('token', response.data.access_token)
          localStorage.setItem(
            'userInfo',
            JSON.stringify(response.data.user_info)
          )
        }
        let status = response.status //标准状态码
        if (response.data.code && response.data.code == 1) {
          useMessage().error(response.data.msg)
        }
        if (status == 200) {
          if (response.data?.code !== 0) {
            return response.data
          } else {
            return response.data
          }
        }
      },
      async (err) => {
        if (err.response) {
          const { status, data } = err.response
          useMessage().error(data.msg || data.message)

          // if (status === 424) {
          //   router.push({ path: '/login', replace: true })
          // }
        } else {
          useMessage().error('网络异常或服务器错误')
        }

        return Promise.reject(err)
      }
    )

    this.instance = instance
  }
  // 自定义拦截器方法，如果需要在实例外部添加拦截器
  addInterceptor(requestInterceptor: any, responseInterceptor: any) {
    this.instance?.interceptors.request.use(requestInterceptor)
    this.instance?.interceptors.response.use(responseInterceptor)
  }
}

const request = new myRequset(import.meta.env.VITE_APP_AUTH).instance

export default request

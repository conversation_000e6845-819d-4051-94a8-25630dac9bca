import { $_getUserBaseInfoById } from '@/api/manager'
import { $_getUserDetail } from '@/api/auth'
import { $getMenuList } from '@/api/menu'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { useRouteStore } from '@/store/modules/route'

export default function initSysData() {
  const { setUserInfo } = useAppStore()

  if (localStorage.getItem('userInfo')) {
    let datas = JSON.parse(localStorage.getItem('userInfo') || '{}')
    getUserBaseInfo(datas.user_id)
    getUserDetail()
    setUserInfo(datas)
  }
}

// 获取用户的详细信息
const getUserDetail = () => {
  $_getUserDetail().then((res: any) => {
    if (res.code == 0) {
      localStorage.setItem('role', JSON.stringify(res.data.permissions))
      localStorage.setItem('areaCode', JSON.stringify(res.data.areaCode))
    }
  })
}

const getUserRoutes = async () => {
  const { setRoutes } = useRouteStore()
  let Routes: any[] = []
  let res: any = await $getMenuList()
  if (res.ok) {
    Routes = res.data
  }
  console.log("--------------------------", Routes);

  setRoutes(Routes)
  return Routes
}

// 获取用户基本信息
const getUserBaseInfo = (id: string) => {
  const { setUserName } = useAppStore()
  const { setUserBaseInfo } = useUserStore()
  $_getUserBaseInfoById(id).then((res: any) => {
    setUserBaseInfo(res.data)
    setUserName(res.data.name)
    localStorage.setItem(
      'isAdmin',
      JSON.stringify(
        res.data.roleList.findIndex((item: any) => item.roleId === '2')
      )
    )
  })
}

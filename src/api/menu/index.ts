import request from '@/utils/request'

//返回当前用户的树形菜单集合
export function $getMenuList(params?: any) {
  return request({
    method: 'get',
    url: '/admin/menu',
    params
  })
}

// 树形菜单合集
export function $getMenuTree() {
  return request({
    method: 'get',
    url: `/admin/menu/tree`,
  })
}

// 更新菜单
export function $putMenu(data: any) {
  return request({
    method: 'put',
    url: '/admin/menu',
    data
  })
}

// 新增菜单
export function $postMenu(data: any) {
  return request({
    method: 'post',
    url: '/admin/menu',
    data
  })
}

// 删除菜单
export function $deleteMenu(id: number) {
  return request({
    method: 'delete',
    url: `/admin/menu/${id}`
  })
}

//返回角色的菜单集合
export function $getRoleMenu(roleId: number) {
  return request({
    method: 'get',
    url: `/admin/menu/tree/${roleId}`
  })
}

// 更新角色的菜单
export const $_putRoleMenu = (data: any) => {
  return request({
      method: 'put',
      url: `/admin/role/menu`,
      data
  })
}


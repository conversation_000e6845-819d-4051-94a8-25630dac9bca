import request from '@/utils/request'

const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded'

//认证服务
export const $_autoLoginToken = (data: any) => {
  return request({
    method: 'post',
    url: '/auth/oauth2/token',
    data: Object.assign(data, {
      grant_type: 'password',
      client_id: 'cb_service_platform',
      client_secret: 'e4887ff09c3a95b818bf97eeda81afbb'
    }),
    headers: {
      'Content-Type': FORM_CONTENT_TYPE
    }
  })
}

//获取用户信息
export const $_getUserInfo = () => {
  return request({
    method: 'get',
    url: '/admin/user/info'
  })
}

//退出登录
export const $_logout = () => {
  return request({
    method: 'delete',
    url: '/auth/token/logout'
  })
}

import request from '@/utils/request'

// 返回树形菜单集合
export const $_getDeptMenuTree = (params: any) => {
  return request({
    method: 'get',
    url: '/admin/dept/tree',
    params
  })
}

// 添加部门
export const $_addDept = (data: any) => {
  return request({
    method: 'post',
    url: '/admin/dept',
    data
  })
}

// 更新部门
export const $_editDept = (data: any) => {
  return request({
    method: 'put',
    url: '/admin/dept',
    data
  })
}

// 删除部门
export const $_deleteDept = (id: any) => {
  return request({
    method: 'delete',
    url: `/admin/dept/${id}`
  })
}

// 新增角色
export const $_addRole = (data: any) => {
  return request({
    method: 'post',
    url: '/admin/role',
    data
  })
}

// 修改角色
export const $_editRole = (data: any) => {
  return request({
    method: 'put',
    url: '/admin/role',
    data
  })
}

// 分页查询角色信息
export const $_getRoleList = (params: any) => {
  return request({
    method: 'get',
    url: '/admin/role/page',
    params
  })
}

// 角色状态改变
export const $_changeRoleStatus = (data: any) => {
  return request({
    method: 'put',
    url: `/admin/role/changeStatus/${data.id}`,
    params: {
      status: data.type
    }
  })
}

// 分页查询 包含机构
export function $getDeptPage(type:any = 0, params:any) {
  return request({
    url: `/admin/sysArea/pageTreeByFullId/${type}`,
    params,
  });
}

//查询所有服务来源
export const $_getSysApiSourceList = () => {
  return request({
      url: '/market/apiSource/queryAllSource',
      method: 'get',
  })
}

// 获取获取机构性质
export function $getOrganizationType(type:any) {
  return request({
    url: `/admin/dict/type/${type}`,
    method: "get",
  });
}

// 删除角色
export const $_deleteRole = (data: any) => {
  return request({
    method: 'delete',
    url: `/admin/role`,
    data
  })
}

// 新增成员
export const $_addMember = (data: any) => {
  return request({
    method: 'post',
    url: '/admin/user',
    data
  })
}

// 分页查询用户
export const $_getMemberList = (params: any) => {
  return request({
    method: 'get',
    url: '/admin/user/page',
    params
  })
}

// 修改用户信息(管理员修改)
export const $_editMember = (data: any) => {
  return request({
    method: 'put',
    url: '/admin/user',
    data
  })
}

//重置密码
export const $_resetUserPassword = (data: any) => {
  return request({
    method: "post",
    url: `/admin/user/resetPassword`,
    data
  });
};

// 删除成员
export const $_deleteMember = (data: any) => {
  return request({
    method: 'delete',
    url: `/admin/user`,
    data
  })
}
import request from '@/utils/request'

export interface PromiseAllType {
  videoAccount: {
    grant: number
    offline: number
    online: number
    total: number
  }
  serviceInfo: {
    total: number
    todayUseNum: number
    successNum: number
    allNum: number
  }
}

// 获取首页数据
export const $_getHomeData = () => {
  // 视频相关接口
  const videoAccount = new Promise<Partial<PromiseAllType>>(
    (resolve, reject) => {
      request({
        method: 'get',
        url: `/video/device/total`,
        params: { grant: true }
      }).then((res: any) => {
        if (res.ok) {
          resolve({ videoAccount: res.data })
        } else {
          reject(res.msg)
        }
      })
    }
  )

  // 服务概况相关接口
  const serviceOverview = new Promise<Partial<PromiseAllType>>(
    async (resolve, reject) => {
      let res1 = await request({
        method: 'get',
        url: '/market/sysTenantApi/pageByPower',
        params: {
          current: 1,
          size: 1
        }
      })
      let res2 = await request({
        method: 'get',
        url: `/log/logManager/getThirdLogCountAndRate`
      })
      resolve({
        serviceInfo: { total: parseInt(res1.data.total, 10), ...res2.data }
      })
    }
  )
  return Promise.all([videoAccount, serviceOverview])
}

import request from '@/utils/request'

// 获取用户信息
export const $_getUserBaseInfoById = (id: any) => {
  return request({
    method: 'get',
    url: `/admin/user/details/${id}`
  })
}

// 修改当前用户信息
export const $_changeUserInfo = (data: any) => {
  return request({
    method: 'put',
    url: `/admin/user/personal/edit`,
    data
  })
}

// 获取appkey列表
export const $_getAppKeyList = (params: any) => {
  return request({
    method: 'get',
    url: `/admin/sysApp`,
    params
  })
}

// 新增appkey成员
export const $_addAppKey = (data: any) => {
  return request({
    method: 'post',
    url: `/admin/sysApp`,
    data
  })
}

//修改appkey状态
export const $_changeAppKeyStatus = (id: any, params: any) => {
  return request({
    method: 'post',
    url: `/admin/sysApp/${id}`,
    params
  })
}

// 删除appkey
export const $_deleteAppKey = (params: any) => {
  console.log('🚀 ~ $_deleteAppKey ~ params:', params)
  return request({
    method: 'delete',
    url: `/admin/sysApp`,
    params
  })
}

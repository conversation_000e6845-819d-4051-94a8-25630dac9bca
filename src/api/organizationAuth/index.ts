import request from '@/utils/request'

//分页查询组织授权列表
export function $queryOrganizationAuthList(data: any) {
    return request({
        method: "post",
        url: "/admin/organization/grant/page",
        data,
    });
}

// 订单合同分页查询
export function $queryOrderContractList(data: any) {
    return request({
        method: "post",
        url: "/admin/org/contract/page",
        data,
    });
}

// 获取服务授权列表
export function $queryOrganizationServiceAuthList(data: any) {
    return request({
        method: "post",
        url: "/market/sysTenantApi/pageByOrg",
        data,
    });
}

// 新建合同订单
export function $createOrderContract(data: any) {
    return request({
        method: "post",
        url: "/admin/org/contract",
        data,
    });
}

// 更新合同订单
export function $updateOrderContract(data: any) {
    return request({
        method: "put",
        url: "/admin/org/contract",
        data,
    });
}

// 查询组织机构自有设备数量
export function $queryOrganizationDeviceCount(params: any) {
    return request({
        method: "get",
        url: "/admin/org/self_device_num",
        params,
    });
}

// 更新组织机构自有设备
export function $updateOrganizationDevice(data: any) {
    return request({
        method: "put",
        url: "/admin/org/self_device_num",
        data,
    });
}

// 获取权限清单时间列表
export function $queryAuthTimeList(data: any) {
    return request({
        method: "post",
        url: "/admin/org/grant/detailedList",
        data,
    });
}
import request from '@/utils/request'
// const myReq = new myRequset(import.meta.env.VITE_APP_AUTH)
// const request = myReq.instance

//分页查询角色
export const $_getRoleData = (params) => {
  return request({
    method: 'get',
    url: '/admin/role/page',
    params
  })
}

//新增角色
export const $_postRole = (data) => {
  return request({
    method: 'post',
    url: '/admin/role',
    data
  })
}
//修改角色
export const $_putRole = (data) => {
  return request({
    method: 'put',
    url: '/admin/role',
    data
  })
}

//获取角色信息
export const $_getRoleDetails = (params) => {
  return request({
    method: 'get',
    url: '/admin/role/details',
    params
  })
}
//删除角色
export const $_delRoleData = (data) => {
  return request({
    method: 'delete',
    url: '/admin/role',
    data
  })
}

//启用/禁用角色，传false禁用，true启用
export const $_putRoleStatus = (id, params) => {
  return request({
    method: 'put',
    url: `/admin/role/changeStatus/${id}`,
    params
  })
}

//更新角色菜单
export const $_putRoleMenu = (data) => {
  return request({
    method: 'put',
    url: `/admin/role/menu`,
    data
  })
}

//返回当前机构的所有角色列表，用于授权
export const $_getRoleListByOrg = () => {
  return request({
    method: 'get',
    url: `/admin/role/list`
  })
}

//查询角色权限日志
export const $_getRoleLog = (params) => {
  return request({
    method: 'get',
    url: `/admin/useOrg/getRolePowerLogs`,
    params
  })
}

// 服务
import request from '@/utils/request'

//获取服务平台的服务类型列表
export function $getServiceTypeList() {
  return request({
    url: '/market/sysApi/getApiTypeListByApiIds',
    method: 'post'
  })
}

//获取已授权的API接口列表
export function $getAuthorizedApiList(data: any) {
  return request({
    url: '/market/sysTenantApi/pageByOrg',
    method: 'post',
    data
  })
}
// 查询算法详情
export function $getAlgorithmDetail(data: any) {
  return request({
    url: '/aicenter/ai/algorithm/queryByCode',
    method: 'post',
    data
  })
}

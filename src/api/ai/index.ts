import request from '@/utils/request'

// 新增算法任务
export function $addAlgorithmTask(data: any) {
  return request({
    url: `/aicenter/task/createCameraTask`,
    method: "post",
    data,
  });
}

// 算法任务详情 （算法任务）
export function $getAlgorithmTaskDetail(code: any) {
  return request({
    url: `/aicenter/task/getTaskDeviceList?taskIndexCode=${code}`,
    method: "get",
  });
}

// 启动算法分析 (算法任务)
export function $startAlgorithmAnalysis(data: any) {
  return request({
    url: `/aicenter/task/executeTask`,
    method: "post",
    data,
  });
}

// 查询算法任务规则
export function $getAlgorithmTaskRule(data:any) {
  return request({
    url: `/aicenter/task/queryAlgRule`,
    method: "post",
    data,
  });
}

// 分页查询算法管理已绑定通道列表 （算法管理）
export function $getAlgorithmChannelList(data: any) {
  return request({
    url: `/aicenter/ai/algorithm/queryChnByCode`,
    method: "post",
    data,
  });
}

// 分页查询算法 (算法服务)
export function $getAlgorithmServiceList(data:any) {
  return request({
    url: `/aicenter/ai/algorithm/page`,
    method: "post",
    data,
  });
}

// 查询算法详情 (算法任务)
export function $getAlgorithmTaskDetail1(data: any) {
  return request({
    url: `/aicenter/ai/algorithm/queryByCode`,
    method: "post",
    data,
  });
}

export const aiServerId = "1800879522075361281";

// 新增/更新时间计划 (算法任务)
export function $addOrEditTimePlan(data: any) {
  return request({
    url: `/aicenter/task/saveOrUpdateWeeklyPlan`,
    method: "post",
    data: {
      aiServerId,
      ...data,
    },
  });
}

// 删除时间计划 (算法任务)
export function $deleteTimePlan(id:any) {
  return request({
    url: `/aicenter/task/deleteWeeklyPlanByIds`,
    method: "post",
    data: {
      ids: [id],
    },
  });
}

//查询算法分析周计划
export function $getAlgorithmWeekPlan(data:any) {
  return request({
    url: `/aicenter/task/queryWeeklyPlans`,
    method: "post",
    data,
  });
}


//编辑智能算法分析任务
export function $editAlgorithmTask(data:any) {
  return request({
    url: `/aicenter/task/editTask`,
    method: "post",
    data,
  });
}

///task/queryByCameraIndexCode
export function $getAlgorithmTaskRelateAis(data:any) {
  return request({
    url: `/aicenter/task/queryByCameraIndexCode`,
    method: "post",
    data,
  });
}
import request from '@/utils/request'
// 通过id获取用户信息
export const $_getUserInfoById = (id: number) => {
  return request({
    method: 'get',
    url: `/admin/tenant/details/${id}`
  })
}

// 租户维护自己的基本信息
export const $_updateTenantInfo = (data: any) => {
  return request({
    method: 'PUT',
    url: `/admin/tenant`,
    data
  })
}

// 获取初始密码更改状态，用于判断前端是否需要弹窗提示用户修改初始密码，返回isChanged=false表示必需修改初始密码
export const $_getInitPasswordChangedStatus = (id?: number) => {
  return request({
    method: 'GET',
    url: `/admin/user/getInitPasswordStatus`
  })
}

// 修改当前用户的密码
export const $_changePassword = (data: any) => {
  return request({
    method: 'put',
    url: `/admin/user/personal/password`,
    data
  })
}

// 获取用户详细信息
export const $_getUserDetail = () => {
  return request({
    method: 'get',
    url: `/admin/user/info`
  })
}

// 获取服务列表 分页
export const $_getApiList = (params:any) => {
  return request({
    method: "get",
    url: `/market/sysTenantApi/page`,
    params,
  });
};

// /sysTenantApi/pageByRole 角色下的API接口列表分页查询
export const $_getApiListByRole = (data:any) => {
  return request({
    method: "post",
    url: `/market/sysTenantApi/pageByRole`,
    data,
  });
};

// （服务权限）添加服务平台用户的权限
export const $_addApiSourcePermission = (data:any) => {
  return request({
    method: "post",
    url: `/admin/useOrg/addPower/server`,
    data,
  });
};



import request from '@/utils/request'

// 获取消息类型
export const $_getMessageTypeList = () => {
  return request({
    method: 'get',
    url: '/message/account/queryChannelTypes',
  })
}

// 分页获取订阅消息
export const $_getMessageList = (data: any) => {
  return request({
    method: 'post',
    url: '/message/subscribe/page',
    data
  })
}

// 获取消息规则列表
export const $_getMessageRuleList = () => {
  return request({
    method: 'post',
    url: '/message/msgTemplate/page',
    data: {
      current: 1,
      size: 999
    }
  })
}

// 新增订阅消息
export const $_addMessageDiscription = (data: any) => {
  return request({
    method: 'post',
    url: '/message/subscribe/subscribeOne',
    data
  })
}

// 修改订阅消息状态
export const $_editMessageDiscriptionType = (params: any) => {
  return request({
    method: 'put',
    url: '/message/subscribe/updateStatusById',
    params
  })
}

// 删除订阅消息
export const $_deleteMessageDiscription = (id: any) => {
  return request({
    method: 'delete',
    url: `/message/subscribe/deleteBySubscribeId?subscribeId=${id}`,
  })
}


// 获取设备列表
export const $_getDeviceList = (params: any) => {
  return request({
    method: 'get',
    url: '/video/device/brief/page',
    params
  })
}

export function $getStreamNodeList1(params:any) {
  return request({
    url: `/video/stream_node/page`,
    method: "get",
    params,
  });
}

// 获取设备类型
export function $getDeviceTypeList() {
  return request({
    url: `/video/device/categories`,
    method: "get",
  });
}

//获取消息类型列表
export function $queryMsgTypeList() {
  return request({
      method: "get",
      url: "/message/msgTemplate/queryTemplateTypes",    
  });
}

//分页查询接收人消息
export function $queryReceiveMsgList(data:any) {
  return request({
      method: "post",
      url: "/message/msgRecord/findRecordReceiverPage", 
      data,
  });
}

//删除接收人消息
export function $deleteReceiveMsg(data:any) {
  return request({
      method: "post",
      url: "/message/msgRecord/deleteMsgRecordReceiver",
      data,
  });
}

//标记为已读
export function $markAsRead(data:any) {
  return request({
      method: "post", 
      url: "/message/msgRecord/readMsg",
      data,
  });
}

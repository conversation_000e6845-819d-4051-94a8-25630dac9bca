import request from '@/utils/request'
import requestcs from '@/utils/requestcs'

// 创建工单
export const $createOrder = (data: any) => {
  return request({
    url: '/contract-order/work-order/create',
    method: 'post',
    data
  })
}
// 查询我创建的工单列表
export const $getOrderList = (params: any) => {
  return request({
    url: '/contract-order/work-order/created/list',
    method: 'get',
    params
  })
}
// 根据id查询工单详情
export const $getOrderDetail = (id: any) => {
  return request({
    url: `/contract-order/work-order/detail/${id}`,
    method: 'get'
  })
}
// 催单
export const $urgeOrder = (data: any) => {
  return request({
    url: `/contract-order/work-order/hasten`,
    method: 'post',
    data
  })
}
// 查询工单处理记录
export const $getOrderLog = (id: any) => {
  return request({
    url: `/contract-order/work-order/record/list/${id}`,
    method: 'get'
  })
}
// 审核
export const $auditOrder = (data: any) => {
  return request({
    url: `/contract-order/work-order/audit`,
    method: 'post',
    data
  })
}
// 查询我的工单列表
export const $getMyOrderList = (params: any) => {
  return request({
    url: '/contract-order/work-order/list',
    method: 'get',
    params
  })
}
// 转单
export const $transferOrder = (data: any) => {
  return request({
    url: `/contract-order/work-order/transfer`,
    method: 'post',
    data
  })
}
// 接单
export const $acceptOrder = (id: any) => {
  return request({
    url: `/contract-order/work-order/accept/${id}`,
    method: 'post'
  })
}
// 处理
export const $dealOrder = (data: any) => {
  return request({
    url: `/contract-order/work-order/process`,
    method: 'post',
    data
  })
}
export const $upLoadFile = (data: any) => {
  return request({
    url: '/admin/sys-file/upload',
    method: 'post',
    data
  })
}
// 工单最新处理数据
export const $getOrderLastData = (id: any) => {
  return request({
    url: `/contract-order/work-order/record/process/${id}`,
    method: 'get'
  })
}
// 申请平台授权
export const $applyAuth = (data: any) => {
  return requestcs({
    url: `/PlatformAuthorization/add`,
    method: 'post',
    data
  })
}

// 视频汇聚中心
import request from '@/utils/request'

// 获取树目录集合
export function $getVideoTreeList() {
  return request({
    url: `/video/catalog/tree`,
    method: 'get'
  })
}

// 懒加载树目录
export function $getVideoTreeListLazy(id: any) {
  return request({
    url: `/video/catalog/tree?parentId=${id}`,
    method: 'get'
  })
}

// 获取目录id查询通道列表
export function $getVideoDeviceListByid(id: any) {
  return request({
    url: `/video/video_device_chn/list_catalog/${id}`,
    method: 'get'
  })
}

// 节点管理表格分页数据
export function $getVideoNodePageList(parmas: any) {
  return request({
    url: `/video/stream_node/page?current=${parmas.current}&size=${parmas.size}`,
    method: 'get'
  })
}

// 查询节点列表
export function $getVideoNodeList() {
  return request({
    url: `/video/stream_node/list`,
    method: 'get'
  })
}

// 启用/停用节点
export function $updateVideoNodeStatus(parmas: any) {
  return request({
    url: `/video/stream_node/active?id=${parmas.id}&active=${parmas.active}`,
    method: 'put'
  })
}

// 设备目录管理查询树
export function $getVideoDeviceTree() {
  return request({
    url: `/video/market/catalog/tree`,
    method: 'get'
  })
}

// 通过流节点ID获取配置信息
export function $getNodeGBConfigById(id: any) {
  return request({
    url: `/video/gb28181_cfg/${id}`,
    method: 'get'
  })
}
// 修改流节点国标配置
export function $updateNodeGBConfig(parmas: any) {
  /**
   *
   * {
  "id": 0,
  "nodeId": 0,
  "sipIp": "string",
  "sipPort": 0,
  "sipPassword": "string",
  "gbId": "string",
  "gbDomain": "string",
  "transportProtocol": "string",
  "tcpPortStart": 0,
  "tcpPortEnd": 0,
  "udpPortStart": 0,
  "udpPortEnd": 0,
  "offlineNotify": "string"
}
   */
  return request({
    url: `/video/gb28181_cfg/edit`,
    method: 'put',
    data: parmas
  })
}
// 分页查询国标数据
export function $getNationalStandardList(parmas: any) {
  return request({
    url: `/video/video_device/page?current=${parmas.current}&size=${parmas.size}&nodeId=${parmas.nodeId}`,
    method: 'get'
  })
}

// 添加国标下级平台
export function $getNationalStandardDetail(data: any) {
  return request({
    url: `/video/video_device/add`,
    method: 'post',
    data: data
  })
}

// 删除下级国标平台
export function $getNationalStandardUpdate(id: any) {
  return request({
    url: `/video/video_device/gb_platform/${id}`,
    method: 'delete'
  })
}

// 修改国标下级平台信息
export function $getNationalStandardEdit(data: any) {
  return request({
    url: `/video/video_device/gb_platform`,
    method: 'put',
    data: data
  })
}

// 节点管理下 添加流节点
export function $addVideoNode(data: any) {
  return request({
    url: `/video/stream_node/add`,
    method: 'post',
    data
  })
}

// 删除流节点
export function $deleteVideoNode(ids: any) {
  return request({
    url: `/video/stream_node/delete`,
    method: 'delete',
    data: ids
  })
}

// 获取下级平台的设备列表
export function $getNationalStandardDeviceList(id: any) {
  return request({
    url: `/video/video_device/node/list/${id}`,
    method: 'get'
  })
}

// 添加国标平台的视频通道
export function $addNationalStandardDevice(data: any) {
  return request({
    url: `/video/video_device/node/device/channel`,
    method: 'post',
    data: data
  })
}

// 修改流节点 /stream_node/edit
export function $updateVideoNode(data: any) {
  return request({
    url: `/video/stream_node/edit/${data.id}`,
    method: 'put',
    data
  })
}

// 通过 id 查询通道列表
export function $getVideoChannelListByid(data: any) {
  return request({
    url: `/video/video_device_chn/page`,
    // url: `/video_device_chn/page`,
    method: 'post',
    data
  })
}

// 设备管理 删除通道
export function $deleteVideoChannel(data: any) {
  let arr = []
  if (!!data.length) {
    arr = data.map((it: any) => it.id)
  } else {
    arr.push(data.id)
  }
  return request({
    url: `/video/video_device_chn/delete`,
    method: 'delete',
    data: arr
  })
}

// 通过国标id 获取通道数据、
export function $getVideoChannelDetail(data: any) {
  return request({
    url: `/video/video_device_chn/list_gb28181`,
    method: 'post',
    data
  })
}

// 移动通道到某一目录
export function $moveVideoChannel(data: any) {
  return request({
    url: `/video/video_device_chn/move?catalogId=${data.catalogId}`,
    method: 'post',
    data: data.channelIds
  })
}

//下拉框设备类型
export function $getDeviceTypeList() {
  return request({
    url: `/video/device/categories`,
    method: 'get'
  })
}

//下拉框设备品牌
export function $getDeviceBrandsList() {
  return request({
    url: `/video/device/brands`,
    method: 'get'
  })
}

//下拉位点选择
export function $getDevicePointList(params: any) {
  return request({
    url: `/video/site/condition/page`,
    method: 'get',
    params
  })
}

//设备权限
export function $getDevicePermissionList() {
  return request({
    url: `/admin/dict/type/use_perm`,
    method: 'get'
  })
}

//分页查询摄像机管理列表
export function $getCameraList(data: any) {
  return request({
    url: `/video/video_device_chn/page`,
    method: 'POST',
    data
  })
}

//添加摄像机
export function $addCamera(data: any) {
  return request({
    url: '/video/device',
    method: 'post',
    data
  })
}
//查询摄像机详情
export function $getCameraDetail(params: any) {
  return request({
    url: `/video/device/detail`,
    method: 'get',
    params
  })
}

//编辑 -查询摄像机详情
export function $editCameraInfo(params: any) {
  return request({
    url: `/video/device/info`,
    method: 'get',
    params
  })
}

// 配置视频资源权限 （ai视频资源管理）
export function $getAiVideoResourceList(params: any, data: any) {
  return request({
    url: `/video/video_device_chn/config_authority`,
    method: 'post',
    params,
    data
  })
}

// 摄像机管理 下拉摄像机类型
export function $getCameraType() {
  return request({
    url: `/video/device/categories`,
    method: 'get'
  })
}

// 批量更新摄像机信息
export function $batchUpdateCamera(data: any) {
  return request({
    url: `/video/device/device/batch`,
    method: 'post',
    data
  })
}

// 查询厂家协议
export function $getManufacturerProtocol(params: any) {
  return request({
    url: `/video/device_protocol/list`,
    method: 'get',
    params
  })
}

// 视频资源适用算法
export function $getAlgorithmList1(data: any) {
  return request({
    url: `/video/video_device_chn/config_algorithm`,
    method: 'post',
    data
  })
}

//摄像机基本档案
export function $getCameraBasicInfo(params: any) {
  return request({
    url: `/video/device/archives`,
    method: 'get',
    params
  })
}

//下拉获取网络服务商
export function $getNetworkServiceList() {
  return request({
    url: `/admin/dict/type/net_provider`,
    method: 'get'
  })
}

//下拉获取传输协议
export function $getTransportProtocol() {
  return request({
    url: `/admin/dict/type/transport_protocol`,
    method: 'get'
  })
}

//删除摄像机
export function $deleteCamera(data: any) {
  return request({
    url: `/video/device`,
    method: 'delete',
    data
  })
}

//查看已绑定算法
export function $getAlgorithmList(params: any) {
  return request({
    url: `/video/device/algorithms`,
    method: 'get',
    params
  })
}

// 通过国标id获取事件列表
export function $getEventListByGbid(data: any) {
  return request({
    url: `/aicenter/event/page`,
    method: 'post',
    data
  })
}

// 获取厂商协议
export function $getDeviceProtocol() {
  return request({
    url: `/video/device/protocols`,
    method: 'get'
  })
}

//配置位点
export function $getConfigPosition(data: any) {
  return request({
    url: '/video/device/site',
    method: 'put',
    data
  })
}

// 获取待分配摄像机列表
export function $getCameraListByStatus(params: any) {
  return request({
    url: `/video/video_device_chn/get_camera_wait_assign`,
    method: 'get',
    params
  })
}

// 获取节点下已分配摄像机列表
export function $getCameraListByNode(params: any) {
  return request({
    url: `/video/stream_node/device/assigned`,
    method: 'get',
    params
  })
}

//摄像机导出
export function $exportCamera(params: any) {
  return request({
    url: `/video/device/export`,
    method: 'get',
    params
  })
}

//摄像机导入
export function $importCamera(params: any) {
  return request({
    url: `/video/device/import`,
    method: 'post',
    params
  })
}

//修改摄像机
export function $updateCamera(data: any) {
  return request({
    url: `/video/device`,
    method: 'put',
    data
  })
}

// 修改 基本档案
export function $updateCameraBasicInfo(data: any) {
  return request({
    url: `/video/device/archives`,
    method: 'put',
    data
  })
}

//获取已关联的设备通道列表(查看说明)
export function $getRelatedChannelList(params: any) {
  return request({
    url: `/video/video_device/channels`,
    method: 'get',
    params
  })
}

//通过摄像机id获取详情
export function $getCameraDetailById(params: any) {
  return request({
    url: `/video/video_device_chn/get_camera_detail`,
    method: 'get',
    params
  })
}

//视频在线离线统计数
export function $getOnlineOfflineCount() {
  return request({
    url: `/video/device/total`,
    method: 'get'
  })
}
// 获取视频列表 (算法任务 新增视频)
export function $getAlgorithmVideoList(parmas: any) {
  return request({
    url: `/video/device/assignable/chn/page?current=${parmas.current}&size=${
      parmas.size
    }&keyword=${parmas.keyword || ''}&gbIds=${parmas.gbIds || []}`,
    method: 'get'
  })
}

//多网域分页查询
export function $getDomainPageList(params: any) {
  return request({
    url: `/video/domain_config/page`,
    method: 'get',
    params
  })
}

//多网域下拉框
export function $getDomainList(params: any) {
  return request({
    url: `/video/domain_config/all`,
    method: 'get',
    params
  })
}

//多网域详情
export function $getDomainDetail(path: any) {
  return request({
    url: `/video/domain_config/detail/${path}`,
    method: 'get'
  })
}

//新增网域
export function $addDomain(data: any) {
  return request({
    url: `/video/domain_config`,
    method: 'post',
    data
  })
}

//修改网域
export function $updateDomain(data: any) {
  return request({
    url: `/video/domain_config`,
    method: 'put',
    data
  })
}

//删除网域
export function $deleteDomain(path: any) {
  return request({
    url: `/video/domain_config/${path}`,
    method: 'delete'
  })
}

//接入管理点击-> 更新下级设备通道并返回目录信息
export function $updateSubDeviceChannel(params: any) {
  return request({
    url: `/video/video_device/access`,
    method: 'put',
    params
  })
}

// 获取国标上级平台 (分页)
export function $getVideoDeviceListByNode(params: any) {
  return request({
    url: `/video/gbPlatform/page`,
    method: 'get',
    params
  })
}

// 添加国标上级平台
export function $addGbUp(data: any) {
  return request({
    url: `/video/gbPlatform`,
    method: 'post',
    data
  })
}

// 修改国标上级平台
export function $updateGbUp(data: any) {
  return request({
    url: `/video/gbPlatform`,
    method: 'put',
    data
  })
}

// 查询上级平台
export function $getUpPlatformList(params: any) {
  return request({
    url: `/video/gbPlatform/detail`,
    method: 'get',
    params
  })
}

// 删除国标上级
export function $delUpGbById(data: any) {
  return request({
    url: `/video/gbPlatform/${data.id}`,
    method: 'delete'
  })
}

// 获取国标上级平台关联的视频节点
export function $getGbUpVideoListByNode(params: any) {
  return request({
    url: `/video/gbPlatform/video/page`,
    method: 'get',
    params
  })
}

// 接入管理获取下级平台的设备通道列表
export function $getSubDeviceChannelList(params: any) {
  return request({
    url: `/video/video_device/channels`,
    method: 'get',
    params
  })
}

// 国标上级添加摄像机
export function $getGbUpVideoListByPlatform(data: any) {
  return request({
    url: `/video/gbPlatform/bind`,
    method: 'put',
    data
  })
}

// 国标上级更新目录
export function $updateDirectory(data: any) {
  return request({
    url: `/video/gbPlatform/catalog`,
    method: 'put',
    data
  })
}

// 获取目录 (国标上级)
export function $getDirectory(params: any) {
  return request({
    url: `/video/gbPlatform/catalog`,
    method: 'get',
    params
  })
}

// 开启授权 or 关闭授权
export function $updateAuth(params: any) {
  return request({
    url: `/video/gbPlatform/auth`,
    method: 'put',
    params
  })
}

// 批量视频编码ID
export function $getVideoEncodeList(data: any) {
  return request({
    url: `/video/gbPlatform/batch/setting_code`,
    method: 'put',
    data
  })
}

// 单个设置视频编码ID
export function $setVideoEncode(data: any) {
  return request({
    url: `/video/gbPlatform/single/setting_code`,
    method: 'put',
    data
  })
}

// 注册或注销服务(上级平台)
export function $registerOrUnregister(params: any) {
  return request({
    url: `/video/gbPlatform/register`,
    method: 'put',
    params
  })
}

// （视频权限）添加服务平台用户的权限
export function $addUserAuthority(data: any) {
  return request({
    url: `/admin/useOrg/addPower/video`,
    method: 'post',
    data
  })
}

//共享设备自动分配节点
export function $autoAssignNode(data: any) {
  return request({
    url: `/video/video_device/share`,
    method: 'put',
    data
  })
}

// 获取国标目录
export function $getVideoDeviceListByPlatform(params: any) {
  return request({
    url: `/video/gbPlatform/catalog/tree`,
    method: 'get',
    params
  })
}

// 更新 / 新增目录
export function $updateCatalog(data: any) {
  return request({
    url: `/video/gbPlatform/catalog`,
    method: 'put',
    data
  })
}

// 删除目录
export function $deleteCatalog(id: any) {
  return request({
    url: `/video/gbPlatform/catalog/${id}`,
    method: 'delete'
  })
}

// 调试接口
export function $debugApi(url: any, method = 'get', params: any, data: any) {
  return request({
    url: url,
    method,
    params,
    data
  })
}

// 获取视频的播放地址 /device/playAddress/{sn}
export function $getVideoPlayUrl(params: any) {
  return request({
    url: `/video/device/playAddress/${params.sn}`,
    method: 'get'
  })
}

import request from '@/utils/request'
//创建摄像头算法分析任务（多摄像头-多任务）
export function $addTask(data: any) {
  return request({
    url: `/aicenter/task/mutiCreateCameraTask`,
    method: 'post',
    data
  })
}
//编辑智能算法分析任务
export function $editAlgorithmTask(data: any) {
  return request({
    url: `/aicenter/task/mutiEditTask`,
    method: 'post',
    data
  })
}
// 算法任务列表数据(算法任务)
export function $getTaskList(data: any) {
  return request({
    url: `/aicenter/task/page`,
    method: 'post',
    data
  })
}
// 分页查询指定算法任务下的子任务
export function $getSubTaskList(data: any) {
  return request({
    url: `/aicenter/task/pageSubTask`,
    method: 'post',
    data
  })
}
// 分页查询算法 (算法服务)
export function $getAlgorithmServiceList(data: any) {
  return request({
    url: `/aicenter/ai/algorithm/page`,
    method: 'post',
    data
  })
}
// 启动算法分析 (算法任务)
export function $startAlgorithmAnalysis(data: any) {
  return request({
    url: `/aicenter/task/executeTask`,
    method: 'post',
    data
  })
}

// 停止算法分析 (算法任务)
export function $stopAlgorithmAnalysis(ids: any) {
  return request({
    url: `/aicenter/task/stopTask`,
    method: 'post',
    data: {
      taskIndexCodes: ids
    }
  })
}
//根据筛选参数分页获取事件记录（事件中心）
export function $getEventList(data: any) {
  return request({
    url: `/aicenter/event/page`,
    method: 'post',
    data
  })
}

// 组织管理下 行政 和 机构管理 共用的接口
import request from '@/utils/request'
// 获取区域列表(表格)
export function $getOrganizationList(params: any) {
  return request({
    url: '/admin/sysArea/tree',
    method: 'get',
    params
  })
}

// 获取区域列表(树形)
export function $getOrganizationTree(params: any) {
  return request({
    url: '/admin/sysArea/pageTree',
    method: 'get',
    params
  })
}

// 新增行政区域 /sysArea
export function $addOrganization(data: any) {
  console.log(data)
  return request({
    url: '/admin/sysArea',
    method: 'post',
    data
  })
}

// 获取获取机构性质
export function $getOrganizationType(type: any) {
  return request({
    url: `/admin/dict/type/${type}`,
    method: 'get'
  })
}

// 修改行政区域 /sysArea
export function $editOrganization(data: any) {
  return request({
    url: '/admin/sysArea',
    method: 'put',
    data
  })
}

// 删除区域 根据id
export function $deleteOrganization(data: any) {
  return request({
    url: `/admin/sysArea`,
    method: 'delete',
    data
  })
}

//分页查询（树结构新，包含机构）
export function $getOrganizationPageTree(path: any, params: any) {
  return request({
    url: `/admin/sysArea/pageTreeByFullId/${path}`,
    method: 'get',
    params
  })
}

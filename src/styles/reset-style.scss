body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
}
* {
  box-sizing: border-box;
}
*::before,
*::after {
  box-sizing: border-box;
}
a:focus,
a:active {
  outline: none;
}
a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1;
  font-weight: 400;
  margin: 0;
  padding: 0;
}
// span,
// output {
//   display: inline-block;
//   line-height: 1;
// }

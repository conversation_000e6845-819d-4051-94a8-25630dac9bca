@import url('./elemenet-style-overflow.scss');
@import url('./transition.scss');
@import url('./scss-suger.scss');
@import url('./reset-style.scss');

//scroll
@mixin main-show-wh() {
  /* css 声明 */
  height: calc(
    100vh - #{$navBarHeight} - #{$tagViewHeight} - #{$appMainPadding * 2}
  );
  width: 100%;
}

.scroll-y {
  @include main-show-wh();
  overflow-y: auto;
}

.scroll-x {
  @include main-show-wh();
  overflow-x: auto;
}

.scroll-xy {
  @include main-show-wh();
  overflow: auto;
}

// 盒子阴影
.box-sha {
  position: relative;
  background-color: #fff;
  z-index: 9;
  padding: 0 !important;
  margin-bottom: 15px;
  border: 0 !important;
}

.sha-dow {
  position: absolute;
  width: 102%;
  height: 101%;
  top: -2px;
  left: -2px;
  padding: 0 !important;
  filter: blur(3px);
  // bottom: -2px;
  // right: -2px;
  background: rgba(57, 137, 255, 0.14);
  z-index: -1;
  border-radius: 12px;
}

.van-toast--text {
  color: #999 !important;
}

.vue-pdf-embed canvas {
  width: 100% !important;
  height: 100% !important;
}

// el 弹窗
.el-dialog {
  padding: 0 !important;
}

.el-dialog__header {
  padding: 12px 24px !important;
}

.el-dialog__footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 0 !important;
}

ul {
  margin: 0 !important;
  padding: 0 !important;
}

html,
body {
  width: 100%;
  height: 100%;
}

// #app {
//   width: 100vw;
//   height: 100vh;
//   position: fixed;
//   top: 0;
//   left: 0;
//   right: 0;
//   bottom: 0;
// }

.hide-scrollbar {
  -ms-overflow-style: none;
  /* IE 和 Edge */
  scrollbar-width: none;
  /* Firefox */

  &::-webkit-scrollbar {
    /* WebKit (Safari 和 Chrome) */
    width: 0;
    height: 0;
  }

  overflow: -moz-scrollbars-none;
  /* Older Firefox browsers */
  overflow-y: scroll;
  /* 保持内容可滚动 */
}

.borr {
  &::after {
    content: '';
    position: absolute;
    height: 8px;
    width: 1px;
    right: -1px;
    top: 50%;
    border-right: 1px solid #cccccc;
    transform: translateY(-50%);
  }
}

.containerBox {
  @apply w-full h-full rounded p-[16px] box-border;
}

// 状态正常颜色
.normal {
  color: #00db58;
}

// 状态异常颜色
.abnormal {
  color: #ff604f;
}

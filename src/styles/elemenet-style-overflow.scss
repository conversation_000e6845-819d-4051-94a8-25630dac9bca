// cover some element-ui styles
// element plus 颜色重置
:root {
  // primary color
  // 默认颜色
  --el-color-primary: #3665ff;
  // 经过颜色
  --el-color-primary-light-3: #6387ff;
  // 点击颜色
  --el-color-primary-dark-2: #2247c0;
  // 禁用颜色

  // --el-text-color-primary: #E8E8E8;

  // defalut color
  --el-color-primary-light-9: #ffffff;
  --el-color-primary-light-7: #3665ff;
  // --el-fill-color-blank: #E3E3E3;

  // error color
  --el-color-light-5: #e3e3e3;
  --el-color-danger: #ff6565;
}
.el-button {
  // --el-button-active-bg-color: #EFF3FF !important;
  --el-button-disabled-bg-color: #e3e3e3 !important;
}
.el-drawer.ltr, .el-drawer.rtl{
  top:80px !important;
  height: calc(100% - 80px) !important;
}
.el-button.el-button--primary {
  background: #3665ff !important;
  color: #fff !important;
}

.el-tag {
  background-color: #3665ff !important;
  color: #fff !important;
}

.el-radio__input.is-checked .el-radio__inner {
  background-color: #fff !important;
}
.el-radio__inner:after {
  width: 8px !important;
  height: 8px !important;
  background-color: #3665ff !important;
}
.el-radio__input.is-disabled.is-checked .el-radio__inner:after {
  background-color: #b4b4b4 !important;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
//.el-dropdown-menu {
//  a {
//    display: block
//  }
//}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

/*
 element-ui 样式重置
*/
.elODialogModal {
  .el-dialog {
    margin-top: 7vh !important;
  }

  .el-dialog__header {
    border-bottom: 1px solid #ddd;
  }

  .el-dialog__body {
    padding-top: 10px;
    overflow-y: auto;
  }
}

.elODialogModalBodyH60vh {
  .el-dialog__body {
    min-height: 50vh;
    max-height: 65vh;
  }
}

.elODialogModalBodyH70vh {
  .el-dialog__body {
    min-height: 60vh;
    max-height: 70vh;
  }
}

.elODialogModalBodyH40vh {
  .el-dialog__body {
    min-height: 40vh;
    max-height: 55vh;
  }
}

.elODialogModalBodyW80vh {
  .el-dialog__body {
    width: 80vh;
  }
}

.elFormItemMarginB0px {
  .el-form-item {
    margin-bottom: 12px !important;
  }
}

// h5样式
.formBox {
  .el-input__wrapper {
    border: none !important;
    box-shadow: none !important;
  }

  .el-input__inner {
    text-align: right;
  }

  .el-form-item {
    margin: 0;
  }

  .el-select {
    flex: 1;

    .el-input__wrapper.is-focus {
      box-shadow: none !important;
    }

    .el-input.is-focus {
      .el-input__wrapper {
        box-shadow: none !important;
      }
    }
  }

  .el-textarea__inner {
    text-align: right;
    background-color: #f8f8f9;
    border: none;
  }

  .el-textarea {
    --el-input-hover-border-color: none;
    --el-input-focus-border-color: none;
  }
}

.unFlex.el-form-item {
  display: block;
}

.el-dialog--center .el-dialog__body {
  padding: 10px;
}

.formBox {
  .el-upload {
    flex: 1;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #409eff;
    }
  }
  .el-upload-list {
    width: 50vw;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 78px;
    height: 78px;
    line-height: 78px;
    text-align: center;
  }
}
.el-message {
  padding: 12px 8px;
}
.elMessageBox {
  min-width: calc(100% - 80px) !important;
  display: flex;
  justify-content: center;
}

.el-select__selected-item {
  .el-tag {
    background-color: #3665ff !important;
    color: #fff !important;
    border-radius: 2px !important;
    .el-tag__close {
      color: #fff !important;
    }
  }
}

// .darkk {
// .el-dropdown-menu.el-dropdown-menu--default{
//   background-color: #2E3946;
// }

// .el-popper.is-light .el-popper__arrow::before{
//   background-color: #2E3946;
// }
// }

.videoCenter-dropdown {
  background: #232630 !important;
  border: none !important;
  .el-dropdown-menu {
    background-color: transparent !important;
    border: none !important;
  }
  .el-popper__arrow::before {
    background-color: #232630 !important;
    border: none !important;
  }
  .el-scrollbar {
  }
  .el-dropdown-menu__item:not(.is-disabled):focus {
    background-color: #232630 !important;
    border: none !important;
    color: #fff !important;
  }
  .is-active {
    color: #3665ff !important;
  }
}

.isUnder:after {
  border-bottom: 1px solid #3665ff !important;
  content: '';
  position: absolute;
  height: 1px;
  bottom: 0;
  width: 16px;
  left: 50%;
  transform: translateX(-50%);
}
// .el-select__input-wrapper {
//   width: 100% !important;
// .el-select__input {
//   width: 100% !important;
// }
// }

.men {
  top: 75px !important;
}

.el-tree-node__content {
  height: 32px !important;
  display: relative;
}

.el-drawer__header {
  margin: 0 !important
}


// 登陆相关
import router from '@/router/index'
import { Local } from '@/utils/storage'
import { encrypt, decrypt } from '@/utils/enc'
import {
  $_getInitPasswordChangedStatus,
  $_changePassword,
  $_getUserDetail
} from '@/api/auth'
import { $getMenuList } from '@/api/menu'
import { $_getUserBaseInfoById } from '@/api/manager'
import { useAppStore } from '@/store/modules/app'
import { useRouteStore } from '@/store/modules/route'
import { useUserStore } from '@/store/modules/user'
import { success } from '@/utils/toast'
import { $_autoLoginToken, $_logout } from '@/api/login'
import { FormInstance } from 'element-plus/es/components/form/index.mjs'

export const useLogin = (data?: any) => {
  const { setUserInfoState, setRoutes } = useRouteStore()

  const { setUserInfo, setUserName, setNeedModifyPassword } = useAppStore()

  const { setUserBaseInfo } = useUserStore()
  // 登陆表单实例
  const loginFormRef = ref<FormInstance | undefined>(undefined)

  // 修改密码实例
  const dialogViewRef: any = ref(null)

  interface loginFormType {
    username: string
    password: string
    scope: string
  }

  // 表单数据
  const loginForm = ref<loginFormType>({
    username: '',
    password: '',
    scope: 'server'
  })

  // 记住密码
  const remberPass = ref(false)

  // 观察是否记住密码
  watch(remberPass, (val) => {
    if (!val) {
      Local.remove('rember')
      Local.remove('username')
      Local.remove('password')
    }
  })

  // 存密码
  const remberPassEve = () => {
    if (remberPass.value) {
      Local.set('rember', remberPass.value)
      Local.set('serverPlatformUsername', loginForm.value?.username)
      Local.set(
        'serverPlatformPassword',
        encrypt(loginForm.value!.password?.toString())
      )
    }
  }

  // 登陆按钮加载状态
  const loginLoading = ref(false)

  // 登陆验证规则
  const loginRules = {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
  }

  // 登陆事件
  const loginEve = () => {
    loginFormRef?.value?.validate((valid: any) => {
      if (valid) {
        // 记住密码？
        // 验证通过 显示加载状态
        loginLoading.value = true
        // 验证通过 加密密码
        const passWord = encrypt(loginForm.value!.password.toString())
        // 调用登录接口
        $_autoLoginToken({ ...loginForm.value, password: passWord })
          .then((res: any) => {
            getUserBaseInfo(res.user_id)
            // 获取用户基本信息
            remberPassEve()
            // 登录成功
            localStorage.setItem('token', res.access_token)
            Local.set('refresh_token', res.refresh_token)
            getUserDetail()
            // localStorage.setItem('userInfo', JSON.stringify(res))
            setUserInfo(res)
            // router.push('/')
            // 检查是否需要修改密码才能登陆
            checkPass()
          })
          .finally(() => {
            // 隐藏加载状态
            loginLoading.value = false
          })
      } else {
        // 验证失败
        console.log(valid)
        console.log('error submit!!')
      }
    })
  }

  // 获取用户基本信息
  const getUserBaseInfo = (id: string) => {
    $_getUserBaseInfoById(id).then((res: any) => {
      setUserBaseInfo(res.data)
      setUserName(res.data.name)
      localStorage.setItem(
        'isAdmin',
        JSON.stringify(
          res.data.roleList.findIndex((item: any) => item.roleId === '2')
        )
      )
    })
  }

  // 获取用户的详细信息
  const getUserDetail = () => {
    $_getUserDetail().then((res: any) => {
      if (res.code == 0) {
        localStorage.setItem('role', JSON.stringify(res.data.permissions))
        localStorage.setItem('areaCode', JSON.stringify(res.data.areaCode))
      }
    })
  }

  // 修改密码表单实例
  const passFormRef = ref<any>(null)

  // 修改密码表单数据
  const passForm = ref<{
    password: string
    newpassword: string
    confirmpassword: string
    [key: string]: any
  }>({
    password: '',
    newpassword: '',
    confirmpassword: ''
  })

  // 确认密码验证规则
  const passRules = {
    password: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
    newpassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      {
        min: 8,
        max: 16,
        message: '密码长度在 8 到 16 个字符',
        trigger: 'blur'
      },
      {
        validator: (rule: any, value: any, callback: (v?: any) => {}) => {
          const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z]).{8,16}$/
          if (!passwordRegex.test(value)) {
            callback(new Error('密码须包含一个小写字母和一个大写字母'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    confirmpassword: [
      { required: true, message: '请输入确认密码', trigger: 'blur' },
      {
        min: 8,
        max: 16,
        message: '密码长度在 8 到 16 个字符',
        trigger: 'blur'
      },
      {
        validator: (rule: any, value: any, callback: (v?: any) => {}) => {
          if (value !== passForm.value.newpassword) {
            callback('两次输入的密码不一致！')
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 是否需要修改
  const isChanged = ref(false)

  // 获取路由菜单列表
  const getUserRoutes = async () => {
    let Routes: any[] = []
    let res: any = await $getMenuList()
    if (res.ok) {
      Routes = res.data
    }
    setRoutes(Routes)
    return Routes
  }

  // 检查是否需要修改密码
  const checkPass = () => {
    $_getInitPasswordChangedStatus().then(async (res) => {
      isChanged.value = res.data.isChanged
      setUserName(res.data.name)
      setNeedModifyPassword(isChanged.value)
      if (!isChanged.value) {
        dialogViewRef.value.open()
      }
      else {
        let Routes = await getUserRoutes()
        if (Routes?.length > 0) {
          router.push('/')
        }
      }
    })
  }

  // 确定修改密码
  const changePassEve = (el: any = null) => {
    if (el) {
      el.loading = true
    } else {
      dialogViewRef.value.loading = true
    }
    passFormRef.value
      .validate()
      .then(() => {
        console.log('验证通过')
        changePassReq(el)
      })
      .catch(() => {
        console.log('验证失败')
        if (el) {
          el.loading = false
        } else {
          dialogViewRef.value.loading = false
        }
      })
  }

  // 修改密码请求
  const changePassReq = (el: any) => {
    passForm.value.username = loginForm.value?.username
    $_changePassword(passForm.value)
      .then((res: any) => {
        if (res.code == 0) {
          success('修改密码成功, 请重新登陆！')
          dialogViewRef.value.close()
          router.push('/login')
        } else {
        }
      })
      .finally(() => {
        if (el) {
          el.loading = false
        } else {
          dialogViewRef.value.loading = false
        }
      })
  }

  // 退出登陆
  const logOut = () => {
    $_logout().then((res: any) => {
      if (res.code === 0) {
        success('退出成功')
        // 清除本地存储
        const name = Local.get('serverPlatformUsername') || null
        const passWord = Local.get('serverPlatformPassword') || null
        Local.clear()
        if (name && passWord) {
          Local.set('serverPlatformUsername', name)
          Local.set('serverPlatformPassword', passWord)
          Local.set('rember', true)
        }
        setUserInfoState(false)
        router.push('/login')
      }
    })
  }

  const enterEve = (event: any) => {
    if (event.keyCode === 13 || event.which === 13) {
      // 在这里执行你的逻辑
      event.preventDefault() // 阻止默认行为，比如表单提交等（根据需要使用）
      loginEve()
    }
  }

  onMounted(() => {
    // 判断是否是记住密码
    const rember = Local.get('rember') || null
    // 获取本地账号密码
    const username = Local.get('serverPlatformUsername') || null
    const password = Local.get('serverPlatformPassword') || null
    if (rember && username && password && loginForm.value) {
      loginForm.value.username = username
      loginForm.value.password = decrypt(password)
      remberPass.value = rember
    } else {
      remberPass.value = false
    }
  })
  return {
    logOut,
    loginFormRef,
    loginRules,
    loginForm,
    loginEve,
    loginLoading,
    remberPass,
    dialogViewRef,
    passFormRef,
    passForm,
    passRules,
    changePassEve,
    enterEve,
    getUserBaseInfo
  }
}

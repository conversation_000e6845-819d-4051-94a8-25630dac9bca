// 获取表格数据通用
import { onMounted, ref, watch } from 'vue'
export const useTableData = (options: any) => {
  // 表格实力
  const tableRef = ref<any>(null)
  // 表格数据
  const tableData = ref<any>([])
  // 表格数据总数
  const tableTotal = ref<any>(0)
  // 表格分页信息
  const tablePagination = ref<any>({
    current: 1,
    size: 20,
    pageQuery: true
  })

  // 新增请求参数
  const addReqParams = ref<any>(null)

  // 表格是否加载中
  const tableLoading = ref<any>(false)

  // 加载背景颜色
  const loadingBgColor = ref<any>('rgba(51, 57, 67, 0.8)')

  // 表格勾选的数组
  const selectList = ref<any>([])
  // 获取表格数据
  const getTableData = (val: any = tablePagination.value) => {
    console.log('请求前的餐宿', tablePagination.value)
    tableLoading.value = true
    options
      .reqFn(val)
      .then((res: any) => {
        if (res.code == 0 || res.resultcode == 200) {
          if (options.fn) {
            options.fn(res)
          } else {
            tableData.value = res.data
            tableTotal.value = res.total
          }
        }
      })
      .finally(() => {
        tableLoading.value = false
      })
  }

  // 页数改变时触发
  const handleSizeChange = (size: any) => {
    tablePagination.value.size = size
    getTableData()
  }

  // 页码改变时触发
  const handleCurrentChange = (current: any) => {
    console.log(current)
    tablePagination.value.current = current
    getTableData()
  }

  // 表格选中事件
  const handleSelect = (val: any) => {
    selectList.value = Array.from(new Set(val))
  }

  // 表格重置
  const resetTable = () => {
    tablePagination.value = {
      current: 1,
      size: 20
    }
    getTableData()
  }

  // 搜索框清空事件
  const clearEve = (type: any) => {
    tablePagination.value[type] = ''
    getTableData()
  }

  // onMounted(() => {
  //   if(!options.addParams) {
  //     getTableData(tablePagination.value)
  //   }
  // })

  // watch(() => addReqParams.value, (val) => {
  //   console.log("但是范德萨发大水发大水发手打");
  //   if(val) {
  //     getTableData({...addReqParams.value, ...tablePagination.value})
  //   }
  // })

  return {
    tableRef,
    tableData,
    selectList,
    tableTotal,
    tablePagination,
    addReqParams,
    getTableData,
    handleSizeChange,
    handleCurrentChange,
    handleSelect,
    tableLoading,
    loadingBgColor,
    resetTable,
    clearEve
  }
}

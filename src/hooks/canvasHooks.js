// canvas 通用方法

export const useCanvas = () => {
  /**
   *
   * @param {*} ctx canvas 元素
   * @param {*} points 图形点位信息
   * @param {*} color 线条颜色
   * @description 绘制多边形
   */

  let canvasObj = null
  // 画布宽高
  const width = ref(0)
  const height = ref(0)

  /**
   *
   * @param {*} ctx canvas元素
   * @param {*} w 指定canvas高
   * @param {*} h 指定canvas宽  宽高必须在这里设置不能在外面 100%
   * @returns
   */
  const initCanvas = (ctx, w, h) => {
    canvasObj = null
    ctx.width = w
    ctx.height = h
    canvasObj = ctx.getContext('2d')
    width.value = w
    height.value = h
  }

  /**
   *
   * @param {*} hex 16进制颜色
   * @returns 转换后的颜色
   */
  const hexToRgb = (hex) => {
    let r = 0,
      g = 0,
      b = 0

    // 如果是3位的十六进制颜色
    if (hex.length == 4) {
      r = parseInt(hex[1] + hex[1], 16)
      g = parseInt(hex[2] + hex[2], 16)
      b = parseInt(hex[3] + hex[3], 16)
    }
    // 如果是6位的十六进制颜色
    else if (hex.length == 7) {
      r = parseInt(hex[1] + hex[2], 16)
      g = parseInt(hex[3] + hex[4], 16)
      b = parseInt(hex[5] + hex[6], 16)
    }

    return `${r}, ${g}, ${b}`
  }

  /**
   *
   * @param {*} points 点
   * @param {*} color 填充颜色已经线的颜色
   */
  const drawPolygon = (points, color, name) => {
    // 设置线条宽度和颜色
    canvasObj.lineWidth = 2
    canvasObj.strokeStyle = color

    // 开始绘制多边形路径
    canvasObj.beginPath()
    const firstX = points[0].x * width.value
    const firstY = points[0].y * height.value
    canvasObj.moveTo(firstX, firstY)

    // 依次连接每个点
    for (let i = 1; i < points.length; i++) {
      const x = points[i].x * width.value
      const y = points[i].y * height.value
      canvasObj.lineTo(x, y)
    }

    // 连接最后一个点到第一个点，闭合多边形
    canvasObj.lineTo(firstX, firstY)
    canvasObj.closePath()

    // 绘制边框
    canvasObj.stroke()
    canvasObj.fillStyle = `rgba(${hexToRgb(color)}, 0.2)`

    // 填充多边形
    canvasObj.fill()
    drawTextAtPoint(firstX + 50, firstY + 30, color, name)
  }

  /**
   *
   * @param {*} x x坐标
   * @param {*} y y坐标
   * @param {*} text 文字
   */
  const drawTextAtPoint = (x, y, color = '#fff', text) => {
    const padding = 5 // 文本框的内边距
    const fontSize = 14 // 字体大小
    canvasObj.font = `${fontSize}px Arial`

    // 按 '/n' 分割文本
    const lines = text.split('/n')
    const maxWidth = Math.max(
      ...lines.map((line) => canvasObj.measureText(line).width)
    )
    const textHeight = fontSize * lines.length // 总文本高度 = 行数 * 行高

    // 设置方框样式
    canvasObj.fillStyle = 'white'
    canvasObj.strokeStyle = 'black'
    canvasObj.lineWidth = 1

    // 绘制矩形背景框
    canvasObj.fillRect(
      x - maxWidth / 2 - padding + 15,
      y - textHeight - padding + 5,
      maxWidth + padding * 2,
      textHeight + padding * 2
    )

    // 绘制矩形边框
    canvasObj.strokeRect(
      x - maxWidth / 2 - padding + 15,
      y - textHeight - padding + 5,
      maxWidth + padding * 2,
      textHeight + padding * 2
    )

    // 绘制文字
    canvasObj.font = `${fontSize}px Arial` // 设置字体样式
    canvasObj.fillStyle = color // 设置文字颜色
    canvasObj.textAlign = 'center' // 文字居中对齐

    // 在每一行的位置绘制文本
    lines.forEach((line, index) => {
      canvasObj.fillText(
        line,
        x + 15,
        y - textHeight + index * fontSize + fontSize + 5
      )
    })
  }

  const clearCanvas = () => {
    if (!canvasObj) return
    canvasObj.clearRect(0, 0, width.value, height.value)
  }

  return {
    initCanvas,
    drawPolygon,
    clearCanvas
  }
}

import { ref } from 'vue'
import { success, warning } from '@/utils/toast'

export const uesDelet = (option = {}) => {
  // 按钮状态 加载 。。。
  const loading = ref(false)

  // 当前删除项
  const delItem = ref()

  const dialogVisible = ref(false)

  // 打开弹窗
  const open = (val) => {
    delItem.value = val
    dialogVisible.value = true
  }

  // 关闭弹窗
  const close = () => {
    dialogVisible.value = false
    option.closeFn && option.closeFn()
  }

  // 删除确定方法
  const delConfirm = () => {
    if (option.fn && typeof option.fn === 'function') {
      loading.value = true
      option
        .fn(delItem.value)
        .then((res) => {
          if (res.code == 0) {
            success('删除成功')
            close()
            option.refresh && option.refresh()
          }
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      warning('请传入删除/刷新方法')
    }
  }

  return {
    delConfirm,
    open,
    close,
    loading,
    dialogVisible
  }
}

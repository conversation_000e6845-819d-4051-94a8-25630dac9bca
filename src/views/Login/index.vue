<template>
  <div id="loginEl" class="h-full w-full overflow-hidden reactive bg-[#d5e1f8]">
    <div class="bg-box">
      <img src="@/assets/login_bg.png" class="bg" alt="" />
      <div class="text-box">
        <div class="text-left out text1">API</div>
        <div class="text-left out text2">算法</div>
        <div class="text-left out text3">设备</div>
        <div class="text-left out text4">应用</div>
        <div class="text-left inte text5">大数据</div>
        <div class="text-left inte text6">人工智能</div>
        <div class="text-left inte text7">物联网</div>
        <div class="text-left inte text8">区块链</div>
      </div>
    </div>
    <div class="login-container h-full w-full flex justify-end items-center">
      <div class="login-box">
        <div class="form-box w-[523px] h-[555px] df fdc aic pt-[83px] relative">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            label-width="auto"
            hide-required-asterisk
            style="width: 420px"
          >
            <div class="tit text-center">{{ appName }}</div>
            <el-form-item
              prop="username"
              class="user"
              style="margin-left: 20px"
            >
              <el-input
                class="h-[40px]"
                v-model="loginForm.username"
                style="width: 381px"
                placeholder="账号"
              >
                <template #prefix>
                  <svg-icon name="loginUser" class="w-[16px] h-[16px]" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password" style="margin-left: 20px">
              <el-input
                class="h-[40px]"
                placeholder="密码"
                style="width: 381px"
                type="password"
                show-password
                v-model="loginForm.password"
              >
                <template #prefix>
                  <svg-icon name="loginPass" class="w-[16px] h-[16px]" />
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <div class="df fdc">
            <!-- 记住密码 -->
            <el-checkbox v-model="remberPass" class="mb-[16px]"
              >记住密码</el-checkbox
            >
            <el-button
              class="w-[381px]"
              :loading="loginLoading"
              style="height: 40px"
              @click="loginEve"
              color="#3665FF"
              >登录</el-button
            >
          </div>
          <!-- <el-button class="w-[100%] mt-[10px]" :loading="loginLoading" style="height: 40px; margin-left: 0;" color="#F2F3F5" >退出</el-button> -->
          <div class="text-center version">版本号：{{ version }}</div>
        </div>
      </div>
      <dialog-view
        ref="dialogViewRef"
        title="登录前请修改密码"
        width="30%"
        @confirm="changePassEve"
      >
        <el-form
          :model="passForm"
          ref="passFormRef"
          :rules="passRules"
          label-width="auto"
          style="max-width: 600px"
        >
          <el-form-item label="原密码" prop="password">
            <el-input
              v-model="passForm.password"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item label="新密码" prop="newpassword">
            <el-input
              v-model="passForm.newpassword"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmpassword">
            <el-input
              v-model="passForm.confirmpassword"
              type="password"
              show-password
            />
          </el-form-item>
        </el-form>
      </dialog-view>
    </div>
    <div class="bottom-tap"></div>
  </div>
</template>

<script setup lang="ts">
import { useLogin } from '@/hooks/login'
import DialogView from '@/components/DialogView/index.vue'
import { onUnmounted, onMounted } from 'vue'

const useLoginValue: any = toRefs(useLogin())

const {
  loginFormRef,
  loginRules,
  loginForm,
  loginEve,
  loginLoading,
  dialogViewRef,
  remberPass,
  passFormRef,
  passForm,
  passRules,
  changePassEve,
  enterEve
} = useLoginValue

// 获取应用名称
const appName = import.meta.env.VITE_SYS_NAME

const version = import.meta.env.VITE_SYS_VERSION

onMounted(() => {
  window.addEventListener('keydown', enterEve)
  // autoAdapt('loginEl')
})

window.onresize = () => {
  try {
    // autoAdapt('loginEl');
  } catch (error) {
    console.log(error)
  }
}

onUnmounted(() => {
  window.removeEventListener('keydown', enterEve)
})
</script>

<style lang="scss" scoped>
.text1 {
  // top: 120px;
  top: 19%;
  // left: 200px;
  left: 17.3%;
}

.text2 {
  // top:190px;
  top: 34%;
  // left: 726px;
  right: 2.2%;
}

.text3 {
  // top:544px;
  bottom: 8.8%;
  // left: 678px;
  right: 9.4%;
}

.text4 {
  // top: 524px;
  bottom: 12%;
  // left: 150px;
  left: 9.6%;
}

.text5 {
  // top: 85px;
  top: 17.6%;
  // left: 516px;
  left: 60%;
}

.text6 {
  // top: 300px;
  top: 50%;
  // left: 680px;
  right: 8.2%;
}

.text7 {
  // top: 542px;
  bottom: 10%;
  // left: 406px;
  left: 44.4%;
}

.text8 {
  // top: 338px;
  top: 55.4%;
  // left: 200px;
  left: 16%;
}

.text-box {
  position: absolute;
  top: 50%;
  left: 5%;
  width: 667px;
  height: 621px;
  transform: translateY(-50%);
  overflow: hidden;
  background: url('@/assets/login/login_item_bg.png') no-repeat center/cover;
}

.text-left {
  position: absolute;
  // transform: translate(-50%, -50%);
}

.out {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #6186ff;
  line-height: 26px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  transform: translate(-50%, -50%);
}

.inte {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: bold;
  font-size: 18px;
  color: #fdfeff;
  line-height: 23px;
  text-shadow: 0px 3px 7px #4d6bcd;
  text-align: center;
  font-style: normal;
  text-transform: none;
  transform: translate(-50%, -50%);
}

.bottom-tap {
  background: url('@/assets/login/login_bottom_tap.png') no-repeat center/cover;
  width: 337px;
  height: 31px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20px;
}

.bg-box {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;

  .bg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.login-container {
  // background: url('@/assets/login_bg.png') no-repeat center / 100% 100%;

  .tit {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: bold;
    font-size: 36px;
    color: #304963;
    line-height: 42px;
    letter-spacing: 2px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 65px;
    position: relative;

    &::after {
      position: absolute;
      content: '';
      width: 98px;
      height: 2px;
      background-color: #4671ff;
      bottom: -16px;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .login-box {
    margin-right: 50px;
  }

  .form-box {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    border-radius: 10px;

    .user {
      margin-bottom: 28px !important;
    }
  }

  .version {
    position: absolute;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    color: #9096a9;
  }
}
</style>

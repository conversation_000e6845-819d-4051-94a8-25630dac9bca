<template>
  <div class="containerBox">
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border"
    >
      <div class="flex-shrink-0">
        <div class="mb-[16px] flex gap-x-[16px]">
          <el-input
            style="width: 150px"
            v-model="query.eventName"
            placeholder="事件名称"
            clearable
            @change="changeEve"
          >
          </el-input>
          <datePicker
            v-model:startAt="query.startAt"
            v-model:endAt="query.endAt"
            class="mr-2"
            :showEndAfterTime="true"
            :showStartAfterTime="true"
            @change="changeEve"
          />
          <div>
            <el-button type="" @click="resetEve">重置</el-button>
          </div>
        </div>
        <el-button type="">导出</el-button>
      </div>
      <div class="flex-1 overflow-hidden">
        <ElementTable
          ref="table"
          :data="tableData"
          :table-title="tableTitle"
          :page-config="pageConfig"
        >
          <template v-slot:video="row">
            <div
              style="
                color: #3665ff;
                cursor: pointer;
                text-decoration: underline;
              "
              @click="goVideoManagement(row.data.row)"
            >
              {{ row.data.row.deviceName }}
            </div>
          </template>
        </ElementTable>
      </div>
    </div>
    <!-- 事件详情弹窗 -->
    <dialog-view
      ref="infoRef"
      width="90vw"
      :title="infoData?.eventName"
      :footer="false"
    >
      <div class="w-full df overflow-hidden items-center">
        <div
          class="flex justify-between h-[70vh] bg-[#D9D9D9] mr-[25px] left"
          style="aspect-ratio: 16 / 9 !important"
        >
          <!-- <img :src="imgUrl +'/oss/file/preview?fileName='+ infoData.rectDrewPicUrl"
                        alt="" style="width: 100%; height: 100%;"/> -->
          <preview-img
            style="width: 100%; height: 100%"
            :name="infoData?.rectDrewPicUrl"
            fit="fill"
          />
        </div>
        <div
          class="flex justify-between flex-1 fdc right h-[70vh] overflow-hidden"
        >
          <div class="h-[62px] fn header df w-full jcsb overflow-hidden">
            <div
              class="header truncate px-2 truncate w-full overflow-hidden"
              :title="infoData?.deviceName"
            >
              {{ infoData?.deviceName }}
            </div>
            <!-- <div class="header px-[10px]">
                            <el-button type="primary" :icon="ArrowLeft" @click="goBack">返回</el-button>
                        </div> -->
          </div>
          <div class="f1 body">
            <el-scrollbar>
              <div class="item">
                <div class="title">事件时间</div>
                <div class="truncate">{{ infoData?.happenTime }}</div>
              </div>
              <div class="item">
                <div class="title">事件名称</div>
                <div>{{ infoData?.eventName }}</div>
              </div>
              <div class="item">
                <div class="title">视频名称</div>
                <div>{{ infoData?.deviceName }}</div>
              </div>
              <div class="item">
                <div class="title">经纬度</div>
                <div>{{ infoData?.longitude }},{{ infoData?.latitude }}</div>
              </div>
              <div class="item">
                <div class="title">唯一标识</div>
                <div>{{ infoData?.deviceIndex }}</div>
              </div>
              <div class="item">
                <div class="title">算法名称</div>
                <div>{{ infoData?.algorithmName }}</div>
              </div>
              <!-- <div class="item">
                                <div class="title">算法类型</div> -->
              <!-- <div style="word-break: break-word;">{{ data.algorithmCode }}</div> -->
              <!-- <div style="word-break: break-word;">城管类</div>
                            </div> -->
              <div class="item">
                <div class="title">厂商名称</div>
                <div>{{ infoData?.factoryName }}</div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </dialog-view>
    <!-- 视频详情弹窗 -->
    <video-info ref="videoInfoRef" />
  </div>
</template>

<script setup lang="ts">
import datePicker from '@/components/DatePicker/index.vue'
import videoInfo from '../serverPlatformApp/components/videoInfo.vue'
import previewImg from '@/components/PreviewImg/index.vue'
import DialogView from '@/components/DialogView/index.vue'
import { $getEventList } from '@/api/addTask/index'

onMounted(() => {
  getDataList()
})
// 视频详情
// 打开视频
const videoInfoRef = ref(null)
const goVideoManagement = (val: any) => {
  videoInfoRef.value.open({ gbId: val.deviceIndex })
}
// 事件详情部分
const infoRef = ref(null)
const infoData = ref(null)
const handleLook = (row: any) => {
  infoData.value = row
  infoRef.value.open(row)
}
// 获取列表
const getDataList = () => {
  $getEventList(query.value).then((res: any) => {
    if (res.code === 0) {
      tableData.value = res.data.records || []
      pageConfig.value.total = res.data.total || 0
    }
  })
}
const changeEve = () => {
  getDataList()
}
const resetEve = () => {
  pageConfig.value.currentPage = 1
  query.value = {
    current: pageConfig.value.currentPage,
    size: pageConfig.value.pageSize,
    eventName: '',
    startAt: '',
    endAt: ''
  }
  getDataList()
}
const tableData = ref([])
const tableTitle: Array<Object | any> = [
  {
    label: '事件名称',
    prop: 'eventName',
    type: 'text'
  },
  {
    label: '发生时间',
    prop: 'happenTime',
    type: 'text'
  },
  {
    label: '视频',
    name: 'video',
    type: 'custom'
  },
  {
    label: '唯一标识',
    prop: 'deviceIndex',
    type: 'text'
  },
  {
    label: '发生位置',
    prop: 'address',
    type: 'text'
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '详情',
        click: (row: any) => {
          handleLook(row)
        }
      }
      // {
      //   isLink: true,
      //   type: 'danger',
      //   name: '删除'
      // }
    ]
  }
]

const pageConfig = ref<any>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})
// 查询参数
const query = ref<any>({
  current: pageConfig.value.currentPage,
  size: pageConfig.value.pageSize,
  eventName: '',
  startAt: '',
  endAt: ''
})
/**
 * 每条页数改变方法
 * @param e 每页条数
 */
const sizeChange = (e: any) => {
  pageConfig.value.pageSize = e
  query.value.size = pageConfig.value.pageSize
  getDataList()
}

/**
 * 页码改变方法
 * @param e 页数
 */
const cuttentChange = (e: any) => {
  pageConfig.value.currentPage = e
  query.value.current = pageConfig.value.currentPage
  getDataList()
}
</script>

<style lang="scss" scoped>
.header {
  text-align: center;
  line-height: 62px;
  color: #333333;
  font-size: 20px;
  font-weight: bold;
  background: rgba(36, 104, 255, 0.16);
}

.body {
  padding: 32px 8px 32px 28px;
  background: rgba(36, 104, 255, 0.04);

  .item {
    margin-bottom: 24px;
    font-weight: 400;
    font-size: 16px;
    color: #333333;

    & > .title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      margin-bottom: 8px;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -8px;
        transform: translate(-100%, -50%);
        height: 8px;
        width: 8px;
        background: #3665ff;
        border-radius: 50%;
      }
    }
  }
}
</style>

<template>
  <div class="w-full h-full">
    <el-row class="h-[560px] w-full">
      <el-col :span="4" class="h-full">
        <div class="h-full border-[1px] border-solid border-[#E2E2E2FF]">
          <h5
            class="h-[66px] bg-[#3665FF1A] text-[#3665FF] text-[18px] leading-[66px] font-bold pl-[26px] mb-[6px]"
          >
            分析时间
          </h5>
          <el-menu :default-active="activeIndex">
            <el-menu-item
              v-for="(item, index) in menuList"
              :key="item"
              :index="item.id"
              @click="menuItemClick(item.id)"
            >
              <div class="flex items-center justify-between">
                <span>{{ item.name }}</span>
                <el-icon
                  v-if="item.notDel == 0 && item.name != '自定义' && !disabled"
                  class="ml-[10px]"
                  @click="deleteTimePlan(item.id)"
                >
                  <Delete
                    style="color: #ff6561ff; font-size: 18px; cursor: pointer"
                  />
                </el-icon>
              </div>
            </el-menu-item>
          </el-menu>
        </div>
      </el-col>
      <el-col :span="20" class="h-full">
        <div class="h-full ml-[25px] flex flex-col">
          <!-- 时间计划名称 -->
          <div class="flex aic">
            <div class="text-[#333333] text-[16px]">时间计划名称</div>
            <el-input
              style="width: 284px"
              clearable
              class="ml-[16px]"
              :disabled="activeItem.notDel == 1 || disabled"
              placeholder="请输入内容"
              v-model="title"
            ></el-input>
          </div>
          <!-- 时段选择 -->
          <div
            class="text-[#333333] text-[16px] mt-[30px]"
            v-if="activeItem.notDel == 0"
          >
            时段选择
          </div>
          <div class="mt-[16px]" v-if="activeItem.notDel == 0 && !disabled">
            <el-button type="primary" @click="saveTimePlan">保存</el-button>
            <el-button @click="resetTimePlan">重置</el-button>
          </div>

          <div class="f1 mt-[16px]">
            <timePlan
              ref="timePlanRef"
              :isEdit="activeItem.notDel == 0 && !disabled"
            ></timePlan>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>

  <!-- 删除目录 -->
  <del-dialog ref="deleteDialogRef" @confirm="deleteTimePlanConfirm">
  </del-dialog>
</template>
<script setup>
import { ref, watch, nextTick, onMounted } from 'vue'
import timePlan from './timePlan.vue'
import {
  $addOrEditTimePlan,
  $deleteTimePlan,
  $getAlgorithmWeekPlan
} from '@/api/ai'
import { success, error } from '@/utils/toast'
import { Delete } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash-es'
import DialogView from '@/components/DialogView/index.vue'
import DelDialog from '@/components/DelDialog/index.vue'
// 传入的属性
const props = defineProps({
  weeklyPlanid: {
    type: String,
    default: ''
  },
  customWeeklyPlanid: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const deleteDialogRef = ref()
const timePlanRef = ref()
const activeIndex = ref(props.weeklyPlanid)
const activeItem = ref({ name: '', day: [], id: '' })
const title = ref('')

const $emit = defineEmits(['update:customWeeklyPlanid', 'update:weeklyPlanid'])
const menuList = ref([])
const copyTimePlan = ref([])
// 选中目录
const menuItemClick = (index) => {
  activeIndex.value = index
  activeItem.value = menuList.value.find((item) => item.id == index)
  console.log('activeItem', activeItem.value, menuList.value, index)

  let dayData = cloneDeep(activeItem.value.day)
  // 将所有时间段分解为连续的小段
  dayData.forEach((day, rowIndex) => {
    let newDay = []
    day.forEach((item, itemIndex) => {
      let from = parseInt(item.from)
      let to = parseInt(item.to)
      // 分解时间段为连续的小段
      for (let i = from; i < to; i += 3600) {
        let newTo = i + 3600
        if (newTo > to) newTo = to
        newDay.push({ from: i.toString(), to: newTo.toString() })
      }
    })
    dayData[rowIndex] = newDay
  })
  // console.log("xxx",timePlanRef.value.weekData,dayData);
  timePlanRef.value.weekData = dayData
  copyTimePlan.value = cloneDeep(dayData)
  title.value = menuList.value.find((item) => item.id == index).name
  if (title.value === '自定义') {
    title.value = ''
  }
  console.log('index', index)
  $emit('update:weeklyPlanid', index)
}
onMounted(() => {
  getTimePlan().then(() => {
    nextTick(() => {
      menuItemClick(props.weeklyPlanid || '1')
    })
  })
})

// 获取时间计划
const getTimePlan = async (weeklyPlanid = props.customWeeklyPlanid) => {
  try {
    // const res = await $getAlgorithmTimePlan()
    const res = await $getAlgorithmWeekPlan({ weeklyPlanid })
    if (res.code == 0) {
      const newVal = res.data
      if (weeklyPlanid) {
        menuList.value = newVal
        return
      }
      const list = [
        ...newVal,
        {
          name: '自定义',
          notDel: 0,
          id: 'add',
          day: [[], [], [], [], [], [], []]
        }
      ]
      menuList.value = list
    }
  } catch (e) {
    console.log(e)
  }
}
// 重置
const resetTimePlan = () => {
  timePlanRef.value.weekData = cloneDeep(copyTimePlan.value)
}

// 保存时间计划
const saveTimePlan = () => {
  const day = timePlanRef.value.weekData
  const id = activeItem.value.id
  console.log(id)
  const index = day.findIndex((item) => item.length > 0)
  if (index == -1) {
    return error('请选择时间')
  }
  if (!title.value) {
    return error('请输入名称')
  }

  // menuList中是否有title重名
  const isRepeat = menuList.value.some((item) => item.name == title.value)
  if (isRepeat && title.value != activeItem.value.name) {
    return error('名称已存在')
  }
  const param = {
    name: title.value,
    day: day
  }
  if (id && id != 'add') {
    param.id = id
  }
  $addOrEditTimePlan(param).then((res) => {
    if (res.code == 0) {
      getTimePlan(res.data).then(() => {
        $emit('update:customWeeklyPlanid', res.data)
        success('保存成功')
        // 找到index
        const index = menuList.value.find((item) => item.name == title.value).id
        console.log('获取的id', index)
        menuItemClick(index)

        // nextTick(() => {
        //   menuItemClick(index)
        // })
      })
    }
  })
}

// 删除目录
const deleteTimePlan = () => {
  const id = activeItem.value.id
  if (!id) {
    return
  }
  deleteDialogRef.value.open()
}

// 确认删除
const deleteTimePlanConfirm = () => {
  $deleteTimePlan(activeItem.value.id).then((res) => {
    if (res.code == 0) {
      success('删除成功')
      $emit('update:customWeeklyPlanid', '')
      deleteDialogRef.value.close()
      getTimePlan().then(() => {
        nextTick(() => {
          menuItemClick(activeIndex.value - 1)
        })
      })
    }
  })
}

defineExpose({
  activeItem,
  menuList
})
</script>

<style lang="scss" scoped>
:deep(.el-menu-item.is-active) {
  background: #3665ff1a;
}
</style>

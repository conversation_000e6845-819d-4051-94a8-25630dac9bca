<template>
  <dialog-view
    :title="detileData?.name"
    width="1132px"
    ref="detileEl"
    :footer="false"
  >
    <!-- {{ detileData }} -->
    <div class="df mb-[24px] aic overflow-hidden">
      <div class="w-[320px] h-[195px] bg-[#D9D9D9] fn mr-[38px]">
        <img
          :src="baseUrl + '/oss/file/preview?fileName=' + detileData.picUrl"
          alt=""
          class="w-full h-full"
        />
      </div>
      <div class="flex-1">
        <div class="df">
          <div class="w-[220px] fn">
            <div class="title">算法名称</div>
            <div class="content">{{ detileData.name }}</div>
          </div>
          <div class="flex-1">
            <div class="title">算法描述</div>
            <div class="content">
              {{ detileData.description }}
            </div>
          </div>
        </div>
        <div class="df jcsb mt-[24px] w-full" style="flex-wrap: wrap">
          <div class="mr-8 flex-1" v-if="detileData.sourceType">
            <div class="title">分析源类型</div>
            <div class="content">
              {{
                dictArr
                  .find((item) => item.name == 'sourceType')
                  .data.find((it) => it.value === detileData.sourceType).label
              }}
            </div>
          </div>
          <div class="mr-8 flex-1" v-if="detileData.analysisType">
            <div class="title">分析类型</div>
            <div class="content">
              {{
                dictArr
                  .find((item) => item.name == 'analysisType')
                  .data.find((it) => it.value === detileData.analysisType).label
              }}
            </div>
          </div>
          <div class="mr-8 flex-1" v-if="detileData.algorithmTypeName">
            <div class="title">算法类型</div>
            <div class="content">{{ detileData.algorithmTypeName }}</div>
          </div>
          <!-- <div class="mr-8" v-if="detileData.vendor">
             <div class="title">厂商</div>
             <div class="content">{{ detileData.vendor }}</div>
           </div> -->
        </div>
      </div>
    </div>
    <div class="df jcsb">
      <div
        class="w-[334px]"
        v-if="detileData.analysisTargetName || detileData.analysisTargetName"
      >
        <div class="title">分析目标</div>
        <div class="content" v-if="detileData.analysisTargetName">
          <div v-for="it in detileData.analysisTargetName?.split(',')">
            {{ it }}
          </div>
        </div>
      </div>
      <div class="w-[334px]" v-if="detileData.appIndust">
        <div class="title">适用行业</div>
        <div class="content">
          <div v-for="it in detileData.appIndustName?.split(',')">{{ it }}</div>
        </div>
      </div>
      <div class="w-[334px]" v-if="detileData.appPlace">
        <div class="title">适用场所</div>
        <div class="content">
          <div v-for="it in detileData.appPlaceName?.split(',')">{{ it }}</div>
        </div>
      </div>
    </div>
    <div
      class="title mt-[24px]"
      v-if="detileData.algorithmConditionsDtos?.length > 0"
    >
      应用条件
    </div>
    <div class="df tiaojianlist" style="flex-wrap: wrap">
      <div
        class="w-[334px] h-[164px] mr-[24px] mb-[16px] item"
        v-for="item in detileData.algorithmConditionsDtos"
      >
        <div class="title">{{ item.title }}</div>
        <div>
          {{ item.describe }}
        </div>
      </div>
    </div>
  </dialog-view>
</template>

<script setup>
import DialogView from '@/components/DialogView/index.vue'
import { ref, onMounted } from 'vue'
import { $getDictType } from '@/api/dict'
const baseUrl = import.meta.env.VITE_RESOURCE_URL
const detileEl = ref()

const detileData = ref({})

// 字典数组
const dictArr = ref([
  // 源类型
  {
    data: [],
    type: 'alg_source_type',
    name: 'sourceType'
  },
  // 分析类型
  {
    data: [],
    type: 'alg_analy_type',
    name: 'analysisType'
  },
  // 分析目标
  {
    data: [],
    type: 'analy_target',
    name: 'analysisTarget'
  },
  // 适用行业
  {
    data: [],
    type: 'applicable_industry',
    name: 'appIndust'
  },
  // 适用场所
  {
    data: [],
    type: 'applicable_place',
    name: 'appPlace'
  }
])

// 获取字典数据
const getDictData = () => {
  dictArr.value.forEach((item) => {
    $getDictType(item.type).then((res) => {
      item.data = res.data
    })
  })
  console.log(dictArr.value)
}

const open = (row) => {
  console.log('获取数据', row)
  detileData.value = row
  detileEl.value.open()
}

onMounted(() => {
  getDictData()
})

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.title {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8px;
}
.content {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #52585f;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.tiaojianlist {
  :nth-child(3n) {
    margin-right: 0;
  }
}
.item {
  padding: 24px;
  padding-top: 28px;
  background: linear-gradient(180deg, #f2f5ff 0%, #ffffff 34%);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
  .title {
    position: relative;
    margin-left: 10px;
    line-height: 16px;
    margin-bottom: 9px;
    &:before {
      content: '';
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -10px;
      width: 3px;
      height: 16px;
      background-color: #3665ff;
    }
  }
}
</style>

<template>
  <el-drawer
    v-model="dialogVisible"
    :title="showName"
    align-center
    size="70%"
    :before-close="handleClose"
  >
    <div class="p-[20px] w-full">
      <el-form
        hide-required-asterisk
        ref="formRef"
        :model="formData"
        label-width="100px"
        inline
        :rules="rules"
      >
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="formData.taskName"
            placeholder="请输入任务名称"
            :disabled="title == '查看任务'"
            style="width: 500px"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="camera" style="width: 50%">
          <div class="df aic w-[50%]">
            <!-- <div v-if="formData.camera" style="text-wrap:nowrap;margin-right: 10px;">{{ formData.camera.name
                          }}</div> -->
            <el-button
              class="mr-[10px]"
              v-if="title != '查看任务' && !videoList?.camera.gbId"
              type="primary"
              @click="changeVideo"
              :disabled="title == '查看任务'"
              >选择视频</el-button
            >
            <el-input
              :model-value="formData?.camera?.name"
              placeholder="视频名称"
              disabled
              v-if="!props.item?.name"
            ></el-input>
            <!-- <div v-if="title != '查看任务' && !videoList?.camera.gbId " class="container w-[60px]" @click="changeVideo">选择视频</div> -->
          </div>
        </el-form-item>
        <el-form-item label="" style="width: 100%" prop="algorithmPresets">
          <el-button
            class="mr-[10px] ml-[25px]"
            v-if="title != '查看任务'"
            type="primary"
            @click="changeAlgorithm"
            >选择算法</el-button
          >
          <!-- <div v-if="title != '查看任务'" class="container w-[60px]" @click="changeAlgorithm">选择算法</div> -->
          <!-- <div class="df aic w-full"> -->
          <div v-if="formData.algorithmPresets?.length > 0" class="mr-[10px]">
            {{
              formData.algorithmPresets
                .map((item) => item.algorithmName)
                .join('、')
            }}
          </div>
          <!-- </div> -->
        </el-form-item>
        <el-form-item style="width: 100%">
          <div class="w-full">
            <el-tabs
              v-model="changeAlgorithmItem"
              type="card"
              class="px-[10px]"
              :closable="title != '查看任务'"
              @tab-remove="removeTab"
              @tab-click="handleTabClick"
            >
              <el-tab-pane
                v-for="item in formData.algorithmPresets"
                :key="item.algorithmCode"
                :label="item.algorithmName"
                :name="item.algorithmCode"
              >
                <!-- {{ item }} -->
              </el-tab-pane>
            </el-tabs>
            <div style="position: relative; padding: 10px">
              <!-- <time-select-dialog ref="timeSelectDialogRef"
                              v-model:weeklyPlanid="item.weeklyPlanid"
                              :key="new Date().getTime()"></time-select-dialog> -->
              <div class="yaopanBtn">
                是否需要研判
                <el-switch
                  v-model="activeItemTask.needExamine"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :active-value="1"
                  :inactive-value="0"
                  :disabled="title == '查看任务'"
                ></el-switch>
              </div>
              <NewTimeSelect
                ref="timeSelectDialogRef"
                :key="new Date().getTime()"
                :disabled="title == '查看任务'"
                v-model:weeklyPlanid="activeItemTask.weeklyPlanId"
                v-model:customWeeklyPlanid="activeItemTask.customWeeklyPlanid"
              />
              <div
                v-if="!formData.algorithmPresets?.length > 0"
                class="w-full h-full model-container"
              ></div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer my-[30px]">
        <div v-if="title == '查看任务'">
          <el-button @click="handleCancel">关闭</el-button>
        </div>
        <div v-else>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="btnLoading">
            保存
          </el-button>
          <!-- <el-button type="primary" @click="handleTaskAddStart">
                      保存并启动
                  </el-button> -->
        </div>
      </div>
    </template>
  </el-drawer>
  <!-- 算法选择弹框 -->
  <selectAlgorithm
    ref="selectAlgorithmDialog"
    @confirm="confirmSelectAlgorithm"
    :data="algorithmList"
  />
  <SelectVideo ref="selectVideoDialog" @handleConfirm="confirmSelectVideo" />
</template>

<script setup>
import selectAlgorithm from './selectAlgorithm.vue'
import { computed, ref, watch } from 'vue'
import SelectVideo from './selectVideo.vue'
import NewTimeSelect from './newTimeSelect.vue'
import {
  $addAlgorithmTask,
  $editAlgorithmTask,
  $getAlgorithmTaskRelateAis
} from '@/api/ai'

const props = defineProps({
  title: {
    type: String,
    default: '新增任务'
  },
  data: {
    //编辑回填的数据
    type: Object,
    default: null
  },
  videoList: {
    //默认选中的视频
    type: Object,
    default: null
  },
  algorithm: {
    //默认选中的算法
    type: Array,
    default: {}
  },
  item: {
    type: Object,
    default: null
  }
})

const btnLoading = ref(false)

const algorithmList = ref([])

const formData = ref({})
const formRef = ref(null)
const timeSelectDialogRef = ref()
const selectAlgorithmDialog = ref()
const selectVideoDialog = ref()

const dialogVisible = ref(false)
const changeAlgorithmItem = ref('')

watch(
  () => props.videoList,
  (val) => {
    if (val?.camera?.gbId) {
      formData.value = props.videoList
      console.log('videoList', val.camera.gbId)
      queryAlgorithmByCameraId(val?.camera?.gbId)
    } else {
      formData.value = {}
    }
  },
  {
    immediate: true,
    deep: true
  }
)
watch(
  () => props.algorithm,
  (val) => {
    if (val) {
      formData.value = {
        taskName: '',
        algorithmPresets:
          [
            {
              ...props.algorithm,
              weeklyPlanId: '1',
              algorithmName: props.algorithm.name
            }
          ] || [],
        camera: {
          gbId: '',
          name: ''
        }
      }
    } else {
      formData.value = {}
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => props.data,
  (val) => {
    if (val) {
      formData.value = {
        taskName: val.taskName,
        camera: {
          gbId: val.cameraindexCode,
          name: val.cameraindexName
        },
        algorithmPresets:
          val.algorithmPresetsVos?.map((item) => {
            return { ...item, weeklyPlanId: item.weeklyPlanid }
          }) || []
      }
      console.log('props.data', props.data)
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const activeItemTask = ref({})
watch(
  () => formData.value?.algorithmPresets,
  (val, oldval) => {
    if (val?.length == oldval?.length) return
    // if(props.title != '新增任务') return
    let tabs = val?.map((item) => item.algorithmCode) || []
    if (!tabs.includes(changeAlgorithmItem.value)) {
      changeAlgorithmItem.value = tabs[0]
    } else {
      changeAlgorithmItem.value = ''
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => changeAlgorithmItem.value,
  (val) => {
    if (changeAlgorithmItem.value) {
      if (activeItemTask.value.algorithmCode) {
        formData.value.algorithmPresets = formData.value.algorithmPresets.map(
          (item) => {
            if (item.algorithmCode === activeItemTask.value.algorithmCode) {
              item = activeItemTask.value
              console.log('选中数据', activeItemTask.value)
            }
            return item
          }
        )
      }
      let temp = formData.value.algorithmPresets.find(
        (item) => item.algorithmCode === val
      )
      activeItemTask.value = temp
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const rules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  camera: [{ required: true, message: '请选择视频', trigger: 'blur' }],
  algorithmPresets: [{ required: true, message: '请选择算法', trigger: 'blur' }]
}

//确认选择的算法
function confirmSelectAlgorithm(data) {
  let addData = data.map((item) => {
    return {
      algorithmCode: item.apiServerCode,
      algorithmName: item.apiServerName,
      vendor: item.vendor || '',
      weeklyPlanId: '1',
      customWeeklyPlanid: ''
    }
  })

  // formData.value.algorithmPresets = [...formData.value?.algorithmPresets||[],...addData]
  formData.value.algorithmPresets = addData
}

//确认选择的视频
function confirmSelectVideo(data) {
  formData.value.camera = data.value
  // if (!formData.value.taskName) {
  formData.value.taskName = data.value.name + '任务'
  // }
  queryAlgorithmByCameraId(data.value.gbId)
}

const flag = ref(false)

const showName = computed(() => {
  console.log('props.item', props.item, !props.item)
  if (!props.item) return props.title
  let temp = ''
  if (props.item.taskName) {
    temp = props.item.taskName
  } else if (props.item.name) {
    temp = props.item.name
  } else {
    return props.title
  }
  // return temp + "--" + props.title
  return temp
})

//查询视频已绑定算法
function queryAlgorithmByCameraId(code) {
  $getAlgorithmTaskRelateAis({ code }).then((res) => {
    if (res.code == 0) {
      // formData.value.algorithmPresets =
      console.log('已绑定算法', res.data)
      if (!res.data) {
        //无任务
        flag.value = false
        return
      } else {
        //有任务
        flag.value = true
      }
      let ids = res.data.algorithmPresetsVos.map((item) => item.algorithmCode)
      let algorithmPresets = []
      if (formData.value.algorithmPresets) {
        algorithmPresets = formData.value.algorithmPresets.filter(
          (item) => !ids.includes(item.algorithmCode)
        )
      }
      formData.value = {
        taskName: res.data.taskName || '',
        taskIndexCode: res.data.taskIndexCode || '',
        camera: {
          gbId: res.data.cameraindexCode || '',
          name: res.data.cameraindexName || ''
        },
        algorithmPresets: [
          ...algorithmPresets,
          ...(res.data.algorithmPresetsVos?.map((item) => {
            return { ...item, weeklyPlanId: item.weeklyPlanid }
          }) || [])
        ]
      }
    }
  })
}

//删除选中算法
function removeTab(tabName) {
  formData.value.algorithmPresets = formData.value.algorithmPresets.filter(
    (item) => item.algorithmCode !== tabName
  )
}

//点击tab
function handleTabClick(tab, event) {}

//选择视频
function changeVideo() {
  selectVideoDialog.value.open()
}

//选择算法
function changeAlgorithm() {
  selectAlgorithmDialog.value.open()
  algorithmList.value = formData.value.algorithmPresets
}

//关闭弹窗前
function handleClose() {
  formData.value = {
    camera: {
      name: '',
      gbId: ''
    }
  }
  dialogVisible.value = false
  activeItemTask.value = {}
  $emit('handleClose')
}

//取消
function handleCancel() {
  handleClose()
}

const $emit = defineEmits(['handleSave', 'handleClose', 'handleStart'])

//保存
async function handleSave() {
  if (!formRef.value) return
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      console.log(formData.value)
      let userId = JSON.parse(localStorage.getItem('userInfo')).user_id
      let option = {
        taskName: formData.value.taskName,
        presetsDto: {
          cameraIndexCode: formData.value.camera.gbId,
          cameraindexName: formData.value.camera.name
        },
        taskIndexCode: formData.value?.taskIndexCode || '',
        algorithmPresets: formData.value.algorithmPresets.map((item) => {
          if (item.weeklyPlanId == 'add') {
            item.weeklyPlanId = '1'
          }
          return item
        }),
        userIds: [userId]
      }
      console.log(option)
      btnLoading.value = true
      if (props.title == '新增任务' && flag.value == false) {
        $addAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('新增成功')
            btnLoading.value = false
            dialogVisible.value = false
            $emit('handleSave')
            formData.value = {}
            activeItemTask.value = {}
          } else {
            btnLoading.value = false
          }
        })
      } else {
        console.log('编辑任务', props.data, props.item)
        if (!option.taskIndexCode) {
          option.taskIndexCode = props.data.taskIndexCode
        }
        $editAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('编辑成功')
            btnLoading.value = false
            dialogVisible.value = false
            $emit('handleSave')
            formData.value = {}
            activeItemTask.value = {}
          } else {
            btnLoading.value = false
          }
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

//保存并启动
async function handleTaskAddStart() {
  if (!formRef.value) return
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      console.log(formData.value)
      let userId = JSON.parse(localStorage.getItem('userInfo')).user_id
      let option = {
        taskName: formData.value.taskName,
        presetsDto: {
          cameraIndexCode: formData.value.camera.gbId,
          cameraindexName: formData.value.camera.name
        },
        algorithmPresets: formData.value.algorithmPresets,
        userIds: [userId]
      }
      console.log(option)
      if (props.title == '新增任务' && flag.value == false) {
        $addAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('新增成功')
            dialogVisible.value = false
            // $emit('handleSave')
            $emit('handleStart', { taskIndexCode: res.data })
            formData.value = {}
            activeItemTask.value = {}
          }
        })
      } else {
        option.taskIndexCode = props.data.taskIndexCode
        $editAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('新增成功')
            dialogVisible.value = false
            // $emit('handleSave')
            $emit('handleStart', props.data)
            formData.value = {}
            activeItemTask.value = {}
          }
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

//弹窗开启
function open() {
  dialogVisible.value = true
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.container {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #3665ff;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-decoration-line: underline;
  text-transform: none;

  &:hover {
    cursor: pointer;
  }
}

.model-container {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: no-drop;
}

.yaopanBtn {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 999;
}
</style>

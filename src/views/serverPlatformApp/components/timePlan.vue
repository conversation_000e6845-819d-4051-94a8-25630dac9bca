<template>
  <div class="w-[100%] h-full bg-[#f5f5f5] flex">
    <el-col :span=4 class="bg-[#2468FF29] !flex flex-col h-full">
      <div class="df fc text" style="flex:1">
        日/时
      </div>
      <div class="df flex-col f1" style="flex:10">
        <div style="flex:1"></div>
        <div style="flex:20" class="f1 df flex-col">
          <div class="flex-1 text df fc">星期一</div>
          <div class="flex-1 text df fc">星期二</div>
          <div class="flex-1 text df fc">星期三</div>
          <div class="flex-1 text df fc">星期四</div>
          <div class="flex-1 text df fc">星期五</div>
          <div class="flex-1 text df fc">星期六</div>
          <div class="flex-1 text df fc">星期日</div>
        </div>
        <div style="flex:2"></div>
      </div>
    </el-col>
    <el-col :span=20 class="bg-[#2468FF0F] !flex flex-col h-full">
      <div class="df" style="flex:1">
        <div class="flex-1 section-title-bg df fc text" style="cursor: pointer;" @click="selectSection(0)">
          0 - 11
        </div>
        <div class="flex-1 section-title-bg df fc text" style="cursor: pointer;" @click="selectSection(1)">
          12 - 23
        </div>
      </div>
      <div class="df flex-col f1 ml-[16px] mr-[16px]" style="flex:10">
        <div class="df items-center h-[24px]">
          <div v-for="n in 24" :key="n" class="flex-1 text-[#333333] text-font-[16px] text-center title flex items-center h-full">
            {{ n - 1 }}
          </div>
        </div>
        <div style="flex:20" class="f1 df flex-col overflow-hidden">
          <!-- 7*24个小格 -->
          <div v-for="(day, rowIndex) in weekData" :key="'row-' + rowIndex" class="row">
            <div v-for="hour in 24" :key="'hour-' + hour" class="box"
              :class="{ 'box-select': isSelected(rowIndex, hour), 'box-hover': isEdit }"
              @mousedown="startSelection($event, rowIndex, hour)"
              @mouseover="handleMouseOver(rowIndex, hour)"
              @mouseup="stopSelection">
            </div>
          </div>
        </div>
        <div style="flex:2" class="df"></div>
      </div>
    </el-col>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { cloneDeep } from 'lodash-es';
import { getTimeInterval, generateTimeIntervals } from '@/utils/Tools'

const selecting = ref(false)
const selectionType = ref(null) // 'select' or 'deselect'

const startSelection = (event, row, col) => {
  if (!props.isEdit) return
  event.preventDefault()
  selecting.value = true
  const isSelectedItem = isSelected(row, col)
  selectionType.value = isSelectedItem ? 'deselect' : 'select'
  selectItem(row, col)
}

const handleMouseOver = (row, col) => {
  if (!props.isEdit || !selecting.value) return
  const isSelectedItem = isSelected(row, col)
  if (selectionType.value === 'select' && !isSelectedItem) {
    selectItem(row, col)
  } else if (selectionType.value === 'deselect' && isSelectedItem) {
    selectItem(row, col)
  }
}

const stopSelection = () => {
  selecting.value = false
  selectionType.value = null
}

const selectItem = (row, col) => {
  if (!props.isEdit) return
  console.log(row, col)
  const time = getTimeInterval(col)
  const day = cloneDeep(weekData.value[row])
  console.log(time, day)
  if (!day) return
  const index = day.findIndex(item => item.from === time.from && item.to === time.to)
  if (index !== -1) {
    day.splice(index, 1)
  } else {
    const insertFrom = time.from
    const insertTo = time.to
    //找到上一个的index
    const lastIndex = day.findIndex(item => parseInt(item.from) >= parseInt(insertFrom))
    if (lastIndex !== -1) {
      day.splice(lastIndex, 0, time)
    } else {
      day.push(time)
    }
  }
  weekData.value[row] = day
}

// 传入的属性
const props = defineProps({
  // 是否可修改
  isEdit: {
    type: Boolean,
    default: true
  }
})

const weekData = ref([])
defineExpose({
  weekData,
})

// 监听timePlan
// watch(() => props.timeList, (newVal) => {
//   console.log(newVal);
//   weekData.value = newVal
// }, { deep: true, immediate: true })

const isSelected = (rowIndex, hour) => {
  const day = weekData.value[rowIndex]
  if (!day) return false

  // 获取当前小时的时间范围
  const hourStart = (hour - 1) * 3600
  const hourEnd = hour * 3600
  const index = day.findIndex(item => {
    const fromSeconds = parseInt(item.from)
    const toSeconds = parseInt(item.to)
    // 判断时间是否在范围内
    const isInRange =
      (fromSeconds >= hourStart && toSeconds <= hourEnd) || (fromSeconds <= hourStart && toSeconds >= hourEnd)
    return isInRange
  })
  return index !== -1
}

const selectSection = (index) => {
  if (!props.isEdit) return
  const times = generateTimeIntervals(index === 0 ? 0 : 12, index === 0 ? 12 : 24)
  weekData.value.forEach((day, rowIndex) => {
    day.splice(0, day.length, ...times)
  })
}

onMounted(() => {
  window.addEventListener('mouseup', stopSelection)
})

onBeforeUnmount(() => {
  window.removeEventListener('mouseup', stopSelection)
})
</script>

<style lang="scss" scoped>
.section-title-bg {
  background: linear-gradient(90deg, rgba(54, 101, 255, 0.26) 0%, rgba(54, 101, 255, 0.08) 100%);
}

.text {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.title {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.row {
  display: flex;
  flex: 1;
}

.box {
  flex: 1;
  background-color: #C5D2FFFF;
  margin-left: 6px;
  margin-top: 12px;

  &:hover {
    cursor: pointer;
  }
}

.box-hover {
  &:hover {
    cursor: pointer;
    background-color: #3665FFFF;
  }
}

.box-select {
  background-color: #3665FFFF;
}
</style>

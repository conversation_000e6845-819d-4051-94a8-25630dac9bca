<template>
  <div class="w-full h-full flex flex-col justify-between">
    <div>
      <div class="flex items-center mb-[16px]">
        <el-input
          v-model="query.keyword"
          clearable
          style="width: 166px; margin-right: 16px"
          placeholder="名称或地址"
          @change="queryFn(query)"
        />
        <!-- <select-by-dict
          v-model="query.usePerm"
          placeholder="权限类型"
          style="width: 166px; margin: 0 16px"
          :reqFn="() => $getDictType('use_perm')"
          :multiple="false"
          :defaultOptions="{ key: 'value', value: 'value', label: 'label' }"
          @change="queryFn(query)"
        /> -->
        <el-button @click="resetFn" style="width: 84px" type="primary" plain
          >重置</el-button
        >
      </div>
    </div>
    <div class="flex-1 box-border flex w-full h-full">
      <div class="flex-1 h-660px">
        <ElementTable
          ref="tableRef"
          :table-title="tableTitle"
          :page-config="pageConfig"
          @selectBox="handleSelectionChange"
          @selectAll="selectAll"
          :data="cameraList"
          row-key="gbId"
        >
          <template #status="{ data: { row } }">
            <div :class="row.onlineFlag ? 'normal' : 'abnormal'">
              {{ row.onlineFlagMark }}
            </div>
          </template>
        </ElementTable>
      </div>

      <!-- 已选清单列表 -->
      <div class="h-full box-border pb-[16px] h-660px flex-shrink-0">
        <div
          class="flex flex-col border border-solid border-[#E5E7EB] rounded-[4px] w-[300px] h-full ml-[16px]"
        >
          <div
            class="flex items-center justify-between border-b border-solid border-[#E5E7EB]"
          >
            <div
              class="w-[140px] h-[40px] bg-[#3665FF] text-[#fff] flex items-center justify-center"
            >
              <span>已选中</span>
              <span>&nbsp; ({{ selectList.length || 0 }})</span>
            </div>
            <div class="mr-[10px] mt-[5px]">
              <el-icon
                @click="clearAllLeftTable2SelectList"
                style="font-size: 20px"
              >
                <Delete />
              </el-icon>
            </div>
          </div>
          <div class="flex flex-col gap-y-[20px] overflow-auto h-full">
            <div
              v-for="(item, index) in selectList"
              :key="index"
              class="flex items-center justify-between px-[10px] h-[40px]"
            >
              <span>{{ item.name }}</span>
              <el-icon
                @click="clearOneLeftTable2SelectList(item)"
                style="font-size: 20px"
              >
                <Delete />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--
    <div class="flex justify-end pb-[36px]">
      <el-button color="#F2F3F5" @click="close">取消</el-button>
      <el-button type="primary" @click="dialogConfirmEve" :loading="loading">
        下一步
      </el-button>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { success } from '@/utils/toast.ts'
import datePicker from '@/components/DatePicker/index.vue'
import { $getDictType } from '@/api/dict'
import { $getCameraList } from '@/api/videoCenter'
import SelectByDict from '@/components/SelectByDict/index.vue'

const props = defineProps({
  startTime: {
    type: String,
    default: ''
  },
  endTime: {
    type: String,
    default: ''
  },
  cameraList: {
    type: Array,
    default: []
  }
})

const emit = defineEmits()

onMounted(() => {
  if (props.cameraList.length) {
    selectList.value = props.cameraList
  }
  getCameraList()
})
// 右侧已选
const tableRef = ref<any>(null)
const clearAllLeftTable2SelectList = () => {
  selectList.value = []
  tableRef.value.clearSelectionFun()
}
const clearOneLeftTable2SelectList = (item: any) => {
  selectList.value = selectList.value.filter((i: any) => i.gbId !== item.gbId)
  setSelectionFn(selectList.value)
  // tableRef.value.toggleRowSelectionFun(item, false)
}
// 查询条件
const queryFn = () => {
  pageConfig.currentPage = 1
  query.current = 1
  query.size = 20
  pageConfig.pageSize = 20
  getCameraList()
}

const resetFn = () => {
  query.keyword = ''
  pageConfig.currentPage = 1
  pageConfig.pageSize = 20
  query.current = 1
  query.size = 20
  getCameraList()
}

// 摄像机列表
const cameraList: any = ref([])
// 获取摄像机列表
const getCameraList = () => {
  $getCameraList({
    ...query,
    startAt: props.startTime,
    endAt: props.endTime
  }).then((res: any) => {
    cameraList.value = res.data.records
    pageConfig.total = res.data.total
    setSelectionFn(selectList.value)
  })
}
// 防范
const setSelectionFn = (rows: any) => {
  if (!Array.isArray(rows)) return
  // 先清除所有选中状态
  tableRef.value.clearSelectionFun()
  // 等待数据更新完成
  nextTick(async () => {
    // 确保表格实例存在
    if (!tableRef.value) return
    // 遍历需要选中的行
    for (const row of rows) {
      if (!row) continue
      // 在表格数据中查找匹配的行
      const matchRow = cameraList.value.filter((item) => item.gbId === row.gbId)
      if (matchRow.length > 0) {
        await tableRef.value.toggleRowSelectionFun(matchRow[0], true)
      }
    }
  })
}
// 选择事件
const selectList = ref<any>([])
const handleSelectionChange = (selection: any, row: any) => {
  console.log('选择的摄像机', selection, row)
  if (!row) {
    if (selection.length === 0) {
      selectList.value = selectList.value.filter(
        (item) => !allList.find((listItem) => listItem.gbId === item.gbId)
      )
    } else {
      const newSelections = selection.filter(
        (item) => !selectList.value.find((it) => it.gbId === item.gbI)
      )
      selectList.value = [...selectList.value, ...newSelections]
    }
  } else {
    if (selection.includes(row)) {
      if (!selectList.value.find((it) => it.gbId === row.gbI)) {
        selectList.value.push(row)
      }
    } else {
      const index = selectList.value.findIndex((it) => it.gbId === row.gbId)
      if (index !== -1) {
        selectList.value.splice(index, 1)
        tableRef.value.clearRowSelection(row, false)
      }
    }
  }
}
const selectAll = (selection: any) => {
  console.log('全部选择', selection)
  if (selection.length === 0) {
    // 取消全选的情况
    selectList.value = selectList.value.filter(
      (item) =>
        !cameraList.value.some((listItem) => listItem.gbId === item.gbId)
    )
  } else {
    // 全选的情况
    const newSelections = selection.filter(
      (item) => !selectList.value.some((it) => it.gbId === item.gbId)
    )
    selectList.value = [...selectList.value, ...newSelections]
  }
}
const tableTitle: Array<Object | any> = [
  {
    type: 'selection'
  },
  {
    label: '名称',
    prop: 'name',
    type: 'text'
  },
  {
    label: '类型',
    prop: 'categoryName',
    type: 'text'
  },
  {
    label: 'IP地址',
    prop: 'gbId',
    type: 'text'
  },
  {
    label: '位点地址',
    prop: 'siteAddress',
    type: 'text'
  },
  {
    label: '权限类型',
    prop: 'usePermMark',
    type: 'text'
  },
  {
    label: '状态',
    prop: 'onlineFlagMark',
    name: 'status',
    type: 'custom'
  }
]

const pageConfig = reactive<any>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => pageChange(e)
})

const table: any = ref()
let orgObj = JSON.parse(localStorage.getItem('userBaseInfo')!).userBaseInfo
const query = reactive<any>({
  keyword: '',
  grantDataIds: [orgObj.orgId],
  current: 1,
  powerSet: true,

  size: 20
})

// 分页事件
const sizeChange = (e: any) => {
  pageConfig.pageSize = e
  query.size = e
  getCameraList()
}
const pageChange = (e: any) => {
  pageConfig.currentPage = e
  query.current = e
  getCameraList()
}
const close = () => {
  emit('cancel')
}

const dialogConfirmEve = () => {
  emit('selectCameraFn', selectList.value)
}
watch(
  () => selectList.value,
  (newVal, oldVal) => {
    emit('selectCameraFn', selectList.value)
  },
  {
    deep: true
  }
)
</script>

<style lang="scss" scoped></style>

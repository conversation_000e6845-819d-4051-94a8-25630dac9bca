<!-- 已绑定通道 -->
<template>
  <dialog-view :title="title?title+'--'+'已配置视频':'已配置视频'" ref="channelDialog" :footer="false" @confirm="channelConfirmAdd" width="80%"
    @cancel=closeDialog>
    <div class="flex-shrink-0 flex items-center h-[66px]">
      <el-input style="width: 166px; margin-right: 20px" v-model="tablePagination.keyword" placeholder="名称或唯一标识" clearable
        @clear="resetSearch" @input="searchNameEve"/>
      <!-- <el-button type="primary" style="width: 84px" @click="searchNameEve">查询</el-button> -->
      <el-button style="width: 84px" @click="resetSearch" type="primary" plain>重置</el-button>
    </div>
    <div class="table-cont w-full h-[600px]" ref="tableRef">

      <table-view :tableData="tableData" :total="tableTotal" :currentPage="tablePagination.current"
        :pageSize="tablePagination.size" @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"
        @handleSelect="handleSelect" v-loading="tableLoading" :loadingBgColor="loadingBgColor">
        <el-table-column prop="name" label="视频名称" width="250">
          <template #default="{ row }">
            <div style="text-align: left;">{{ row.name }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="类型" width="80"/>
        <el-table-column prop="brandName" label="品牌" width="120"/>
        <el-table-column prop="gbId" label="唯一标识" width="200" />
        <el-table-column prop="port" label="端口" width="80"/>
        <el-table-column prop="siteAddress" label="地址" />
        <el-table-column prop="areaName" label="行政区域" width="120"/>
        <el-table-column prop="onlineFlag" label="状态" width="80">
          <template #="{ row }">
            <div style="color: #16d46b" v-if="row.onlineFlag == '在线'">
              在线
            </div>
            <div v-else style="color: #ff4949">离线</div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100px">
          <template #="{ row }">
            <div>
              <el-link class="mr-2" type="primary" @click="goVideSourceManagement(row)">查看</el-link>
            </div>
          </template>
        </el-table-column>
      </table-view>
    </div>
    <!-- 视频详情弹窗 -->
    <video-info ref="showVideoRef"></video-info>
  </dialog-view>
</template>

<script setup>
import DialogView from "@/components/DialogView/index.vue";
import { ref } from "vue";
import TableView from "@/components/tableView/index.vue";
import { useTableData } from "@/hooks/tableData";
import { $getAlgorithmChannelList } from '@/api/ai'
import { useRoute, useRouter } from "vue-router";
import VideoInfo from './videoInfo.vue'

const props = defineProps({
  api: {
    type: Function,
    default: $getAlgorithmChannelList
  },
  title:{
    type: String,
    default: ''
  }
})


const $router = useRouter();

const {
  tableData,
  selectList,
  tableTotal,
  tablePagination,
  getTableData,
  tableDataPage,
  handleSizeChange,
  handleCurrentChange,
  handleSelect,
  tableLoading,
  loadingBgColor,
} = useTableData({
  reqFn: props.api,
  fn: (res) => {
    tableData.value = res.data.records.map((it, idx) => {
      return ({
        ...it,
        index: idx + 1 + (tablePagination.value.current - 1) * tablePagination.value.size,
      })
    });
    tableTotal.value = res.data.total - 0;
  },
});

const channelDialog = ref(null)
const row = ref({})

const open = (row, flag = true) => {
  if(row.algorithmCode) {
    tablePagination.value.algorithmCode = row.algorithmCode
  }
  if(row.taskIndexCode) {
    tablePagination.value.taskIndexCode = row.taskIndexCode
  }
  channelDialog.value.open()
  getTableData()
}

// 搜索
const searchNameEve = () => {
  tablePagination.value.current = 1
  getTableData()
}

// 重置搜索
const resetSearch = () => {
  tablePagination.value.keyword = ''
  getTableData()
}

const showVideoRef = ref(null)

const goVideSourceManagement = (row) => {
  // $router.push({
  //   path: "/ai/videSourceManagement",
  //   query: { 
  //     name: row.name,
  //     gbId: row.gbId
  //    },
  // });
  showVideoRef.value.open(row)
};

const closeDialog = () => {
  tablePagination.value.keyword = ''
}
// 导出方法

defineExpose({
  open
})

</script>

<style lang="scss" scoped></style>
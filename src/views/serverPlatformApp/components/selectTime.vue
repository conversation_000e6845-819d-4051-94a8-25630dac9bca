<template>
  <div class="w-full h-full flex flex-col justify-between">
    <div class="p-[20px] w-full flex-1">
      <div class="flex items-center mb-[16px]">
        <div class="w-[50px]">步骤2:</div>
        <div>配置算法任务识别时间</div>
        <div class="ml-[16px] mr-[6px]">已选择摄像机:</div>
        <div>{{ props.cameraList.length }}个</div>
        <div class="ml-[16px] mr-[6px]">已选择算法服务:</div>
        <div>{{ props.algorithm.length }}个</div>
      </div>
      <el-form
        hide-required-asterisk
        ref="formRef"
        :model="formData"
        label-width="100px"
        inline
        :rules="rules"
      >
        <el-form-item style="width: 100%">
          <div class="w-full">
            <el-tabs
              v-model="changeAlgorithmItem"
              type="card"
              class="px-[10px]"
              :closable="title != '查看任务'"
              @tab-remove="removeTab"
              @tab-click="handleTabClick"
            >
              <el-tab-pane
                v-for="item in formData.algorithmPresets"
                :key="item.algorithmCode"
                :label="item.algorithmName"
                :name="item.algorithmCode"
              >
                <!-- {{ item }} -->
              </el-tab-pane>
            </el-tabs>
            <div style="position: relative; padding: 10px">
              <!-- <time-select-dialog ref="timeSelectDialogRef"
                              v-model:weeklyPlanid="item.weeklyPlanid"
                              :key="new Date().getTime()"></time-select-dialog> -->
              <!-- <div class="yaopanBtn">
                是否需要研判
                <el-switch
                  v-model="activeItemTask.needExamine"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :active-value="1"
                  :inactive-value="0"
                  :disabled="title == '查看任务'"
                ></el-switch>
              </div> -->
              <NewTimeSelect
                ref="timeSelectDialogRef"
                :key="new Date().getTime()"
                :disabled="title == '查看任务'"
                v-model:weeklyPlanid="activeItemTask.weeklyPlanId"
                v-model:customWeeklyPlanid="activeItemTask.customWeeklyPlanid"
              />
              <div
                v-if="!formData.algorithmPresets?.length > 0"
                class="w-full h-full model-container"
              ></div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex justify-end pb-[36px] box-border">
      <el-button color="#F2F3F5" @click="close">取消</el-button>
      <el-button type="primary" @click="dialogConfirmEve" :loading="loading"
        >提交
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { success, warning } from '@/utils/toast.ts'

import NewTimeSelect from './newTimeSelect.vue'
import {
  $addAlgorithmTask,
  $editAlgorithmTask,
  $getAlgorithmTaskRelateAis
} from '@/api/ai'

const props = defineProps({
  title: {
    type: String,
    default: '新增任务'
  },
  data: {
    //编辑回填的数据
    type: Object,
    default: null
  },
  videoList: {
    //默认选中的视频
    type: Object,
    default: null
  },
  algorithm: {
    //默认选中的算法
    type: Array,
    default: {}
  },
  item: {
    type: Object,
    default: null
  },
  cameraList: {
    //默认选中的算法
    type: Array,
    default: {}
  }
})
// 选择的摄像机与算法
const selectList = ref([])
const close = () => {
  $emit('cancel')
}

const dialogConfirmEve = () => {
  let arr = formData.value.algorithmPresets.filter(
    (item) => item.weeklyPlanId == 'add'
  )
  if (arr.length > 0) {
    return warning('请保存自定义设置后提交')
  }
  // formData.value.algorithmPresets
  $emit('selectTimeFn', formData.value)
}

const btnLoading = ref(false)

const algorithmList = ref([])

const formData = ref({})
const formRef = ref(null)
const timeSelectDialogRef = ref()
const selectAlgorithmDialog = ref()
const selectVideoDialog = ref()

const dialogVisible = ref(false)
const changeAlgorithmItem = ref('')

watch(
  () => props.videoList,
  (val) => {
    if (val?.camera?.gbId) {
      formData.value = props.videoList
      console.log('videoList', val.camera.gbId)
      queryAlgorithmByCameraId(val?.camera?.gbId)
    } else {
      formData.value = {}
    }
  },
  {
    immediate: true,
    deep: true
  }
)
watch(
  () => props.algorithm,
  (val) => {
    if (val) {
      let arr = val.map((item) => {
        return {
          weeklyPlanId: item.weeklyPlanId || '1',
          algorithmName: item.apiServerName,
          algorithmCode: item.apiServerCode
        }
      })
      formData.value = {
        taskName: '',
        algorithmPresets: arr || [],
        camera: {
          gbId: '',
          name: ''
        }
      }

      console.log('props.algorithm', val, props.algorithm, formData.value)
    } else {
      formData.value = {}
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => props.data,
  (val) => {
    if (val) {
      formData.value = {
        taskName: val.taskName,
        camera: {
          gbId: val.cameraindexCode,
          name: val.cameraindexName
        },
        algorithmPresets:
          val.algorithmPresetsVos?.map((item) => {
            return { ...item, weeklyPlanId: item.weeklyPlanid }
          }) || []
      }
      console.log('props.data', props.data)
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const activeItemTask = ref({})
watch(
  () => formData.value?.algorithmPresets,
  (val, oldval) => {
    if (val?.length == oldval?.length) return
    // if(props.title != '新增任务') return
    let tabs = val?.map((item) => item.algorithmCode) || []
    if (!tabs.includes(changeAlgorithmItem.value)) {
      changeAlgorithmItem.value = tabs[0]
    } else {
      changeAlgorithmItem.value = ''
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => changeAlgorithmItem.value,
  (val) => {
    if (changeAlgorithmItem.value) {
      if (activeItemTask.value.algorithmCode) {
        formData.value.algorithmPresets = formData.value.algorithmPresets.map(
          (item) => {
            if (item.algorithmCode === activeItemTask.value.algorithmCode) {
              item = activeItemTask.value
              console.log('选中数据', activeItemTask.value)
            }
            return item
          }
        )
      }
      let temp = formData.value.algorithmPresets.find(
        (item) => item.algorithmCode === val
      )

      activeItemTask.value = temp
      activeItemTask.value.customWeeklyPlanid =
        activeItemTask.value.weeklyPlanId !== '1' &&
        activeItemTask.value.weeklyPlanId !== '2'
          ? activeItemTask.value.weeklyPlanId
          : ''
      console.log('处理完成后的数据', activeItemTask.value)
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const rules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  camera: [{ required: true, message: '请选择视频', trigger: 'blur' }],
  algorithmPresets: [{ required: true, message: '请选择算法', trigger: 'blur' }]
}

//确认选择的算法
function confirmSelectAlgorithm(data) {
  let addData = data.map((item) => {
    return {
      algorithmCode: item.apiServerCode,
      algorithmName: item.apiServerName,
      vendor: item.vendor || '',
      weeklyPlanId: '1',
      customWeeklyPlanid: ''
    }
  })

  // formData.value.algorithmPresets = [...formData.value?.algorithmPresets||[],...addData]
  formData.value.algorithmPresets = addData
}

//确认选择的视频
function confirmSelectVideo(data) {
  formData.value.camera = data.value
  // if (!formData.value.taskName) {
  formData.value.taskName = data.value.name + '任务'
  // }
  queryAlgorithmByCameraId(data.value.gbId)
}

const flag = ref(false)

const showName = computed(() => {
  console.log('props.item', props.item, !props.item)
  if (!props.item) return props.title
  let temp = ''
  if (props.item.taskName) {
    temp = props.item.taskName
  } else if (props.item.name) {
    temp = props.item.name
  } else {
    return props.title
  }
  // return temp + "--" + props.title
  return temp
})

//查询视频已绑定算法
function queryAlgorithmByCameraId(code) {
  $getAlgorithmTaskRelateAis({ code }).then((res) => {
    if (res.code == 0) {
      // formData.value.algorithmPresets =
      console.log('已绑定算法', res.data)
      if (!res.data) {
        //无任务
        flag.value = false
        return
      } else {
        //有任务
        flag.value = true
      }
      let ids = res.data.algorithmPresetsVos.map((item) => item.algorithmCode)
      let algorithmPresets = []
      if (formData.value.algorithmPresets) {
        algorithmPresets = formData.value.algorithmPresets.filter(
          (item) => !ids.includes(item.algorithmCode)
        )
      }
      formData.value = {
        taskName: res.data.taskName || '',
        taskIndexCode: res.data.taskIndexCode || '',
        camera: {
          gbId: res.data.cameraindexCode || '',
          name: res.data.cameraindexName || ''
        },
        algorithmPresets: [
          ...algorithmPresets,
          ...(res.data.algorithmPresetsVos?.map((item) => {
            return { ...item, weeklyPlanId: item.weeklyPlanid }
          }) || [])
        ]
      }
    }
  })
}

//删除选中算法
function removeTab(tabName) {
  formData.value.algorithmPresets = formData.value.algorithmPresets.filter(
    (item) => item.algorithmCode !== tabName
  )
}

//点击tab
function handleTabClick(tab, event) {}

const $emit = defineEmits()

//保存
async function handleSave() {
  if (!formRef.value) return
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      console.log(formData.value)
      let userId = JSON.parse(localStorage.getItem('userInfo')).user_id
      let option = {
        taskName: formData.value.taskName,
        presetsDto: {
          cameraIndexCode: formData.value.camera.gbId,
          cameraindexName: formData.value.camera.name
        },
        taskIndexCode: formData.value?.taskIndexCode || '',
        algorithmPresets: formData.value.algorithmPresets.map((item) => {
          if (item.weeklyPlanId == 'add') {
            item.weeklyPlanId = '1'
          }
          return item
        }),
        userIds: [userId]
      }
      console.log(option)
      btnLoading.value = true
      if (props.title == '新增任务' && flag.value == false) {
        $addAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('新增成功')
            btnLoading.value = false
            dialogVisible.value = false
            $emit('handleSave')
            formData.value = {}
            activeItemTask.value = {}
          } else {
            btnLoading.value = false
          }
        })
      } else {
        console.log('编辑任务', props.data, props.item)
        if (!option.taskIndexCode) {
          option.taskIndexCode = props.data.taskIndexCode
        }
        $editAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('编辑成功')
            btnLoading.value = false
            dialogVisible.value = false
            $emit('handleSave')
            formData.value = {}
            activeItemTask.value = {}
          } else {
            btnLoading.value = false
          }
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

//保存并启动
async function handleTaskAddStart() {
  if (!formRef.value) return
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      console.log(formData.value)
      let userId = JSON.parse(localStorage.getItem('userInfo')).user_id
      let option = {
        taskName: formData.value.taskName,
        presetsDto: {
          cameraIndexCode: formData.value.camera.gbId,
          cameraindexName: formData.value.camera.name
        },
        algorithmPresets: formData.value.algorithmPresets,
        userIds: [userId]
      }
      console.log(option)
      if (props.title == '新增任务' && flag.value == false) {
        $addAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('新增成功')
            dialogVisible.value = false
            // $emit('handleSave')
            $emit('handleStart', { taskIndexCode: res.data })
            formData.value = {}
            activeItemTask.value = {}
          }
        })
      } else {
        option.taskIndexCode = props.data.taskIndexCode
        $editAlgorithmTask(option).then((res) => {
          if (res.code == 0) {
            console.log('新增成功')
            dialogVisible.value = false
            // $emit('handleSave')
            $emit('handleStart', props.data)
            formData.value = {}
            activeItemTask.value = {}
          }
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

//弹窗开启
function open() {
  dialogVisible.value = true
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.container {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #3665ff;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-decoration-line: underline;
  text-transform: none;

  &:hover {
    cursor: pointer;
  }
}

.model-container {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: no-drop;
}

.yaopanBtn {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 999;
}
</style>

<template>
  <div class="flex flex-col justify-between h-full">
    <div class="flex flex-col justify-between flex-1">
      <div>
        <div class="flex items-center mb-[32px]">
          <div class="w-[50px]">步骤1:</div>
          <div>任务信息</div>
        </div>
        <el-form
          :model="formData"
          label-width="120px"
          ref="formRefTwo"
          :rules="rules"
          inlne
          style="max-width: 800px"
        >
          <!-- <el-form-item label="创建组织">
          <span>{{ orgName }}</span>
        </el-form-item>
        <el-form-item label="使用组织">
          <span>{{ orgName }}</span>
        </el-form-item> -->
          <div class="flex">
            <el-form-item label="任务名称" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入任务名称"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="任务有效期" prop="endTime">
              <!-- <datePicker
                v-model:startAt="formData.startTime"
                v-model:endAt="formData.endTime"
                :showStartAfterTime="true"
                :showEndAfterTime="true"
                :disabledStartTime="true"
                class="mr-2"
              /> -->
              <div class="df">
                <el-date-picker
                  v-model="formData.startTime"
                  type="datetime"
                  @change="handelChangeStart"
                  placeholder="请选择日期"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  ref="startAtRef"
                  :disabled-date="disabledDateStart"
                  :disabled="true"
                />
                <div class="mx-[5px] bg-transparent">-</div>
                <el-date-picker
                  v-model="formData.endTime"
                  type="datetime"
                  @change="handelChangeStart"
                  placeholder="请选择日期"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  ref="endAtRef"
                  :disabled-date="disabledDateEnd"
                />
              </div>
            </el-form-item>
          </div>

          <!-- <el-form-item label="事件订阅:" prop="endTime">
          <el-checkbox-group
            class="flex flex-wrap W-[300px]"
            v-model="formData.subscribe"
          >
            <el-checkbox label="短信通知" />
            <el-checkbox label="邮件" />
            <el-checkbox label="消息队列" />
            <el-checkbox label="https" />
            <el-checkbox label="站内消息" />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="订阅接收人:" prop="endTime">
          <el-button type="primary">选择用户</el-button>
        </el-form-item> -->
        </el-form>
      </div>

      <div class="flex-1 flex flex-col justify-between">
        <el-tabs v-model="changeAlgorithmItem" type="card" class="px-[10px]">
          <el-tab-pane
            v-for="item in changeTabList"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          >
            <!-- {{ item }} -->
          </el-tab-pane>
        </el-tabs>
        <div class="flex-1">
          <div
            v-if="formData.startTime && formData.endTime"
            class="w-full h-full"
          >
            <selectCamera
              @selectCameraFn="selectCameraFn"
              :startTime="formData.startTime"
              :endTime="formData.endTime"
              :cameraList="cameraValue"
              v-if="changeAlgorithmItem == '1'"
            ></selectCamera>
            <selectAlgorithm
              @selectAlgorithmFn="selectAlgorithmFn"
              :startTime="formData.startTime"
              :endTime="formData.endTime"
              :algorithmList="algorithmValue"
              v-if="changeAlgorithmItem == '2'"
            ></selectAlgorithm>
          </div>
          <div v-else class="w-full h-full flex items-center justify-center">
            <span>请选择任务有效期</span>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-end pb-[36px] box-border">
      <el-button color="#F2F3F5" @click="close">取消</el-button>
      <el-button type="primary" @click="dialogConfirmEve" :loading="loading">
        下一步
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { success, warning } from '@/utils/toast.ts'
import datePicker from '@/components/DatePicker/index.vue'
import selectCamera from './selectCamera.vue'
import selectAlgorithm from './selectAlgorithm.vue'
import { format } from '@/utils/dayjs.ts'
import dayjs from 'dayjs'

const props = defineProps({
  appFrom: {
    type: Object,
    default: {}
  }
})

const formData = ref<Object>({
  startTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
})

const orgName = ref<String>('')

const formRefTwo = ref<any>(null)

const emit = defineEmits()
const changeAlgorithmItem = ref<any>('1')
const changeTabList = [
  {
    label: '视频',
    value: '1'
  },
  {
    label: '算法服务',
    value: '2'
  }
]

const rules = ref<any>({
  title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  endTime: [{ required: true, message: '请选择任务有效期', trigger: 'blur' }]
})

const loading = ref<boolean>(false)

// 时间选择相关
const handelChangeStart = (val: any) => {
  console.log('时间问题', formData.value)
}
const disabledDateStart = (date) => {
  return new Date(date).getTime() > new Date(formData.value.endTime).getTime()
}

const disabledDateEnd = (date) => {
  return new Date(date).getTime() < new Date(formData.value.startTime).getTime()
}

const close = () => {
  emit('cancel')
}
const cameraValue = ref<any>([])
const selectCameraFn = (val: any) => {
  console.log('时间问题', formData.value)
  cameraValue.value = val
}
const algorithmValue = ref<any>([])
const selectAlgorithmFn = (val: any) => {
  algorithmValue.value = val
}
const dialogConfirmEve = () => {
  formRefTwo.value.validate(async (valid: boolean) => {
    if (valid) {
      if (!cameraValue.value.length) {
        return warning('请选择摄像头')
      }
      if (!algorithmValue.value.length) {
        return warning('请选择算法服务')
      }
      let obj = {
        cameraValue: cameraValue.value,
        algorithmValue: algorithmValue.value,
        infoValue: formData.value
      }
      emit('taskInformation', obj)
    }
  })
}
const blean = ref<any>(true)
onMounted(() => {
  // console.log('声明周期')
  // if (props.appFrom) {
  //   formData.value.title = props.appFrom.taskName
  //   formData.value.startTime = props.appFrom.startTime
  //   formData.value.endTime = props.appFrom.endTime
  // }
  // // orgName.value = JSON.parse(
  // //   localStorage.getItem('userBaseInfo')
  // // ).userBaseInfo.orgName
  // // nextTick(() => {
  // //   formData.value.startTime = format(new Date(), 'YYYY-MM-DD HH:mm:ss')
  // //   console.log('开始时间', formData.value.startTime)
  // // })
})
watch(
  () => props.appFrom,
  (newVal, oldVal) => {
    if (newVal.taskIndexCode) {
      formData.value.title = props.appFrom.taskName
      formData.value.startTime = props.appFrom.startTime
      formData.value.endTime = props.appFrom.endTime
      cameraValue.value = newVal.taskVideoInfoVoList.map((item) => {
        return {
          gbId: item.cameraCode,
          name: item.cameraName
        }
      })
      algorithmValue.value = newVal.algorithmPresetsVos.map((item) => {
        return {
          apiServerName: item.algorithmName,
          apiServerCode: item.algorithmCode,
          weeklyPlanId: item.weeklyPlanid
        }
      })
    } else {
      formData.value.title = ''
      formData.value.startTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      formData.value.endTime = ''
      console.log('打印时间', formData.value)
    }
    changeAlgorithmItem.value = '1'
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<style lang="scss" scoped></style>

<template>
  <el-drawer
    v-model="showInfo"
    :title="infoDetailValue?.taskName + '算法服务-详情'"
    direction="rtl"
    size="80%"
    :close-on-click-modal="false"
    @open="openDrawer"
  >
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border pt-0"
    >
      <div class="flex-shrink-0 mb-[16px]">
        <div class="w-full flex flex-col">
          <!-- <div class="flex items-center mb-[16px]">
            <div class="flex items-center mr-[260px]">
              <p class="w-[80px]">创建组织:</p>
              <p>*********</p>
            </div>
            <div class="flex items-center">
              <p class="w-[80px]">使用组织:</p>
              <p>*********</p>
            </div>
          </div> -->
          <div class="flex items-center mb-[16px]">
            <div class="flex items-center mr-[260px]">
              <p class="w-[80px]">任务名称:</p>
              <p>{{ infoDetailValue?.taskName }}</p>
            </div>
            <div class="flex items-center">
              <p class="w-[80px]">任务有效期:</p>
              <p>
                {{ infoDetailValue.startTime }} 至 {{ infoDetailValue.endTime }}
              </p>
            </div>
          </div>
        </div>
        <div class="flex align-center">
          <el-input
            style="width: 300px; margin-right: 10px"
            v-model="query.keyWord"
            placeholder="摄像机名称或服务名称"
            clearable
            @change="getData"
          >
          </el-input>
          <!-- <el-select
            v-model="query.type"
            placeholder="状态"
            class="mr-2"
            style="width: 166px"
          >
            <el-option
              v-for="item in menuData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
          <div>
            <el-button type="" @click="resetEve">重置</el-button>
          </div>
        </div>
      </div>
      <div class="flex-1">
        <ElementTable
          ref="table"
          :data="tableData"
          :table-title="tableTitle"
          :page-config="pageConfig"
          @selectChange="handleSelectionChange"
        >
          <template #videoName="{ data: { row } }">
            <div
              style="
                color: #3665ff;
                text-decoration: underline;
                cursor: pointer;
              "
              @click="openVideo(row)"
            >
              {{ row.cameraName }}
            </div>
          </template>
          <template #roleList="{ data: { row } }">
            <div
              v-if="row.eventCount !== '0'"
              style="
                color: #3665ff;
                text-decoration: underline;
                cursor: pointer;
              "
              @click="clickRunDia(row)"
            >
              {{ row.eventCount }}
            </div>
            <div v-else>无</div>
          </template>

          <template #runStatus="{ data: { row } }">
            <span style="color: #e23e31" v-if="row.runStatus != 1">已结束</span>
            <span style="color: #85c5ad" v-else>分析中</span>
          </template>
        </ElementTable>
      </div>
    </div>
    <!-- 区域划分编辑 -->
    <template #footer>
      <div class="dialog-footer pb-[36px]">
        <el-button type="primary" @click="dialogConfirmEve"> 确定 </el-button>
      </div>
    </template>
    <regionalDivision v-model:showRegional="showRegional"></regionalDivision>
    <!-- 视频详情弹窗 -->
    <video-info ref="videoInfoRef" />
    <runDia v-model:showRun="showRun" :runDetailValue="runDetailValue"></runDia>
  </el-drawer>
</template>

<script setup lang="ts">
import runDia from './runDia.vue'
import videoInfo from './videoInfo.vue'
import { $applyAuth } from '@/api/order/index.ts'
import { PageConfigType } from '@/components/ElementTable/index.vue'
import { success } from '@/utils/toast.ts'
import regionalDivision from './regionalDivision.vue'
import { $getSubTaskList } from '@/api/addTask/index'

const props = defineProps({
  infoDetailValue: {
    type: Object,
    default: () => {}
  }
})
// 重置
const resetEve = () => {
  query.value.keyWord = ''
  query.value.current = 1
  query.value.size = 20
  pageConfig.value.currentPage = 1
  pageConfig.value.pageSize = 20
  getData()
}
const getData = () => {
  $getSubTaskList(query.value).then((res: any) => {
    if (res.code === 0) {
      tableData.value = res.data.records.map((item) => {
        item.runStatus = props.infoDetailValue.runStatus
        item.serverName = '算法服务'
        return item
      })
      pageConfig.value.total = +res.data.total
    }
  })
}
// 事件记录
const showRun = ref<boolean>(false)
const runDetailValue = ref<any>({})
const clickRunDia = (val: any) => {
  // runDetailValue.value.taskIndexCode = props.infoDetailValue.taskIndexCode
  runDetailValue.value.taskName = val.algorithmName
  runDetailValue.value.subtaskId = val.id
  // runDetailValue.value.apiServerCode = val.aiTaskCode

  showRun.value = true
}
// 打开视频
const videoInfoRef = ref(null)
const openVideo = (params) => {
  videoInfoRef.value.open({ gbId: params.cameraCode })
}
// 区域划分
const showRegional = ref<any>(false)
// 表格部分
const handleSelectionChange = () => {}
const tableData = ref<any>([])

const tableTitle: Array<Object | any> = [
  {
    type: 'index'
  },
  {
    label: '视频名称',
    name: 'videoName',
    type: 'custom'
  },
  {
    label: '视频IP',
    prop: 'cameraCode',
    type: 'text'
  },
  // {
  //   label: '视频位置',
  //   prop: 'phone',
  //   type: 'text'
  // },
  {
    label: '服务名称',
    prop: 'algorithmName',
    type: 'text'
  },
  {
    label: '服务类型',
    prop: 'serverName',
    type: 'text'
  },
  {
    label: '任务状态',
    type: 'custom',
    name: 'runStatus'
  },
  {
    label: '产生事件数量',
    prop: 'roleName',
    type: 'custom',
    name: 'roleList'
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '算力重点区域绘制',
        click: (row: any) => {
          console.log('获取数据', row)
          goRegionalFn(row)
        }
      },
      // {
      //   isLink: true,
      //   type: 'primary',
      //   name: '运行日志'
      // },
      {
        isLink: true,
        type: 'danger',
        name: '删除'
      }
    ]
  }
]

const pageConfig = ref<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 0,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})
// 区域划分
const goRegionalFn = (row: any) => {
  showRegional.value = true
}

/**
 * 每条页数改变方法
 * @param e 每页条数
 */
const sizeChange = (e: any) => {
  pageConfig.value.pageSize = e
  query.value.size = e
  getData()
}

/**
 * 页码改变方法
 * @param e 页数
 */
const cuttentChange = (e: any) => {
  pageConfig.value.currentPage = e
  query.value.current = e
  getData()
}
// 赛选
const query = ref<any>({
  keyWord: '',
  // type: '',
  current: 1,
  size: 20,
  localTaskCode: ''
})
const menuData = ref<Array<any>>([
  {
    value: '1',
    label: '在线'
  },
  {
    value: '2',
    label: '离线'
  }
])

const showInfo = defineModel('showInfo', {
  type: Boolean,
  default: false
})

const dialogConfirmEve = () => {
  showInfo.value = false
}

const openDrawer = () => {
  query.value.localTaskCode = props.infoDetailValue.taskIndexCode
  getData()
}
</script>

<style lang="scss" scoped></style>

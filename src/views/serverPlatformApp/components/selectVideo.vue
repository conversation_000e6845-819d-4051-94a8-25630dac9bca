<template>
  <dialog-view ref="dialogView" title="选择摄像机" width="70%" @confirm="confirmEve" @cancel="cancelEve">
      <div class="h-[600px] df fdc">
          <div class="df w-[320px]">
              <el-input placeholder="名称、标识或地址" class="mr-[16px]" v-model="tablePagination.keyword"
                  clearable @input="clickSearch"></el-input>
              <!-- <el-button type="primary" @click="clickSearch" style="width: 84px;">查询</el-button> -->
              <el-button @click="resetTable" style="width: 84px;" type="primary" plain>重置</el-button>
          </div>
          <table-view class="mt-[16px]" :tableData="tableData" :total="tableTotal"
              :currentPage="tablePagination.current" :pageSize="tablePagination.size"
              @handleCurrentChange="handleCurrentChange" @handleSelectionChange="handleSelect" style="padding: 0;" @handleSizeChange="handleSizeChange"
              @handleTableCurrentChange="getCurrentRow">
              <el-table-column label="选择" width="55">
                  <template #default="scope">
                      <el-radio v-model="algorithm" :label="scope.row"><i></i></el-radio>
                  </template>
              </el-table-column>
              <!-- <el-table-column type="index" label="序号" width="60" /> -->
              <el-table-column prop="name" label="名称" width="300" />
              <el-table-column prop="brandName" label="厂商" width="200" />
              <el-table-column prop="gbId" label="标识" />
              <el-table-column prop="siteAddress" label="地址" />
              <el-table-column prop="count" label="已配置算法" width="140">
                  <template #default="{ row }">
                      <div class="" style="color: #3665FF; text-decoration: underline; cursor: pointer;"
                          @click="openCountEve(row)">
                          {{ row.algorithmTotal }}
                      </div>
                  </template>
              </el-table-column>
              <el-table-column prop="onlineFlagMark" label="状态" width="100">
                  <template #default="{ row }">
                      <div style="color: #00D97EFF" v-if="row.onlineFlagMark === '在线'">在线</div>
                      <div v-else style="color: #FF4949">离线</div>
                  </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                  <template #="{ row }">
                      <div class="mr-2">
                          <el-link class="mr-2" type="primary" @click="handleClick(row)">查看</el-link>
                      </div>
                  </template>
              </el-table-column>
          </table-view>
      </div>
  </dialog-view>
  <!-- 绑定算法弹窗 -->
  <binding-algorithm ref="bindingAlgorithmRef" />
  <!-- 视频详情弹窗 -->
  <video-info ref="showVideoRef"></video-info>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import TableView from "@/components/tableView/index.vue";
import DialogView from "@/components/DialogView/index.vue";
import { useTableData } from "@/hooks/tableData";
import { $getCameraList } from '@/api/videoCenter'
import bindingAlgorithm from "./bindingAlgorithm.vue";
import VideoInfo from './videoInfo.vue'

const dialogView = ref(null);

const $emit = defineEmits(['handleConfirm'])

// 左 Table
const {
  tableData,
  selectList,
  tableTotal,
  tablePagination,
  addReqParams,
  getTableData,
  handleSizeChange,
  handleCurrentChange,
  handleSelect,
  tableLoading,
  resetTable,
  loadingBgColor,
} = useTableData({
  reqFn: $getCameraList,
  fn: (res) => {
      const data = res.data.records
      // data中过滤掉在rightTreeData已经存在的
      tableData.value = data
      tableTotal.value = parseInt(res.data.total);
  },
});

function clickSearch() {
  getTableData()
}

// 单选
const algorithm = ref()
const getCurrentRow = (row) => {
  algorithm.value = row
}

tablePagination.value = {...tablePagination.value, curLevel:false, grant: true, areaTreeId: '00001'}

getTableData()

const confirmEve = () => {
  $emit('handleConfirm', algorithm)
  dialogView.value.close()
}


function open() {
  dialogView.value.open()
}
const bindingAlgorithmRef = ref(null)
// 算法路数点击
const openCountEve = (row) => {
  bindingAlgorithmRef.value.open(row.sn)
}

const showVideoRef = ref(null)
// 预览
const handleClick = (row) => {
  showVideoRef.value.open(row)
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
<template>
  <el-drawer
    v-model="showRun"
    :title="runDetailValue?.taskName + '算法服务-事件记录'"
    direction="rtl"
    size="70%"
    :close-on-click-modal="false"
    @open="openDrawer"
  >
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border pt-0"
    >
      <div class="flex flex-shrink-0 justify-between mb-[16px]">
        <div class="flex align-center">
          <el-input
            style="width: 300px; margin-right: 10px"
            v-model="query.keyword"
            placeholder="事件名称或发生位置或视频名称"
            clearable
            @change="getData"
          >
          </el-input>
          <!-- <el-select
            v-model="query.type"
            placeholder="唯一标识"
            class="mr-2"
            style="width: 166px"
          >
            <el-option
              v-for="item in menuData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
          <div>
            <el-button type="" @click="resetEve">重置</el-button>
          </div>
        </div>
        <div>
          <el-button type="">导出</el-button>
        </div>
      </div>
      <div class="flex-1 overflow-hidden">
        <ElementTable
          ref="table"
          :data="tableData"
          :table-title="tableTitle"
          :page-config="pageConfig"
          @selectChange="handleSelectionChange"
        >
          <template #roleList="{ data: { row } }">
            <el-tag v-for="it in row.roleList">
              {{ it.roleName }}
            </el-tag>
          </template>
          <template #videoName="{ data: { row } }">
            <div
              style="
                color: #3665ff;
                text-decoration: underline;
                cursor: pointer;
              "
              @click="openVideo(row)"
            >
              {{ row.deviceName }}
            </div>
          </template>
        </ElementTable>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer pb-[36px]">
        <el-button type="primary" @click="dialogConfirmEve"> 确定 </el-button>
      </div>
    </template>

    <!-- 视频详情弹窗 -->
  </el-drawer>
  <!-- 事件详情弹窗 -->
  <dialog-view
    ref="infoRef"
    width="90vw"
    :title="infoData?.eventName"
    :footer="false"
  >
    <div class="w-full df overflow-hidden items-center">
      <div
        class="flex justify-between h-[70vh] bg-[#D9D9D9] mr-[25px] left"
        style="aspect-ratio: 16 / 9 !important"
      >
        <!-- <img :src="imgUrl +'/oss/file/preview?fileName='+ infoData.rectDrewPicUrl"
                        alt="" style="width: 100%; height: 100%;"/> -->
        <preview-img
          style="width: 100%; height: 100%"
          :name="infoData?.rectDrewPicUrl"
          fit="fill"
        />
      </div>
      <div
        class="flex justify-between flex-1 fdc right h-[70vh] overflow-hidden"
      >
        <div class="h-[62px] fn header df w-full jcsb overflow-hidden">
          <div
            class="header truncate px-2 truncate w-full overflow-hidden"
            :title="infoData?.deviceName"
          >
            {{ infoData?.deviceName }}
          </div>
          <!-- <div class="header px-[10px]">
                            <el-button type="primary" :icon="ArrowLeft" @click="goBack">返回</el-button>
                        </div> -->
        </div>
        <div class="f1 body">
          <el-scrollbar>
            <div class="item">
              <div class="title">事件时间</div>
              <div class="truncate">{{ infoData?.happenTime }}</div>
            </div>
            <div class="item">
              <div class="title">事件名称</div>
              <div>{{ infoData?.eventName }}</div>
            </div>
            <div class="item">
              <div class="title">视频名称</div>
              <div>{{ infoData?.deviceName }}</div>
            </div>
            <div class="item">
              <div class="title">经纬度</div>
              <div>{{ infoData?.longitude }},{{ infoData?.latitude }}</div>
            </div>
            <div class="item">
              <div class="title">唯一标识</div>
              <div>{{ infoData?.deviceIndex }}</div>
            </div>
            <div class="item">
              <div class="title">算法名称</div>
              <div>{{ infoData?.algorithmName }}</div>
            </div>
            <!-- <div class="item">
                                <div class="title">算法类型</div> -->
            <!-- <div style="word-break: break-word;">{{ data.algorithmCode }}</div> -->
            <!-- <div style="word-break: break-word;">城管类</div>
                            </div> -->
            <div class="item">
              <div class="title">厂商名称</div>
              <div>{{ infoData?.factoryName }}</div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </dialog-view>
  <video-info ref="videoInfoRef" />
</template>

<script setup lang="ts">
import videoInfo from './videoInfo.vue'
import { $applyAuth } from '@/api/order/index.ts'
import { PageConfigType } from '@/components/ElementTable/index.vue'
import previewImg from '@/components/PreviewImg/index.vue'
import DialogView from '@/components/DialogView/index.vue'
import { success } from '@/utils/toast.ts'

import { $getEventList } from '@/api/addTask/index'
const props = defineProps({
  runDetailValue: {
    type: Object,
    default: () => {}
  }
})
const getData = () => {
  $getEventList(query.value).then((res: any) => {
    if (res.code === 0) {
      tableData.value = res.data.records
      pageConfig.value.total = +res.data.total
    }
  })
}
// 打开视频
const videoInfoRef = ref(null)
const openVideo = (params) => {
  videoInfoRef.value.open({ gbId: params.deviceIndex })
}
// 事件详情部分
const infoRef = ref(null)
const infoData = ref(null)
const handleLook = (data: any) => {
  infoData.value = data
  infoRef.value.open(data)
}
// 重置
const resetEve = () => {
  query.value.keyword = ''
  query.value.current = 1
  query.value.size = 20
  pageConfig.value.currentPage = 1
  pageConfig.value.pageSize = 20
  getData()
}
// 选择
const handleSelectionChange = () => {}
// 表格部分
const tableData = ref<any>()
const tableTitle: Array<Object | any> = [
  // {
  //   type: 'selection'
  // },
  {
    label: '事件名称',
    prop: 'eventName',
    type: 'text'
  },
  {
    label: '发生时间',
    prop: 'sendTime',
    type: 'text'
  },
  // {
  //   label: '视频',
  //   prop: 'deviceName',
  //   type: 'text'
  // },
  {
    label: '视频名称',
    name: 'videoName',
    type: 'custom'
  },
  {
    label: '唯一标识',
    prop: 'eventId',
    type: 'text'
  },
  {
    label: '位点',
    prop: 'address',
    type: 'text'
  },
  {
    label: '发生位置',
    prop: 'siteAddress',
    type: 'custom',
    name: 'roleList'
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '详情',
        click: (data: any) => {
          console.log(data)
          handleLook(data)
        }
      }
    ]
  }
]

const pageConfig = ref<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})

/**
 * 每条页数改变方法
 * @param e 每页条数
 */
const sizeChange = (e: any) => {
  pageConfig.value.pageSize = e
  query.value.size = e
  getData()
}

/**
 * 页码改变方法
 * @param e 页数
 */
const cuttentChange = (e: any) => {
  pageConfig.value.currentPage = e
  query.value.current = e
  getData()
}
// 赛选
const query = ref<any>({
  keyword: '',
  // type: '',
  current: 1,
  size: 20
})
const menuData = ref<Array<any>>([])

const showRun = defineModel('showRun', {
  type: Boolean,
  default: false
})

const dialogConfirmEve = () => {
  showRun.value = false
}

const openDrawer = () => {
  if (props.runDetailValue?.taskIndexCode) {
    query.value.taskIndexCodes = [props.runDetailValue.taskIndexCode]
  }
  if (props.runDetailValue?.apiServerCode) {
    query.value.algorithmCode = props.runDetailValue.apiServerCode
  }
  if (props.runDetailValue?.subtaskId) {
    query.value.subtaskId = props.runDetailValue.subtaskId
  }

  query.value.keyword = ''
  query.value.current = 1
  query.value.size = 20
  pageConfig.value.currentPage = 1
  pageConfig.value.pageSize = 20
  getData()
}
</script>

<style lang="scss" scoped>
.header {
  text-align: center;
  line-height: 62px;
  color: #333333;
  font-size: 20px;
  font-weight: bold;
  background: rgba(36, 104, 255, 0.16);
}

.body {
  padding: 32px 8px 32px 28px;
  background: rgba(36, 104, 255, 0.04);

  .item {
    margin-bottom: 24px;
    font-weight: 400;
    font-size: 16px;
    color: #333333;

    & > .title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      margin-bottom: 8px;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -8px;
        transform: translate(-100%, -50%);
        height: 8px;
        width: 8px;
        background: #3665ff;
        border-radius: 50%;
      }
    }
  }
}
</style>

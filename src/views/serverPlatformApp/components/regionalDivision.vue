<template>
  <el-drawer
    v-model="showRegional"
    title="算力重点区域绘制"
    direction="rtl"
    size="37%"
    :close-on-click-modal="false"
    @open="openDrawer"
  >
    <div class="w-full h-full flex flex-col items-center">
      <!-- <div
        class="w-full h-[400px] flex-shrink-0"
        style="background-color: red"
      ></div> -->
      <div class="image-annotator">
        <div class="canvas-container">
          <img
            ref="imageRef"
            src="@/assets/header_other.png"
            @load="initCanvas"
            crossorigin="anonymous"
            class="annotator-image"
          />
          <canvas
            ref="canvasRef"
            class="annotator-canvas"
            @mousedown="startDrawing"
            @mousemove="draw"
            @mouseup="stopDrawing"
            @mouseleave="stopDrawing"
          />
        </div>
      </div>
      <p class="mt-[16px] mb-[16px] w-full content">
        说明:在视频画面区域内绘制封闭多端线,系统将AI算力集中于封闭区域内的检测,提高识别效率
      </p>
      <div class="flex flex-col flex-1 overflow-y-auto">
        <div class="w-full">
          <div class="w-full">
            <div class="title">算法名称</div>
            <div class="content">
              {{ detileData?.name }}
            </div>
          </div>
          <div class="w-full">
            <div class="title">算法描述</div>
            <div class="content">
              {{ detileData?.description }}
            </div>
          </div>
        </div>
        <div class="w-full flex-1">
          <div
            class="title mt-[24px]"
            v-if="detileData.algorithmConditionsDtos?.length > 0"
          >
            应用条件
          </div>
          <div class="df tiaojianlist" style="flex-wrap: wrap">
            <div
              class="w-[334px] h-[164px] mr-[24px] mb-[16px] item"
              v-for="item in detileData.algorithmConditionsDtos"
            >
              <div class="title">{{ item.title }}</div>
              <div>
                {{ item.describe }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer pb-[36px]">
        <el-button type="primary" @click="dialogConfirmRegional">
          保存
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
const showRegional = defineModel('showRegional', {
  type: Boolean,
  default: false
})
// 划分重点区域部分
const canvasRef = ref<any>(null)
const imageRef = ref<any>(null)
const isDrawing = ref(false)
const points = ref<any>([])

const initCanvas = () => {
  if (!canvasRef.value || !imageRef.value) return

  const canvas = canvasRef.value
  const img = imageRef.value

  const displayedWidth = img.clientWidth
  const displayedHeight = img.clientHeight

  const dpr = window.devicePixelRatio || 1
  canvas.width = displayedWidth * dpr
  canvas.height = displayedHeight * dpr
  canvas.style.width = `${displayedWidth}px`
  canvas.style.height = `${displayedHeight}px`

  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.scale(dpr, dpr)
    ctx.strokeStyle = '#FF0000'
    ctx.lineWidth = 2
    ctx.lineJoin = 'round'
    ctx.lineCap = 'round'
  }
}

const getCanvasMousePos = (canvas: any, e: any) => {
  const rect = canvas.getBoundingClientRect()
  return {
    x: e.clientX - rect.left,
    y: e.clientY - rect.top
  }
}

const startDrawing = (e: any) => {
  if (!canvasRef.value) return
  isDrawing.value = true
  points.value = [getCanvasMousePos(canvasRef.value, e)]
}

const draw = (e: any) => {
  if (!isDrawing.value || !canvasRef.value) return

  const pos = getCanvasMousePos(canvasRef.value, e)
  console.log('pos', pos)
  points.value.push(pos)
  redrawAllPaths()
}

const stopDrawing = () => {
  isDrawing.value = false
}

const redrawAllPaths = () => {
  if (!canvasRef.value) return

  const ctx = canvasRef.value.getContext('2d')
  if (!ctx) return

  ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)

  if (points.value.length > 0) {
    ctx.beginPath()
    ctx.moveTo(points.value[0].x, points.value[0].y)
    for (let i = 1; i < points.value.length; i++) {
      ctx.lineTo(points.value[i].x, points.value[i].y)
    }
    ctx.stroke()
  }
}

const getCoordinates = () => {
  if (!imageRef.value || points.value.length === 0) {
    console.warn('没有可导出的坐标数据')
    return
  }

  const coordinates = points.value.map((p: any) => ({
    x: p.x / imageRef.value!.clientWidth,
    y: p.y / imageRef.value!.clientHeight
  }))

  console.log('坐标数据：', coordinates)
  return coordinates
}

const clearCanvas = () => {
  if (!canvasRef.value) return

  const ctx = canvasRef.value.getContext('2d')
  if (ctx) {
    ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)
  }
  points.value = []
}
// 保存按钮
const dialogConfirmRegional = () => {
  showRegional.value = false
  let gps = getCoordinates()
}
// 算法数据
const detileData = ref({
  name: '违规撑伞检测',
  description:
    '通过对输入的图片进行检测、分析，检测街面上是否存在经营撑伞的现象，如在店门口撑伞经营，路边撑伞经营。',
  algorithmConditionsDtos: [
    {
      title: '目标遮挡粘连',
      itemVlaue: '1',
      describe: '遮挡、冲出视野面积不可超过目标区域总面积的50%的'
    },
    {
      title: '画面质量',
      itemVlaue: '1',
      describe: '图像要求清晰可见，无遮挡；避免过曝、过暗、失真、色差等现象'
    },
    {
      title: '光照要求',
      itemVlaue: '1',
      describe: '支持白天时段使用；夜间光照良好，成像正常条件下也可以使用'
    },
    {
      title: '目标像素要求',
      itemVlaue: '1',
      describe: '在1920*1080分辨率下,目标宽度大于105像素'
    },
    {
      title: '其他要求',
      itemVlaue: null,
      describe: null
    }
  ]
})

// 弹窗开启事件
const openDrawer = () => {}
</script>

<style lang="scss" scoped>
.title {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8px;
}
.content {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #52585f;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.tiaojianlist {
}
.item {
  padding: 24px;
  padding-top: 28px;
  background: linear-gradient(180deg, #f2f5ff 0%, #ffffff 34%);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
  .title {
    position: relative;
    margin-left: 10px;
    line-height: 16px;
    margin-bottom: 9px;
    &:before {
      content: '';
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -10px;
      width: 3px;
      height: 16px;
      background-color: #3665ff;
    }
  }
}
.image-annotator {
  width: 100%;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden; /* 防止图片超出容器 */
}

.annotator-image {
  width: 100%;
  height: 400px;
  object-fit: contain; /* 保持比例，完整显示图片 */
  background-color: #f0f0f0; /* 图片未加载时的背景色 */
}

.annotator-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 400px;
  cursor: crosshair;
}
</style>

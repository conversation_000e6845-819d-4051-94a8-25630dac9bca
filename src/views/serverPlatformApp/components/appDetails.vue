<!-- 详情页面 -->
<template>
  <div class="flex flex-col gap-y-[16px]">
    <div>
      <el-button
        type="primary"
        @click="
          () => {
            emit('infoCallback')
          }
        "
        >返回列表</el-button
      >
    </div>
    <div>
      <div class="content-box-item">
        <div
          class="item-type"
          :style="{ backgroundColor: typeBgColor(info?.type) }"
        >
          {{ info?.type }}
        </div>
        <div class="w-full flex flex-row items-center">
          <svg-icon
            name="app_icon"
            style="width: 24px; height: 24px; margin-right: 5px"
          ></svg-icon>
          <div class="text-[18px] font-bold">{{ info?.title }}</div>
        </div>
        <div class="w-full flex flex-row justify-between">
          <div class="item-text">资源提供方: 楚雄州住建局</div>
          <div class="item-text">更新周期: 每周</div>
          <div class="item-text">创建时间: 2024-07-22 00:05:34</div>
          <div class="item-text">创建时间: 2024-07-22 00:05:34</div>
        </div>
        <div class="w-full flex flex-row justify-between items-center">
          <div class="item-text">已绑定算法: 非机动车乱停、垃圾满溢</div>
          <div class="item-text">状态: 编排中</div>
          <div>
            <el-button type="primary">查询详情</el-button>
            <el-button type="success">启动</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="px-[24px] py-[16px] box-border bg-white">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="API详情" name="first">API详情</el-tab-pane>
        <el-tab-pane label="错误参照码" name="second">错误参照码</el-tab-pane>
        <el-tab-pane label="示例代码" name="third">示例代码</el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TabsPaneContext } from 'element-plus'
defineProps<{
  info: any
}>()

const emit = defineEmits(['infoCallback'])

const typeBgColor = computed(() => {
  return (type: string) => {
    switch (type) {
      case '数据服务':
        return '#FFB400'
      case '算法服务':
        return '#28a745'
      case '业务服务':
        return '#3665FF'
    }
  }
})

const activeName = ref('first')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

onMounted(() => {
  console.log(111)
})
</script>

<style lang="scss" scoped>
.content-box-item {
  @apply relative w-full h-[128px] border rounded flex flex-col  p-[16px] box-border justify-between bg-white pr-[20%] cursor-pointer;
  .item-type {
    @apply absolute right-0 top-0 py-[8px] px-[16px] box-border  text-white;
  }
  .item-text {
    @apply text-gray-400;
    font-size: 16px;
  }
}

:deep(.el-tabs__item.is-active) {
  color: #3665ff;
}
:deep(.el-tabs__item:hover) {
  color: #3665ff;
}
:deep(.el-tabs__active-bar) {
  background: #3665ff;
}
</style>

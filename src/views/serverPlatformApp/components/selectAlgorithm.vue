<template>
  <div class="w-full h-full">
    <div class="w-full h-full flex flex-col justify-between">
      <div class="flex-col flex-shrink-0 items-center mb-[16px]">
        <!-- <div class="flex items-center mb-[16px]">
          <div class="w-[50px]">步骤3:</div>
          <div>选择算法服务</div>
          <div class="ml-[16px] mr-[6px]">已选择摄像机:</div>
          <div>{{ props.cameraList.length }}个</div>
          <div class="ml-[16px] mr-[6px]">已选择算法服务:</div>
          <div>{{ selectList.length }}个</div>
        </div> -->
        <div class="flex">
          <el-input
            placeholder="名称或类型"
            class="mr-[16px]"
            style="width: 244px"
            v-model="tablePagination.name"
            clearable
            @clear="clearEve('name')"
            @input="clickSearch"
          ></el-input>
          <!-- <select-by-dict
            v-model="tablePagination.usePerm"
            placeholder="服务类型"
            style="width: 166px; margin-right: 16px"
            :reqFn="() => $getServiceTypeList('')"
            :multiple="false"
            :defaultOptions="{ key: 'value', value: 'value', label: 'label' }"
            @change="clickSearch"
          /> -->
          <el-cascader
            v-model="typeValue"
            :options="listData"
            @change="handleChange"
          />
          <!-- <el-button type="primary" @click="clickSearch" style="width: 84px;">查询</el-button> -->
          <el-button
            @click="resetTable"
            style="width: 84px"
            type="primary"
            plain
            >重置</el-button
          >
        </div>
      </div>
      <div class="flex-1 box-border flex w-full">
        <div class="flex-1 h-660px">
          <table-view
            class=""
            ref="tableViewRef"
            :tableData="tableData"
            :total="tableTotal"
            :currentPage="tablePagination.current"
            :pageSize="tablePagination.size"
            v-loading="tableLoading"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
            @handleSelectBox="handleSelect"
            style="padding: 0"
            :rowKey="'apiServerCode'"
            @handleTableCurrentChange="getCurrentRow"
          >
            <!-- <el-table-column label="选择" width="55">
                        <template #default="scope">
                            <el-radio v-model="algorithm" :label="scope.row"><i></i></el-radio>
                        </template>
  </el-table-column> -->
            <el-table-column
              type="selection"
              width="55"
              :selectable="checkSelectable"
            />
            <!-- <el-table-column type="index" label="序号" width="60" /> -->
            <el-table-column prop="apiServerName" label="名称" width="" />
            <el-table-column prop="apiTypeName" label="服务类型" width="" />
            <el-table-column prop="description" label="描述" width="500" />
            <!-- <el-table-column prop="useNum" label="可用路数"> </el-table-column> -->
            <el-table-column prop="count" label="已配置视频" width="">
              <template #default="scope">
                <span
                  class="text-[#3665FF] cursor-pointer"
                  style="text-decoration: underline"
                  @click="handleVideoCount(scope.row)"
                  >{{ scope.row.count }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #="{ row }">
                <div class="df">
                  <div class="df">
                    <div class="mr-2">
                      <el-link
                        class="mr-2"
                        type="primary"
                        @click="handleDetail(row)"
                        >详情</el-link
                      >
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </table-view>
        </div>

        <!-- 已选清单列表 -->
        <div class="h-full box-border pb-[16px] flex-shrink-0">
          <div
            class="flex flex-col border border-solid border-[#E5E7EB] rounded-[4px] w-[300px] h-full ml-[16px]"
          >
            <div
              class="flex items-center justify-between border-b border-solid border-[#E5E7EB]"
            >
              <div
                class="w-[140px] h-[40px] bg-[#3665FF] text-[#fff] flex items-center justify-center"
              >
                <span>已选中</span>
                <span>&nbsp; ({{ selectList.length || 0 }})</span>
              </div>
              <div class="mr-[10px] mt-[5px]">
                <el-icon
                  @click="clearAllLeftTable2SelectList"
                  style="font-size: 20px"
                >
                  <Delete />
                </el-icon>
              </div>
            </div>
            <div class="flex flex-col gap-y-[20px] overflow-auto h-full">
              <div
                v-for="(item, index) in selectList"
                :key="index"
                class="flex items-center justify-between px-[10px] h-[40px]"
              >
                <span>{{ item.apiServerName }}</span>
                <el-icon
                  @click="clearOneLeftTable2SelectList(item)"
                  style="font-size: 20px"
                >
                  <Delete />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="flex justify-end pb-[36px] box-border">
        <el-button color="#F2F3F5" @click="close">取消</el-button>
        <el-button type="primary" @click="dialogConfirmEve" :loading="loading">
          下一步
        </el-button>
      </div> -->
    </div>

    <!-- 查看绑定通道弹窗 -->
    <channel-list ref="chanRef" />
    <!-- 查看详情 -->
    <algorithm-detile ref="algorithmDetileRef" />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import algorithmDetile from './algorithmDetile.vue'
import TableView from '@/components/tableView/index.vue'
import { $getAuthorizedApiList, $getServiceTypeList } from '@/api/servicePage'
import { $getAlgorithmTaskDetail1 } from '@/api/ai'
import { useTableData } from '@/hooks/tableData'
import channelList from './channelList.vue'
import { warning } from '@/utils/toast'
import { $getDictType } from '@/api/dict/index'
import SelectByDict from '@/components/SelectByDict/index.vue'

onMounted(() => {
  tablePagination.value.size = 20
  tablePagination.value.grantDataIds = [orgObj.orgId]
  if (props.algorithmList.length) {
    selectList.value = props.algorithmList
  }
  clickSearch()
  getServiceTypeList()
})
const orgObj = JSON.parse(localStorage.getItem('userBaseInfo')!).userBaseInfo
// 获取服务类型型
const listData = ref<any>([])
const typeValue = ref<any>([])
const getServiceTypeList = () => {
  $getServiceTypeList().then((res: any) => {
    listData.value = res.data.map((item: any) => {
      return {
        label: item.sourceName,
        value: item.sourceCode,
        children: item.apiTypeId.map((api: any) => {
          return {
            label: Object.values(api)[0],
            value: Object.keys(api)[0],
            sourceCode: item.sourceCode
          }
        })
      }
    })
    console.log(listData.value)
  })
}
// 删除已选择
const tableViewRef = ref<any>(null)
const clearAllLeftTable2SelectList = () => {
  selectList.value = []
  tableViewRef.value.clearSelection()
}
const clearOneLeftTable2SelectList = (item: any) => {
  selectList.value = selectList.value.filter(
    (i: any) => i.apiServerCode !== item.apiServerCode
  )
  // tableViewRef.value.clearRowSelection(item, false)
  setSelectionFn(selectList.value)
}
// 防范
const setSelectionFn = (rows: any) => {
  if (!Array.isArray(rows)) return
  // 先清除所有选中状态
  tableViewRef.value.clearSelection()
  // 等待数据更新完成
  nextTick(async () => {
    console.log('为什么全被情况', rows, tableData.value)
    // 确保表格实例存在
    if (!tableViewRef.value) return
    // 遍历需要选中的行
    for (const row of rows) {
      if (!row) continue
      // 在表格数据中查找匹配的行
      const matchRow = tableData.value.filter(
        (item) => item.apiServerCode === row.apiServerCode
      )
      if (matchRow.length > 0) {
        console.log('需要回显的数组', matchRow)
        await tableViewRef.value.clearRowSelection(matchRow[0], true)
      }
    }
  })
}
const handleChange = (e: any) => {
  tablePagination.value.apiTypeId = e[1]
  tablePagination.value.sourceCode = e[0]
  clickSearch()
  console.log('改变类型', e, tablePagination.value)
}

const props = defineProps({
  data: {
    type: Array,
    default: []
  },
  cameraList: {
    type: Array,
    default: []
  },
  algorithmList: {
    type: Array,
    default: []
  }
})

const changeList = computed(() => {
  return props.data.map((item: any) => {
    return item.apiServerCode
  })
})

//动态计算样式
function computedClass(status: any) {
  if ([0, 4].includes(status)) {
    return 'text-red-500'
  } else if ([1, 3].includes(status)) {
    return 'text-green-500'
  } else if ([2].includes(status)) {
    return 'text-gray-500'
  } else {
    return ''
  }
}

//根据状态判断是否可选
function checkSelectable(row: any, index: any) {
  if (row.isListed == 1) {
    return true
  } else {
    return true
  }
}

getDict()
const dictList = ref([])
//获取状态字典
function getDict() {
  $getDictType('sys_api_type').then((res) => {
    dictList.value = res.data
    console.log('类型', dictList.value)
  })
}

const chanRef = ref()
const {
  tableData,
  selectList,
  tableTotal,
  tablePagination,
  addReqParams,
  getTableData,
  handleSizeChange,
  handleCurrentChange,
  tableLoading,
  loadingBgColor
} = useTableData({
  reqFn: $getAuthorizedApiList,
  fn: (res: any) => {
    tableData.value = res.data.records.map((item: any) => {
      item.select = false
      // if (dictList.value.length > 0) {
      //   dictList.value.forEack((it) => {
      //     if (item.apiTypeId == it.id) {
      //       item.apiTypeName = it.name
      //     }
      //   })
      // }

      return item
    })
    tableTotal.value = res.data.total
    console.log('🚀 ~ tableTotal.value:', tableTotal.value)
  }
})

const $emit = defineEmits()

// 单选
const algorithm = ref()
const getCurrentRow = (row: any) => {
  algorithm.value = row
}

watch(
  () => tableData.value,
  (val) => {
    if (val.length) {
      console.log('回填选中的算法', selectList.value)
      setSelectionFn(selectList.value)
      // let data = tableData.value.filter((item: any) =>
      //   changeList.value.includes(item.apiServerCode)
      // )
      // console.log('回填选中的算法', data)
      // nextTick(() => {
      //   data.forEach((element: any) => {
      //     tableViewRef.value.clearRowSelection(element, true)
      //   })
      // })
    }
  },
  { deep: true, immediate: true }
)

const keyword = ref('')
const clickSearch = () => {
  getTableData(tablePagination.value)
}
// 重置
const resetTable = () => {
  typeValue.value = ''
  tablePagination.value = {
    current: 1,
    size: 20,
    grantDataIds: [orgObj.orgId]
  }
  getTableData()
}
// 视频路数点击
const handleVideoCount = (row: any) => {
  chanRef.value.open(row)
}

const algorithmDetileRef = ref<any>(null)

// 查看详情
const handleDetail = (row: any) => {
  getAlgorithmInfo(row.apiServerCode)
}

// 获取算法详情
const getAlgorithmInfo = (code: any) => {
  $getAlgorithmTaskDetail1({ code }).then((res: any) => {
    if (res.code == 0) {
      algorithmDetileRef.value.open(res.data)
    }
  })
}

// 清空条件
const clearEve = (val: any) => {
  tablePagination.value[val] = ''
  clickSearch()
}
const close = () => {
  $emit('cancel')
}
const handleSelect = (selection: any, row: any, allList: any) => {
  console.log('选择的算法', selection, row)
  if (!row) {
    if (selection.length === 0) {
      selectList.value = selectList.value.filter(
        (item) =>
          !allList.find(
            (listItem) => listItem.apiServerCode === item.apiServerCode
          )
      )
    } else {
      const newSelections = selection.filter(
        (item) =>
          !selectList.value.find(
            (it) => it.apiServerCode === item.apiServerCode
          )
      )
      selectList.value = [...selectList.value, ...newSelections]
    }
  } else {
    if (selection.includes(row)) {
      if (
        !selectList.value.find((it) => it.apiServerCode === row.apiServerCode)
      ) {
        selectList.value.push(row)
      }
    } else {
      const index = selectList.value.findIndex(
        (it) => it.apiServerCode === row.apiServerCode
      )
      if (index !== -1) {
        selectList.value.splice(index, 1)
        tableViewRef.value.clearRowSelection(row, false)
      }
    }
  }
}
const dialogConfirmEve = () => {
  $emit('selectAlgorithmFn', selectList.value)
}
watch(
  () => selectList.value,
  (newVal, oldVal) => {
    $emit('selectAlgorithmFn', selectList.value)
  },
  {
    deep: true
  }
)
</script>
<style lang="scss" scoped></style>

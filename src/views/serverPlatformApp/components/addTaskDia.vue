<template>
  <el-drawer
    v-model="showDrawer"
    :title="appFrom?.taskIndexCode ? '编辑算法服务' : '新增算法服务'"
    direction="rtl"
    size="70%"
    :close-on-click-modal="false"
    @open="openDrawer"
  >
    <addFrom
      v-if="checkout == 'one'"
      @cancel="cancel"
      :appFrom="appFrom"
      @taskInformation="taskInformation"
    ></addFrom>
    <!-- <selectCamera
      v-if="checkout == 'two'"
      @cancel="cancel"
      @selectCameraFn="selectCameraFn"
    ></selectCamera>
    <selectAlgorithm
      v-if="checkout == 'three'"
      @cancel="cancel"
      :cameraList="submitDatda.cameraValue"
      @selectAlgorithmFn="selectAlgorithmFn"
    ></selectAlgorithm> -->
    <selectTime
      v-if="checkout == 'four'"
      :algorithm="submitDatda.algorithmValue"
      :cameraList="submitDatda.cameraValue"
      @cancel="cancel"
      @selectTimeFn="selectTimeFn"
    ></selectTime>
  </el-drawer>
</template>

<script setup lang="ts">
import addFrom from './addFrom.vue'
import selectCamera from './selectCamera.vue'
import selectAlgorithm from './selectAlgorithm.vue'
import selectTime from './selectTime.vue'
import { $addTask, $editAlgorithmTask } from '@/api/addTask/index'
import { error, success } from '@/utils/toast'

const showDrawer = defineModel('showDrawer', {
  type: Boolean,
  default: false
})
const props = defineProps({
  appFrom: {
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits()
// checkout
const checkout = ref('')
// 提交的数据
const submitDatda = reactive<any>({
  infoValue: {},
  cameraValue: [],
  algorithmValue: [],
  timeValue: {}
})
// 任务信息
const cancel = () => {
  showDrawer.value = false
}
const taskInformation = (val: any) => {
  submitDatda.infoValue = val.infoValue
  submitDatda.cameraValue = val.cameraValue.map((it: any) => {
    return {
      cameraIndexCode: it.gbId,
      cameraindexName: it.name
    }
  })
  submitDatda.algorithmValue = val.algorithmValue
  console.log('打印传过来选择的算法信息', submitDatda.algorithmValue)

  checkout.value = 'four'
}
// const selectCameraFn = (val) => {
//   submitDatda.cameraValue = val

//   checkout.value = 'three'
// }
// const selectAlgorithmFn = (val) => {
//   submitDatda.algorithmValue = val
//   checkout.value = 'four'
// }
// 提交按钮
const selectTimeFn = (val: any) => {
  let userId = JSON.parse(localStorage.getItem('userInfo')!)?.user_id
  let orgObj = JSON.parse(localStorage.getItem('userBaseInfo')!).userBaseInfo
  let algorithmValue = val.algorithmPresets.map((it: any) => {
    return {
      algorithmCode: it.algorithmCode,
      algorithmName: it.algorithmName,
      vendor: it.vendor || 'HIKVISION',
      weeklyPlanId: it.weeklyPlanId
    }
  })
  if (props.appFrom.taskIndexCode) {
    $editAlgorithmTask({
      taskIndexCode: props.appFrom.taskIndexCode,
      presetsDtos: submitDatda.cameraValue,
      algorithmPresets: algorithmValue,
      taskName: submitDatda.infoValue?.title,
      startTime: submitDatda.infoValue?.startTime,
      endTime: submitDatda.infoValue?.endTime,
      userIds: [userId],
      organizationId: orgObj.orgId,
      organizationName: orgObj.orgName
    }).then((res) => {
      console.log('res', res)
      if (res.code == 0) {
        success('编辑成功')
        showDrawer.value = false
        emit('Refresh')
      }
    })
  } else {
    $addTask({
      presetsDtos: submitDatda.cameraValue,
      algorithmPresets: algorithmValue,
      taskName: submitDatda.infoValue?.title,
      startTime: submitDatda.infoValue?.startTime,
      endTime: submitDatda.infoValue?.endTime,
      userIds: [userId],
      organizationId: orgObj.orgId,
      organizationName: orgObj.orgName
    }).then((res) => {
      console.log('res', res)
      if (res.code == 0) {
        success('新增成功')
        showDrawer.value = false
        emit('Refresh')
      }
    })
  }

  console.log('submitDatda', submitDatda)
}
const appFrom = ref<any>({})
const openDrawer = () => {
  if (props.appFrom) {
    appFrom.value = props.appFrom
  }
  checkout.value = 'one'
}
</script>

<style lang="scss" scoped></style>

<!-- 已绑定算法弹窗 -->
<template>
  <dialog-view ref="dialogView" width="500px" :footer="false" :title="title" @confirm="confirmEve">
    <div v-if="algorithmList.length == 0" style="text-align: center;">暂无算法...</div>
    <div v-else>
      <TableView :tableData="algorithmList" :needPagination="false" style="max-height: 400px; padding: 0;" >
        <!-- <el-table-column label="厂商" prop="vendor" width="140"/> -->
        <el-table-column label="算法名称" prop="name"/>
        <el-table-column label="算法类型" prop="name"/>
      </TableView>
      <!-- <div v-for="item in algorithmList" class="mb-[20px]">
        <div>厂商：{{ item.vendor }}</div>
        <div>算法名称：{{ item.name }}</div>
      </div> -->
    </div>
  </dialog-view>
</template>

<script setup>
  import DialogView from "@/components/DialogView/index.vue";
  // import { $getAlgorithmList } from '@/api/ai'
import { $getAlgorithmList } from "@/api/videoCenter";

  import { ref, onMounted } from "vue";


  const dialogView = ref(null);
  const list = ref([]);

  const algorithmList = ref([])

  // 已绑定算法
  const getAlgorithmList = (id) => {
    $getAlgorithmList({sn: id}).then(res=>{
      algorithmList.value = res.data
      dialogView.value.open();
    })
  }

  const title = ref('已绑定算法')

  // 打开配置权限弹窗
  const open = (val) => {
    title.value = val.name + ' -- ' + '已绑定算法'
    getAlgorithmList(val.sn)
  }



  defineExpose({
    open
  })
</script>

<style lang="scss" scoped>
</style>
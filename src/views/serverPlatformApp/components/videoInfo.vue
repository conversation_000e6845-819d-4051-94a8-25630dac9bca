<!-- 视频详情弹窗 -->
<template>
  <dialog-view
    ref="dialogView"
    :title="list?.name"
    :needOkBtn="false"
    cancelBtnText="关闭"
    width="40%"
    @confirm="confirmEve"
    @cancel="cancelEve"
  >
    <div class="flex flex-col w-[100%] cjj" style="aspect-ratio: 16 / 7">
      <div
        class="left bg-[#000] flex items-center justify-center"
        style="aspect-ratio: 16 / 7"
      >
        <div
          class="flex w-full h-full z-9999 overflow-hidden"
          style="aspect-ratio: 16 / 7"
          ref="leftVideo"
          :class="isFull ? 'full_screen' : 'noFull'"
        >
          <!-- <H264View ref="videoEl" :data="urlData" @infoCallback="infoCallback" /> -->
          <jc-video
            ref="jcRef"
            :sn="urlData"
            @dataInfo="infoCallback"
            @playingEve="playingEve"
            @timeOut="timeoutEve"
          ></jc-video>
          <!-- <xg-player ref="xgplayerRef" :srcObj="urlData" @dataInfo="infoCallback"></xg-player> -->
          <div
            v-if="isTimeOut"
            class="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center bg-[#000] text-[#FFF]"
            style="z-index: 99999"
          >
            视频加载失败...
          </div>
          <canvas
            v-if="!!videoInfo"
            ref="canvasEl"
            class="absolute top-0 left-0 right-0 bottom-0 area z-9999"
          ></canvas>
          <div
            class="action w-full bg-[#000] flex items-center justify-end px-8"
            v-if="!!videoInfo && isPlaying"
          >
            <div class="flex-1 h-full flex items-center text-[#FFF]">
              {{ list?.name }}
            </div>
            <div class="flex h-full items-center">
              <img
                class="w-[24px] h-[24px]"
                v-if="!isFull"
                @click="fullScreen(true)"
                src="@/assets/images/cssScroll.png"
                alt=""
              />
              <img
                class="w-[24px] h-[24px]"
                v-else
                @click="fullScreen(false)"
                src="@/assets/images/exitFull.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="right w-[50%] pl-[32px] pr-[16px] flex flex-col mt-[16px]"
        style="height: 100%"
      >
        <div>
          <div
            style="text-align: right"
            class="h-[36px] w-full flex items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">
              视频名称
            </div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{ list?.name }}
            </div>
          </div>
          <div
            style="text-align: right"
            class="h-[36px] flex w-full items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">协议</div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{ list?.protocolName }}
            </div>
          </div>
          <div
            style="text-align: right"
            class="h-[36px] flex w-full items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">标识</div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{ list?.gbId ? list?.gbId : list?.streamIp }}
            </div>
          </div>
          <div
            style="text-align: right"
            class="h-[36px] flex w-full items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">地址</div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{ list?.siteAddress }}
            </div>
          </div>
          <div
            style="text-align: right"
            class="h-[36px] flex w-full items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">
              分辨率
            </div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{
                videoInfo.width && videoInfo.height
                  ? videoInfo.width + '*' + videoInfo.height
                  : '-'
              }}
            </div>
          </div>
          <div
            style="text-align: right"
            class="h-[36px] flex w-full items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">码流</div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{
                videoInfo.videodatarate
                  ? (videoInfo.videodatarate / 1024).toFixed(0) + 'kbps'
                  : '0kbps'
              }}
            </div>
          </div>
          <div
            style="text-align: right"
            class="h-[36px] flex w-full items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">帧率</div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{ videoInfo.framerate ? videoInfo.framerate + 'fps' : '0fps' }}
            </div>
          </div>
          <div
            style="text-align: right"
            class="h-[36px] flex w-full items-center"
          >
            <div class="w-[60px] text-[#676767] flex-shrink-0 h-full">
              编码格式
            </div>
            <div class="flex-1 h-full text-[#000] pl-[16px] text-left">
              {{ videoInfo.encoder ? videoInfo.encoder : '-' }}
            </div>
          </div>
        </div>
        <div style="text-align: right" class="h-[40px] flex w-full mb-[-5px]">
          <div
            class="w-full text-[#676767] flex-shrink-0 h-full flex justify-start"
          >
            已绑定算法 (点击查看算法区域划分)
          </div>
        </div>
        <div class="overflow-auto flex-1">
          <el-scrollbar v-if="!!rulesList.length">
            <div
              v-for="(it, index) in rulesList"
              class="border-b border-[#E5E5E5]"
            >
              <el-checkbox-button
                :disabled="!it.algRuleVos?.length"
                :title="!it.algRuleVos?.length ? '未绑定算法区域' : ''"
                v-model="checkAllList[index]"
                @change="(val) => handleCheckAllChange(val, index)"
                :class="['checkbox-color', 'color-' + index]"
              >
                <div class="flex items-center relative w-full">
                  {{ it.algorithmName }}
                  <div
                    class="w-[10px] h-[10px] ml-1"
                    :style="{ backgroundColor: getColor(index) }"
                  ></div>
                  <div
                    class="flex h-full items-center cursor-pointer absolute right-0 top-0"
                    @click.prevent="openItemEve(it)"
                    v-if="it.algRuleVos?.length > 0"
                  >
                    <el-icon class="mr-2 w-[16px] h-[16px]" v-if="it.collapse"
                      ><ArrowDownBold
                    /></el-icon>
                    <el-icon class="mr-2 w-[16px] h-[16px]" v-else
                      ><ArrowRightBold
                    /></el-icon>
                  </div>
                </div>
              </el-checkbox-button>
              <div
                class="w-full bxx"
                :style="{
                  height: it.collapse
                    ? it.algRuleVos?.length * 45 + 'px'
                    : '0px'
                }"
              >
                <el-checkbox-group
                  class="ml-6 flex flex-col"
                  v-model="checkboxGroup[index]"
                  @change="(val) => handleCheckedCitiesChange(val, index)"
                >
                  <el-checkbox-button
                    v-for="(item, idx) in it.algRuleVos"
                    :key="item.id"
                    :value="item.id"
                    class="flex"
                  >
                    <div class="flex justify-between w-[80%] h-full relative">
                      <div class="h-full flex items-center">
                        {{ item.ruleName }}
                      </div>
                    </div>
                  </el-checkbox-button>
                </el-checkbox-group>
              </div>
            </div>
          </el-scrollbar>
          <el-empty v-else description="暂无划分区域" />
        </div>
      </div>
      <div></div>
    </div>
  </dialog-view>
</template>

<script setup>
import DialogView from '@/components/DialogView/index.vue'
// import H264View from '@/components/CommonVideo/h264.vue';
import jcVideo from '@/components/CommonVideo/jcVideo.vue'
import { ref, nextTick } from 'vue'
import { $getCameraDetailById } from '@/api/videoCenter/index'
import { useCanvas } from '@/hooks/canvasHooks.js'
import { success, warning } from '@/utils/toast'

import { $getAlgorithmTaskRule } from '@/api/ai'
import { ArrowDownBold, ArrowRightBold } from '@element-plus/icons-vue'

// 视频播放器
const xgplayerRef = ref(null)

const jcRef = ref(null)

const { drawPolygon, clearCanvas, initCanvas } = useCanvas()

const colorList = [
  '#f56c6c',
  '#e6a23c',
  '#409eff',
  '#67c23a',
  '#8e44ad',
  '#1abc9c',
  '#f39c12',
  '#e91e63',
  '#795548',
  '#2c2c54'
]

const getColor = (index) => {
  return colorList[index]
}

// 全屏状态
const isFull = ref(false)
// 视频播放器
const videoEl = ref(null)
// canvas 区域画布实例
const canvasEl = ref(null)
const urlData = ref()

const dialogView = ref(null)

const list = ref()

const videoInfo = ref({})

const leftVideo = ref(null)

// 模拟绑定算法
const checkAllList = ref([])
const checkboxGroup = ref([])

// 全选事件
const handleCheckAllChange = (val, index) => {
  if (val) {
    checkboxGroup.value[index] = rulesList.value[index].algRuleVos?.map(
      (item) => item.id
    )
  } else {
    checkboxGroup.value[index] = []
  }
}

// 单选事件
const handleCheckedCitiesChange = (val, index) => {
  if (val.length == rulesList.value[index].algRuleVos?.length) {
    checkAllList.value[index] = true
  } else {
    checkAllList.value[index] = false
  }
}

// 打开子算法
const openItemEve = (val) => {
  rulesList.value.forEach((item) => {
    if (item.algorithmCode == val.algorithmCode) {
      item.collapse = !item.collapse
    }
  })
}

function infoCallback(data) {
  videoInfo.value = data
}

// 所有的子算法区域
const childrenList = ref([])

const addList = (val) => {
  childrenList.value =
    val.map((it) => it.algRuleVos?.map((item) => item)).flat() || []
}

// 获取视频绑定的算法规则
const rulesList = ref([])
const queryAlgorithmByCameraId = (val) => {
  const obj = {
    cameraIndexCode: val.gbId || val.deviceIndex,
    taskIndexCode: ''
  }
  $getAlgorithmTaskRule(obj).then((res) => {
    rulesList.value = res.data.map((item) => {
      return {
        ...item,
        collapse: false
      }
    })
    addList(res.data)
    checkAllList.value = res.data.map((item) => false)
    checkboxGroup.value = res.data.map((item) => [])
  })
}

const open = async (val) => {
  queryAlgorithmByCameraId(val)
  await getData({ gbId: val.gbId || val.deviceIndex })
  urlData.value = list.value.sn
  dialogView.value.open()
  nextTick(() => {
    initCanvas(
      canvasEl.value,
      leftVideo.value.offsetWidth,
      leftVideo.value.offsetHeight
    )
    // videoEl.value.startConnect(urlData.value)
  })
}

function cancelEve() {
  // urlData.value = {}
  // videoInfo.value = {}
  // videoEl.value.stopConnect()
  // rulesList.value = []
  isTimeOut.value = false
  videoInfo.value = {}
}

function confirmEve() {
  // videoEl.value.stopConnect()
}

const getData = async (params) => {
  const res = await $getCameraDetailById(params)
  list.value = res.data
}

// 视频全屏
const fullScreen = (val) => {
  isFull.value = val
  clearCanvas()
  nextTick(() => {
    initCanvas(
      canvasEl.value,
      leftVideo.value.offsetWidth,
      leftVideo.value.offsetHeight
    )
    drawLine(sameList)
  })
}

const isTimeOut = ref(false)

// 超时事件
const timeoutEve = () => {
  isTimeOut.value = true
}

// 画线
let sameList = []
watch(
  () => checkboxGroup.value,
  (val) => {
    const list = val.map((it) => it.map((item) => item)).flat()
    // 找出chilrenList中和list中相同的元素
    sameList = childrenList.value.filter((item) =>
      list.some((it) => it == item?.id)
    )
    drawLine(sameList)
  },
  {
    deep: true
  }
)

// 画线方法
const drawLine = (val) => {
  clearCanvas()
  // 再画
  for (let i = 0; i < val.length; i++) {
    drawPolygon(
      val[i].aiGovernResultVo.content.basic.polygon[0].points,
      colorList[
        rulesList.value.findIndex(
          (it) => it.algorithmCode == val[i].algorithmCode
        )
      ],
      val[i].algorithmName + '/n' + val[i].ruleName
    )
  }
}

// const mouseEve = () => {
//   xgplayerRef.value.focusEvent()
// }

// 展示视频操作栏
const showControls = () => {
  jcRef.value.showOperationEve()
}

// 隐藏视频操作栏
const hiddenControls = () => {
  jcRef.value.hideOperationEve()
}

// 视频是否开始播放了
const isPlaying = ref(false)

const playingEve = (val) => {
  isPlaying.value = val
}

onMounted(() => {})

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
// 随机20个颜色
$colors: #f56c6c, #e6a23c, #409eff, #67c23a, #8e44ad, #1abc9c, #f39c12, #e91e63,
  #795548, #2c2c54;
.el-checkbox-button {
  width: 100%;
  margin-bottom: 4px;
}
.checkbox-color {
  width: 100%;
  margin-bottom: 4px;
  :deep(.el-checkbox-button__inner) {
    border: 0 !important;
    border-radius: 0 !important;
    width: 100% !important;
    box-shadow: none !important;
    border: 1px solid #d9d9d9 !important;
  }
}

:deep(.el-checkbox-group .el-checkbox-button) {
  @for $i from 1 through length($colors) {
    &.color-#{$i - 1} {
      --el-checkbox-button-checked-bg-color: nth($colors, $i) !important;
      background-color: nth($colors, $i) !important;
    }
  }
}
.left {
  .action {
    position: absolute;
    bottom: -45px;
    left: 0;
    height: 40px;
    transition: all 0.4s ease;
    z-index: 999999;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  &:hover {
    .action {
      bottom: -1px;
    }
    // .area {
    //   z-index: 0 !important;
    // }
  }
}
svg {
  width: 20px;
  height: 20px;
}
.right {
  :deep(.el-checkbox-button__inner) {
    height: 40px !important;
    display: flex;
    align-items: center;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
    border-radius: 0 !important;
  }
  .bxx {
    transition: all 0.3s ease;
    overflow: hidden;
  }
}

.full_screen {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.noFull {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>

<template>
  <div class="flex flex-row w-full h-full" style="background-color: #f2f4f5">
    <!-- <div class="content-tree bg-white">
      <div
        class="left flex flex-col w-[250px] h-[full] flex-shrink-0 overflow-hidden"
        style="border-right: 1px solid #e2e2e2"
      >
        <div
          style="font-weight: bold"
          class="bg-tit flex-shrink-0 h-[66px] bg-[#3665FF] bg-opacity-10 w-full text-[20px] flex items-center justify-center text-[#3665FF]"
        >
          应用目录
        </div>
        <div class="p-[8px] box-border">
          <el-input placeholder="应用名称" clearable>
            <template #suffix>
              <el-icon><Search /></el-icon> </template
          ></el-input>
        </div>
        <div class="h-[calc(100%-66px)] w-full p-[8px]"></div>
      </div>
    </div> -->
    <div class="content">
      <div class="p-[16px] box-border w-full h-full flex flex-col gap-y-[16px]">
        <div class="content-header box-border" v-if="appInfo == null">
          <div class="mb-[16px] flex items-center">
            <el-input
              style="width: 150px"
              v-model="query.keyWord"
              placeholder="任务名称"
              clearable
              class="mr-2"
              @change="queryFn"
            >
            </el-input>
            <el-select
              v-model="query.algorithmCode"
              placeholder="请选择已绑定算法"
              class="mr-2"
              style="width: 166px"
              @change="queryFn"
              clearable
            >
              <el-option
                v-for="item in menuData"
                :key="item.algorithmCode"
                :label="item.name"
                :value="item.algorithmCode"
              />
            </el-select>

            <datePicker
              v-model:startAt="query.startTime"
              v-model:endAt="query.endTime"
              :showEndAfterTime="true"
              :showStartAfterTime="true"
              class="mr-2"
              @change="queryFn"
            />
            <el-button type="primary" @click="resetEve">重置</el-button>
          </div>
          <el-button type="primary" @click="clickAddApp"
            >新增视频算法任务</el-button
          >
        </div>
        <div
          v-if="appInfo == null"
          class="flex-1 flex flex-col justify-between overflow-hidden h-full"
        >
          <div class="flex-1 overflow-auto">
            <div class="content-box">
              <div
                class="content-box-item"
                v-for="item in dataList"
                :key="item.title"
              >
                <div
                  class="item-type"
                  :style="{ backgroundColor: typeBgColor(item.type) }"
                >
                  {{ item.type }}
                </div>
                <div class="w-full flex flex-row items-center mb-[16px]">
                  <svg-icon
                    name="app_icon"
                    style="width: 24px; height: 24px; margin-right: 5px"
                  ></svg-icon>
                  <div class="text-[18px] font-bold">{{ item.taskName }}</div>
                </div>
                <div class="w-full flex flex-row mb-[16px] pr-[20%]">
                  <!-- <div class="item-text">创建组织: 楚雄州住建局</div>
                  <div class="item-text">使用组织: 楚雄住建局,楚雄环保局</div> -->
                  <div class="item-text">创建时间: {{ item.createTime }}</div>
                  <!-- <div class="item-text">订阅人数: 133人</div> -->
                  <div class="item-text">
                    任务有效期: {{ item.startTime }} 至 {{ item.endTime }}
                  </div>
                </div>
                <div
                  class="w-full flex flex-row items-center mb-[16px] pr-[20%]"
                >
                  <!-- <div class="item-text">
                    已绑定算法: 非机动车乱停、垃圾满溢
                  </div>
                  <div class="item-text">来源: 自建</div>
                  <div class="item-text">视频数量: 100路</div> -->
                  <div class="item-text">
                    状态:
                    <span style="color: #e23e31" v-if="item.runStatus != 1"
                      >已结束</span
                    >
                    <span style="color: #85c5ad" v-else>分析中</span>
                  </div>
                  <!-- <div class="item-text"></div> -->
                </div>
                <div class="w-full flex flex-row justify-end items-center">
                  <!-- <el-button type="success">组织授权</el-button> -->
                  <el-button
                    v-if="item.runStatus != 1"
                    type="primary"
                    @click="handleStart(item)"
                    >启动</el-button
                  >
                  <el-button v-else type="primary" @click="handleStart(item)"
                    >停止</el-button
                  >
                  <el-button type="primary" @click="clickUpdateApp(item)"
                    >编辑</el-button
                  >
                  <el-button type="primary" @click="clickSubscribe(item)"
                    >消息订阅</el-button
                  >
                  <el-button
                    type="primary"
                    @click="clickAppItem(item, '任务详情')"
                    >任务详情</el-button
                  >
                  <el-button
                    type="primary"
                    @click="clickAppItem(item, '事件记录')"
                    >事件记录</el-button
                  >
                  <el-button type="primary" @click="clickAppItem(item, '日志')"
                    >日志</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="appInfo == null"
          class="flex flex-shrink-0 flex-row justify-end mt-[16px] bg-white p-[16px] box-border"
        >
          <el-pagination
            class="fn"
            :current-page="tablePagination.currentPage"
            :page-size="tablePagination.pageSize"
            layout="total,prev, pager,  next, sizes, jumper"
            background
            :total="tablePagination.total"
            @size-change="tablePagination.handleSizeChange"
            @current-change="tablePagination.handleCurrentChange"
          >
            <template #default>
              <div>
                共
                <span class="text-[#3665FF] font-bold">{{
                  tablePagination.total
                }}</span>
                条
              </div>
            </template>
          </el-pagination>
        </div>

        <div v-else>
          <AppDetails @infoCallback="infoCallback" :info="appInfo"></AppDetails>
        </div>
      </div>
    </div>
    <!-- 新增应用弹窗 -->
    <!-- <add-task ref="addTaskRef" :data="{}"></add-task> -->
    <addTaskDia
      v-model:showDrawer="showDrawer"
      :appFrom="appFrom"
      @Refresh="Refresh"
    ></addTaskDia>
    <!-- 日志 -->
    <LogModel ref="logViewRef" modular="algorithm_task"></LogModel>
    <!-- 算法服务详情 -->
    <serverInfo
      v-model:showInfo="showInfo"
      :infoDetailValue="infoDetailValue"
    ></serverInfo>
    <!-- 事件记录 -->
    <runDia v-model:showRun="showRun" :runDetailValue="runDetailValue"></runDia>
    <!-- 启动弹框 -->
    <dialog-view
      :title="startItem?.taskName"
      ref="startDialog"
      @confirm="confirmStartEvent"
      okBtnText="确定"
    >
      <div style="text-align: center">
        您确定要{{
          startItem && startItem.runStatus == 1 ? '停止' : '启动'
        }}该算法任务吗？
      </div>
    </dialog-view>
    <!-- 订阅 -->
    <dialogSubscribe ref="dialogSubscribeRef"></dialogSubscribe>
  </div>
</template>

<script setup lang="ts">
import AppDetails from './components/appDetails.vue'
import addTask from './components/addTask.vue'
import addTaskDia from './components/addTaskDia.vue'
import LogModel from '@/components/LogModel/index.vue'
import runDia from './components/runDia.vue'
import serverInfo from './components/serverInfo.vue'
import dialogSubscribe from './components/dialogSubscribe.vue'
import datePicker from '@/components/DatePicker/index.vue'
import DialogView from '@/components/DialogView/index.vue'
import { error, success } from '@/utils/toast'

import {
  $getTaskList,
  $getAlgorithmServiceList,
  $startAlgorithmAnalysis,
  $stopAlgorithmAnalysis
} from '@/api/addTask/index'

// 搜索
const query = ref<any>({
  algorithmCode: '',
  keyWord: '',
  startTime: '',
  endTime: '',
  size: 20,
  current: 1
})
onMounted(() => {
  let orgObj = JSON.parse(localStorage.getItem('userBaseInfo')!).userBaseInfo
  query.value.organizationId = +orgObj.orgId
  getAllAlgorithmList()
  queryTaskListFn()
})
// 消息订阅部分
const dialogSubscribeRef = ref<any>(null)
const clickSubscribe = (val: any) => {
  dialogSubscribeRef.value.addEnv()
}
// 启动
const startItem = ref<any>({})
const startDialog = ref<any>(null)
const handleStart = (row: any) => {
  startItem.value = row
  startDialog.value.open()
}
// 确认启动算法
const confirmStartEvent = () => {
  startDialog.value.loading = true
  if (startItem.value.runStatus == 1) {
    // 停止
    $stopAlgorithmAnalysis([startItem.value.taskIndexCode])
      .then((res: any) => {
        if (res.code == 0) {
          success('停止成功')
          startDialog.value.close()
          queryTaskListFn()
        }
        startDialog.value.loading = false
      })
      .catch((e) => {
        startDialog.value.loading = false
      })
  } else {
    $startAlgorithmAnalysis({ taskIndexCodes: [startItem.value.taskIndexCode] })
      .then((res: any) => {
        if (res.code == 0) {
          success('启动成功')
          startDialog.value.close()
          startDialog.value.loading = false
          queryTaskListFn()
        } else {
          startDialog.value.loading = false
        }
      })
      .catch((e) => {
        startDialog.value.loading = false
      })
  }
}
// 查询算法任务列表
const queryTaskListFn = () => {
  $getTaskList(query.value).then((res) => {
    if (res.code === 0) {
      dataList.value = res.data.records
      tablePagination.total = +res.data.total
    }
  })
}
// 获取所有算法 (用于表外筛选 已绑定算法下拉框)
const getAllAlgorithmList = () => {
  $getAlgorithmServiceList({ current: 1, size: 999 }).then((res) => {
    menuData.value = res.data.records
  })
}
// 查询时间
const queryFn = () => {
  queryTaskListFn()
}
// 刷新事件
const Refresh = () => {
  queryTaskListFn()
}
// 重置事件
const resetEve = () => {
  let orgObj = JSON.parse(localStorage.getItem('userBaseInfo')!).userBaseInfo
  query.value = {
    algorithmCode: '',
    keyWord: '',
    startTime: '',
    endTime: '',
    size: 20,
    current: 1,
    organizationId: +orgObj.orgId
  }
  tablePagination.pageSize = 20
  tablePagination.currentPage = 1
  queryTaskListFn()
}
const selectTieme = ref<[Date, Date]>([
  new Date(2000, 10, 10, 10, 10),
  new Date(2000, 10, 11, 10, 10)
])
const menuData = ref<Array<any>>([])
// 改变事件
const changeEve = () => {}
// 获取数据
const getData = () => {}
// 事件记录
const showRun = ref<boolean>(false)
const runDetailValue = ref<any>({})
// 分页数据
const tablePagination = reactive<any>({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  handleSizeChange: (e: any) => {
    sizeChange(e)
  },
  handleCurrentChange: (e: any) => {
    pageChange(e)
  }
})
const dataList = ref<any>([])

const typeBgColor = computed(() => {
  return (type: string) => {
    switch (type) {
      case '数据服务':
        return '#FFB400'
      case '算法服务':
        return '#28a745'
      case '业务服务':
        return '#3665FF'
    }
  }
})
// 任务详情
const showInfo = ref<boolean>(false)
const infoDetailValue = ref<any>({})
// 选中的应用详情
const appInfo = ref<any>(null)
// 日志
const logViewRef = ref(null)
const clickAppItem = (item: any, type: string) => {
  switch (type) {
    case '日志':
      console.log('日志', item)
      // logViewRef.value.open(row ? row.userId : '')

      break
    case '任务详情':
      // appInfo.value = item
      infoDetailValue.value.taskIndexCode = item.taskIndexCode
      infoDetailValue.value.taskName = item.taskName
      infoDetailValue.value.startTime = item.startTime
      infoDetailValue.value.endTime = item.endTime
      showInfo.value = true
      break
    case '事件记录':
      runDetailValue.value.taskIndexCode = item.taskIndexCode
      runDetailValue.value.taskName = item.taskName
      showRun.value = true
      break
  }
}

const infoCallback = () => {
  appInfo.value = null
}

// 新增应用按钮点击事件
const addTaskRef = ref(null)
const showDrawer = ref<boolean>(false)
const clickAddApp = () => {
  appFrom.value = {}
  console.log('新增应用')

  showDrawer.value = true
}
// 编辑
const appFrom = ref<any>({})
const clickUpdateApp = (val) => {
  appFrom.value = JSON.parse(JSON.stringify(val))
  showDrawer.value = true
}
// 分页事件
const sizeChange = (e: any) => {
  tablePagination.pageSize = e
  query.value.size = e
  queryTaskListFn()
}
const pageChange = (e: any) => {
  tablePagination.currentPage = e
  query.value.current = e
  queryTaskListFn()
}
</script>

<style scoped lang="scss">
.content {
  @apply w-full h-full box-border;
  .content-header {
    @apply bg-white p-[16px] box-border rounded;
  }
  .content-box {
    @apply flex flex-col gap-y-[16px] gap-x-[16px] w-full h-full;
    &-item {
      @apply relative w-full  border rounded flex flex-col  p-[16px] box-border justify-between bg-white  cursor-pointer;
      .item-type {
        @apply absolute right-0 top-0 py-[8px] px-[16px] box-border  text-white;
      }
      .item-text {
        @apply text-gray-400;
        font-size: 16px;
        flex: 1;
        text-align: left;
      }
    }
  }
}
.el-pagination {
  :deep(.is-active) {
    background-color: #e5eeff !important;
    color: #3665ff !important;
  }
  :deep(.el-pagination__total) {
    font-size: 16px !important;
    font-weight: 600 !important;
  }
}
</style>

<template>
  <div>
    <div class="p-[8px] box-border">
      <el-input v-model="filterText" placeholder="关键字" clearable>
        <template #suffix>
          <el-icon><Search /></el-icon> </template
      ></el-input>
    </div>
    <el-tree
      ref="treeRef"
      :data="treeMenuList"
      :filter-node-method="filterNode"
      :current-node-key="1"
      default-expand-all
      highlight-current
      node-key="id"
      :expand-on-click-node="false"
    >
      <template #default="{ node, data }">
        <div
          style="width: 100%"
          v-if="data.children && (data.content == null || '')"
          @click.stop="toggleNode(node)"
        >
          <span>
            {{ data.label }}
          </span>
        </div>
        <span v-else>
          {{ data.label }}
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus/es/components/index.mjs'
import { $_getHelpList } from '@/api/help'

const treeMenuList = ref<Tree[]>([])

// 格式化树状结构数据
const formatTree = (data: any[]): Tree[] => {
  let result: Tree[] = []
  data.forEach((item) => {
    let obj: Tree = {
      id: parseInt(item.id, 10),
      label: item.name,
      content: item.content
    }
    if (item.children && item.children.length > 0) {
      obj.children = formatTree(item.children)
    }
    result.push(obj)
  })
  return result
}

onMounted(async () => {
  let res = await $_getHelpList()
  treeMenuList.value = formatTree(res.data)
})

interface Tree {
  [key: string]: any
}

const filterText = ref('')

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.includes(value)
}

const emit = defineEmits(['nodeClick'])

const toggleNode = (node: any) => {
  node.expanded = !node.expanded
}

const treeRef = ref<InstanceType<typeof ElTree>>()

function findNodeById(treeData: Tree[], targetId: number) {
  let result = null
  function traverse(nodes: any, path: any) {
    for (let node of nodes) {
      const currentPath = [...path, node.label]

      // 检查当前节点的id是否匹配
      if (node.id === targetId) {
        result = {
          content: node.content,
          labels: currentPath
        }
        return
      }
      if (node.children) {
        traverse(node.children, currentPath)
      }
    }
  }
  traverse(treeData, [])
  return result
}

watch(
  () => treeRef.value?.getCurrentKey(),
  (node) => {
    emit('nodeClick', findNodeById(treeMenuList.value, node))
  }
)
</script>

<style lang="scss" scoped>
.active {
  color: #3665ff;
}

.el-input {
  --el-input-bg-color: #f4f5f7;

  :deep(.el-input__suffix) {
    cursor: pointer;
    padding-right: 11px;
  }

  :deep(.el-input__wrapper) {
    padding-right: 0;
  }
}

:deep(
    .el-tree--highlight-current
      .el-tree-node.is-current
      > .el-tree-node__content
  ) {
  // 设置颜色
  background-color: rgba(
    135,
    206,
    235,
    0.2
  ); // 透明度为0.2的skyblue，作者比较喜欢的颜色
  color: #409eff; // 节点的字体颜色
  font-weight: bold; // 字体加粗
}
</style>

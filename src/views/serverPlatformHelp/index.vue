<template>
  <div class="flex flex-row w-full h-full" style="background-color: #f2f4f5">
    <div class="content-tree bg-white">
      <div
        class="left flex flex-col w-[250px] h-[full] flex-shrink-0 overflow-hidden"
        style="border-right: 1px solid #e2e2e2"
      >
        <div
          style="font-weight: bold"
          class="bg-tit flex-shrink-0 h-[66px] bg-[#3665FF] bg-opacity-10 w-full text-[20px] flex items-center justify-center text-[#3665FF]"
        >
          文档目录
        </div>
        <div class="h-[calc(100%-66px)] w-full p-[8px]">
          <help-menu-tree @node-click="handleNodeClick"></help-menu-tree>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-box">
        <div
          class="bg-white p-[8px] border border-solid border-[#e5e7eb] box-border rounded breadcrumb"
        >
          <el-breadcrumb class="h-[100%] flex items-center">
            <el-breadcrumb-item
              v-for="(item, index) of titles"
              :key="index"
              :class="{
                isLink: index !== titles.length - 1
              }"
              >{{ item }}</el-breadcrumb-item
            >
          </el-breadcrumb>
        </div>
        <div style="height: calc(100% - 80px)">
          <Editor
            :needMenu="false"
            v-model:html-value="contentValue"
            :read-only="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HelpMenuTree from './components/helpMenuTree.vue'
import Editor from '@/components/Editor/codeEditor.vue'

const contentValue = ref('')

const titles = ref<any[]>([])

const handleNodeClick = (data: any) => {
  contentValue.value = data?.content
  titles.value = data?.labels
}
</script>

<style scoped lang="scss">
.content {
  @apply w-full h-full p-[16px] box-border;
  &-box {
    @apply rounded w-full h-full flex flex-col;
  }
}

.breadcrumb {
  :deep(.el-breadcrumb__inner) {
    color: #2274ff !important;
    cursor: pointer;
  }

  :deep(.isLink .el-breadcrumb__inner) {
    color: #a9a9a9 !important;
  }
}
</style>

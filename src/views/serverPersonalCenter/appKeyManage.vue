<template>
  <div class="containerBox">
    <div
      class="flex flex-col gap-y-[16px] bg-white p-[16px] box-border w-full h-full"
    >
      <div class="flex flex-row">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </div>
      <div class="f1">
        <ElementTable
          ref="table"
          :data="tableData"
          :table-title="tableTitle"
          :page-config="pageConfig"
          :delMethod="$_deleteAppKey"
          :delParams="openDelDialog"
          :refreshMethod="getTableData"
        ></ElementTable>
      </div>
    </div>
  </div>

  <!-- 新增弹窗 -->
  <dialog-view
    ref="dialogViewRef"
    title="新增调用方"
    width="20%"
    @confirm="saveAdd"
    :okBtnText="'保存'"
  >
    <el-form :model="formData" :rules="rules">
      <el-form-item label="调用方名称" required prop="appName">
        <el-input
          placeholder="调用方名称"
          v-model="formData.appName"
          clearable
        />
      </el-form-item>
    </el-form>
  </dialog-view>
</template>

<script setup lang="ts">
import { PageConfigType } from '@/components/ElementTable/index.vue'
import dialogView from '@/components/DialogView/index.vue'
import {
  $_getAppKeyList,
  $_addAppKey,
  $_changeAppKeyStatus,
  $_deleteAppKey
} from '@/api/manager'
import { success } from '@/utils/toast'
import { cloneDeep } from 'lodash-es'

const dialogViewRef = ref<any>(null)

// 新增对象
const formData = reactive<{
  appName: string
}>({
  appName: ''
})

const rules = reactive({
  appName: [
    {
      required: true,
      message: '请输入调用方名称',
      trigger: 'blur'
    }
  ]
})

const handleAdd = () => {
  dialogViewRef.value.open()
}

// 保存新增信息
const saveAdd = async () => {
  let res: any = await $_addAppKey({ ...formData })
  let { ok } = res
  if (ok) {
    dialogViewRef.value.close()
    pageConfig.currentPage = 1
    getTableData()
    success('新增成功')
  }
}

// 表格数据
const tableData = ref<any[]>()

const tableTitle: Array<Object | any> = [
  {
    label: '调用方名称',
    prop: 'appName',
    type: 'text'
  },
  {
    label: 'APP Key',
    prop: 'appKey',
    type: 'text'
  },
  {
    label: '更新时间',
    prop: 'Time',
    type: 'text'
  },
  {
    label: '状态',
    prop: 'appStatus',
    type: 'status',
    options: [
      {
        value: 0,
        type: 'success',
        text: '启用'
      },
      {
        value: 1,
        type: 'danger',
        text: '停用'
      }
    ]
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '触发器',
        prop: 'appStatus',
        options: [
          {
            value: 1,
            text: '启用'
          },
          {
            value: 0,
            text: '停用'
          }
        ],
        click: (row: any) => {
          changeStatus(row)
        }
      },
      {
        isLink: true,
        type: 'danger',
        name: '删除'
      }
    ]
  }
]

const pageConfig = reactive<PageConfigType>({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => {
    pageConfig.pageSize = e
    pageConfig.currentPage = 1
    getTableData()
  },
  handleCurrentChange: (e: any) => {
    pageConfig.currentPage = e
    getTableData()
  }
})

// 获取表格数据
const getTableData = async () => {
  let res: any = await $_getAppKeyList({
    current: pageConfig.currentPage,
    size: pageConfig.pageSize
  })
  let { ok, data } = res
  if (ok) {
    pageConfig.total = data.total
    tableData.value = cloneDeep(data.records).map((item: any) => {
      return {
        ...item,
        Time: item.updateTime ? item.updateTime : item.createTime
      }
    })
  }
}

// 修改状态
const changeStatus = async (row: any) => {
  let res: any = await $_changeAppKeyStatus(row.id, {
    status: row.appStatus == 0 ? 1 : 0
  })
  let { ok } = res
  if (ok) {
    success('修改成功')
    getTableData()
  }
}

// 删除项
const openDelDialog = (row: any) => {
  return { id: row.id }
}

onMounted(async () => {
  getTableData()
})
</script>

<style scoped></style>

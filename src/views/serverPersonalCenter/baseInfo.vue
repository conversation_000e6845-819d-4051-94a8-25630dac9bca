<template>
  <div class="containerBox">
    <div class="bg-white p-[16px] box-border w-full h-full">
      <el-form
        ref="formRef"
        :model="userBaseInfo"
        :rules="rules"
        label-position="right"
        label-width="80"
        status-icon
      >
        <el-form-item label="所属组织">
          <el-input
            v-model="userBaseInfo.orgName"
            disabled
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <up-load type="image" v-model="userBaseInfo.avatar"></up-load
        ></el-form-item>
        <el-form-item label="账号" prop="username">
          <el-input
            v-model="userBaseInfo.username"
            disabled
            style="width: 250px"
          ></el-input
        ></el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="userBaseInfo.name"
            clearable
            style="width: 250px"
          ></el-input
        ></el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input
            v-model="userBaseInfo.phone"
            clearable
            style="width: 250px"
          ></el-input
        ></el-form-item>
        <el-form-item label="电子邮箱" prop="email">
          <el-input
            v-model="userBaseInfo.email"
            clearable
            style="width: 250px"
          ></el-input
        ></el-form-item>
        <el-form-item>
          <el-button @click="handleSave">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus/es/components/index.mjs'
import UpLoad from '@/components/UpLoad/index.vue'
import { $_changeUserInfo } from '@/api/manager'
import { success } from '@/utils/toast'
import { useLogin } from '@/hooks/login'
import { useUserStore } from '@/store/modules/user'

const { getUserBaseInfo } = useLogin()

interface userBaseInfoType {
  orgName: string
  avatar: string
  username: string
  name: string
  phone: string
  email: string
  orgId: string
  userId: string
}
const userBaseInfo = reactive<userBaseInfoType>({
  orgName: '',
  avatar: '',
  username: '',
  name: '',
  phone: '',
  email: '',
  orgId: '',
  userId: ''
})

onMounted(() => {
  let res: any = useUserStore().userBaseInfo
  for (const item in userBaseInfo) {
    userBaseInfo[item as keyof userBaseInfoType] = res[item as any]
  }
})

const formRef = ref<FormInstance>()

const rules = reactive<FormRules<userBaseInfoType>>({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  email: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback('请输入电子邮箱')
        } else if (!/^\w+@[a-z0-9]+\.[a-z]{2,4}$/i.test(value)) {
          callback('请输入正确的邮箱格式')
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})
const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      $_changeUserInfo(userBaseInfo)
        .then((res) => {
          getUserBaseInfo(userBaseInfo.userId)
          success('保存成功')
        })
        .catch((error) => {
          console.log(error)
        })
    }
  })
}
</script>

<style scoped></style>

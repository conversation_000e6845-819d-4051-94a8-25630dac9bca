<template>
  <div class="w-full h-full p-[20px] overflow-hidden">
    <div class="bg-[#FFF] w-full h-full p-[10px] flex flex-col overflow-hidden">
      <!-- 切换消息类型 -->
      <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
        <el-tab-pane v-for="(item, index) in tabsList" :key="index" :label="item.desc" :name="item.value"></el-tab-pane>
      </el-tabs>
      <!-- 搜索条件 -->
      <div class="flex flex-shrink-0 flex-row gap-x-[16px]">
        <el-input style="width: 150px" v-model="query.title" placeholder="消息名称" clearable @change="getReceiveMsgList"> </el-input>
        <div>
          <el-button type="" @click='resetEve'>重置</el-button>
        </div>
      </div>
      <!-- 表格部分 -->
      <div class="w-full flex-1 overflow-hidden">
        <div class="w-full h-full flex flex-col overflow-hidden">
          <div class="py-2">
            <el-button type="danger" @click="delAllEve">删除</el-button>
          </div>
          <ElementTable
              class="flex-1 h-full overflow-hidden"
              ref="table"
              :data="tableData"
              :table-title="tableTitle"
              :page-config="pageConfig"
              @selectChange="handleSelectChange"
              :delMethod="$deleteReceiveMsg"
              :delParams="deleteParams"
              :refreshMethod="getReceiveMsgList"
            >
            <template #templateType="{data: {row}}">
                {{ tabsList.find(item => item.value === row.templateType)?.desc }}
            </template>
            <template #msgContent="{data: {row}}">
                {{ row.msgContent ? JSON.parse(row.msgContent).content : '' }}
            </template>
            <template #lifespan="{data: {row}}">
                <div v-if="row.lifespan == 0">--</div>
                <div class="text-[#7f8c8d]" v-else-if="row.lifespan == -1">已过期</div>
                <div v-else>{{ row.lifespan }}天</div>
            </template>
          </ElementTable>
        </div>
      </div>
    </div>
    <!-- 消息详情弹窗 -->
    <msg-info ref="showMsgRef"/>
    <!-- 批量删除弹窗 -->
    <auto-del ref="autoDelRef" :refreshMethod="getReceiveMsgList" :delMethod="$deleteReceiveMsg"/>
  </div>
</template>

<script setup lang="ts">
import { PageConfigType } from '@/components/ElementTable/index.vue'
import { $queryMsgTypeList, $queryReceiveMsgList, $deleteReceiveMsg } from '@/api/message'
import autoDel from '@/components/DelDialog/autoDel.vue'
import msgInfo from './components/msgInfo.vue';
import { success, warning } from '@/utils/toast.ts'


const activeName = ref<Number | String>('all')

const showMsgRef = ref<any>(null)

const tableData = ref<any[]>([])

const tableTitle: Array<Object | any> = [
  {
    type: 'selection'
  },
  {
    label: '消息名称',
    prop: 'title',
    type: 'text'
  },
  {
    label: '消息类型',
    name: 'templateType',
    type: 'custom'
  },
  {
    label: '来源',
    prop: 'sourceServerName',
    type: 'text'
  },
  {
    label: '消息内容',
    name: 'msgContent',
    type: 'custom'
  },
  {
    label: '创建时间',
    prop: 'createTime',
    type: 'text',
  },
  {
    label: '有效时间',
    name: 'lifespan',
    type: 'custom',
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '详情',
        click: (row: any) => {
          showMsgRef.value.open(row, tabsList.value)
        }
      },
      {
        isLink: true,
        type: 'danger',
        name: '删除',
      }
    ]
  }
]

const pageConfig = ref<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})

// 查询参数
const query = ref<any>({
  current: pageConfig.value.currentPage,
  size: pageConfig.value.pageSize,
  title: '',
  isRead: 1,
  channelType: 5
})

// 重置
const resetEve = () => {
  query.value = {
    current: pageConfig.value.currentPage,
    size: pageConfig.value.pageSize,
    keyword: '',
  }
  getReceiveMsgList()
}

/**
 * 每条页数改变方法
 * @param e 每页条数
 */
 const sizeChange = (e: any) => {
  query.value.size = e
  getReceiveMsgList()
}

/**
 * 页码改变方法
 * @param e 页数
 */
const cuttentChange = (e: any) => {
  query.value.current = e
  getReceiveMsgList()
}

// 获取tabs标签页信息
const tabsList = ref<any[]>([])

const getMessageList = () => {
  $queryMsgTypeList().then((res: any) => {
    if(res.code == 0) {
      tabsList.value = res.data
      activeName.value = res.data[0].value
      getReceiveMsgList()
    }
  })
}

// 获取已读消息列表
const getReceiveMsgList = () => {
  query.value.templateType = activeName.value
  $queryReceiveMsgList(query.value).then((res: any) => {
    if(res.code == 0) {
      tableData.value = res.data.records
      pageConfig.value.total = res.data.total
    }
  })
}

const handleClick = (tab: any) => {
  query.value.templateType = tab
  getReceiveMsgList()
}

// 删除参数设置
const deleteParams = (row:any) => {
  return {ids: [row.id]}
}

// 表格多选操作
const selectList = ref<any[]>([])
const handleSelectChange = (selection: any) => {
  selectList.value = selection
}
  

// 删除全部
const autoDelRef = ref<any>(null)
const delAllEve = () => {
  if(selectList.value.length == 0) {
    return warning('请选择要删除的消息')
  }
  autoDelRef.value.open({ids: selectList.value.map((item: any) => item.id)})
}

onMounted(() => {
  getMessageList()
})
</script>

<style lang="scss" scoped>
</style>
<template>
  <div class="containerBox">
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border"
    >
      <div class="flex flex-row gap-x-[16px]">
        <el-input
          v-model="messageReq.keyword"
          style="width: 150px"
          placeholder="标题"
          clearable
        >
        </el-input>
        <div class="flex flex-row gap-x-[8px] items-center">
          <div>消息类别</div>
          <el-select
            v-model="messageReq.msgType"
            clearable
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="item in messageTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <!-- <div class="flex flex-row gap-x-[8px] items-center">
          <div>消息子类型</div>
          <el-select
            v-model="value"
            clearable
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option
              v-for="item in messageTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
        <div class="flex flex-row gap-x-[8px] items-center">
          <div>时间</div>
          <el-date-picker
            v-model="timeValue"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </div>
        <div>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="reasetSearch">重置</el-button>
        </div>
      </div>
      <div>
        <el-tabs
          v-model="activeName"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane
            :label="`未读消息（${notReadpageConfig.total}）`"
            name="0"
          >
            <div class="flex flex-col gap-y-[16px]">
              <div class="w-full flex justify-end">
                <el-button type="primary" @click="readAll"
                  >全部标记已读</el-button
                >
              </div>
              <div>
                <ElementTable
                  class="h-[750px]"
                  ref="table"
                  :data="notReadTableData"
                  :table-title="tableTitle"
                  :page-config="notReadpageConfig"
                >
                  <template v-slot:msgType="scoped">
                    {{
                      messageTypeOptions.find(
                        (item) => item.value == scoped?.data?.row?.msgType
                      )?.label
                    }}
                  </template>
                  <template v-slot:title="scoped">
                    {{ JSON.parse(scoped?.data?.row?.msgContent)?.title }}
                  </template>
                  <template v-slot:content="scoped">
                    {{ JSON.parse(scoped?.data?.row?.msgContent)?.content }}
                  </template>
                  <template v-slot:pushStatus="scoped">
                    {{
                      pushType?.find(
                        (item) => item.value == scoped?.data?.row?.status
                      )?.label
                    }}
                  </template>
                </ElementTable>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="已读消息" name="1">
            <ElementTable
              class="h-[800px]"
              ref="table"
              :data="tableData"
              :table-title="tableTitle"
              :page-config="pageConfig"
            >
              <template v-slot:msgType="scoped">
                {{
                  messageTypeOptions.find(
                    (item) => item.value == scoped?.data?.row?.msgType
                  )?.label
                }}
              </template>
              <template v-slot:title="scoped">
                {{ JSON.parse(scoped?.data?.row?.msgContent)?.title }}
              </template>
              <template v-slot:content="scoped">
                {{ JSON.parse(scoped?.data?.row?.msgContent)?.content }}
              </template>
            </ElementTable></el-tab-pane
          >
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TabsPaneContext } from 'element-plus'
import { PageConfigType } from '@/components/ElementTable/index.vue'
import { $_getLogsList, $_setRead } from '@/api/logs'
import { $_getDictByType } from '@/api/index'
import { format } from '@/utils/dayjs'
import { success } from '@/utils/toast'

const timeValue = ref('')
const messageTypeOptions = [
  {
    value: 2,
    label: '其他'
  },
  {
    value: 1,
    label: '站内信'
  },
  {
    value: 0,
    label: '公告'
  }
]

// 推送类型
const pushType = ref<any[]>()

const getPushType = async () => {
  let res = await $_getDictByType('msg_send_status')
  pushType.value = res.data
}

const tableTitle: Array<Object | any> = [
  {
    label: '时间',
    prop: 'createTime',
    type: 'time'
  },
  {
    label: '消息类型',
    name: 'msgType',
    type: 'custom'
  },
  // {
  //   label: '消息子类型',
  //   prop: 'orgName',
  //   type: 'text'
  // },
  {
    label: '标题',
    name: 'title',
    type: 'custom'
  },
  {
    label: '内容',
    name: 'content',
    type: 'custom'
  },
  // {
  //   label: '消息目的',
  //   prop: 'roleName',
  //   type: 'text'
  // },
  {
    label: '推送状态',
    name: 'pushStatus',
    type: 'custom'
  }
]

const notReadpageConfig = reactive<PageConfigType>({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
  total: 0,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => {
    notReadpageConfig.pageSize = e
    notReadpageConfig.currentPage = 1
    getLogsList()
  },
  handleCurrentChange: (e: any) => {
    notReadpageConfig.currentPage = e
    getLogsList()
  }
})

const pageConfig = reactive<PageConfigType>({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => {
    pageConfig.pageSize = e
    pageConfig.currentPage = 1
    getLogsList()
  },
  handleCurrentChange: (e: any) => {
    pageConfig.currentPage = e
    getLogsList()
  }
})

// 未读消息
const notReadTableData = ref<any[]>([])

// 已读消息
const tableData = ref<any[]>([])

// 消息请求条件
const messageReq = reactive<{
  keyword: string
  msgType: number | null | undefined
}>({
  keyword: '',
  msgType: null
})

// 获取消息日志
const getLogsList = async () => {
  let obj = {
    isRead: parseInt(activeName.value, 10)
  }
  if (timeValue.value !== '') {
    Object.assign(obj, {
      startTime: format(timeValue.value[0]),
      endTime: format(timeValue.value[1])
    })
  }
  if (activeName.value == '0') {
    Object.assign(obj, {
      current: notReadpageConfig.currentPage,
      size: notReadpageConfig.pageSize
    })
  } else if (activeName.value == '1') {
    Object.assign(obj, {
      current: pageConfig.currentPage,
      size: pageConfig.pageSize
    })
  }
  if (messageReq.msgType !== null && messageReq.msgType !== undefined) {
    Object.assign(obj, { msgType: messageReq.msgType })
  }
  if (messageReq.keyword !== '') {
    Object.assign(obj, { keyword: messageReq.keyword })
  }
  let res = await $_getLogsList(obj)
  if (activeName.value == '0') {
    notReadTableData.value = res.data.records
    notReadpageConfig.total = res.data.total
  } else {
    tableData.value = res.data.records
    pageConfig.total = res.data.total
  }
}

const activeName = ref<string>('0')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  activeName.value = tab.props.name as string
  timeValue.value = ''
  getLogsList()
}

const search = () => {
  pageConfig.currentPage = 1
  notReadpageConfig.currentPage = 1
  getLogsList()
}

const reasetSearch = () => {
  messageReq.keyword = ''
  messageReq.msgType = null
  timeValue.value = ''
  getLogsList()
}

// 全部已读
const readAll = () => {
  let promiseRequset = [] as any[]
  notReadTableData.value.forEach((item: any) => {
    promiseRequset.push($_setRead({ id: item.id }))
  })
  Promise.all(promiseRequset).then((res) => {
    success('全部已读成功')
    notReadpageConfig.currentPage = 1
    getLogsList()
  })
}

onMounted(() => {
  getPushType()
  getLogsList()
})
</script>

<style scoped lang="scss">
:deep(.el-tabs__item.is-active) {
  color: #3665ff;
}
:deep(.el-tabs__item:hover) {
  color: #3665ff;
}
:deep(.el-tabs__active-bar) {
  background: #3665ff;
}
</style>

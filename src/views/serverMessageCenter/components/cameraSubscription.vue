<!-- 分配服务 -->
<template>
  <dialog-view
    title="订阅清单"
    ref="cameraSubscriptionDialog"
    width="98%"
    :needOkBtn="false"
    cancelBtnText="关闭"
    @cancel="handleCancel"
  >
    <shuttle-table></shuttle-table>
  </dialog-view>
</template>

<script setup lang="ts">
import DialogView from '@/components/DialogView/index.vue'
import shuttleTable from './shuttleTable.vue';
import { success, warning } from '@/utils/toast'

const cameraSubscriptionDialog = ref<any>()

const handleCancel = () => {}

const open = () => {
  cameraSubscriptionDialog.value.open()
}
onMounted(() => {})
defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

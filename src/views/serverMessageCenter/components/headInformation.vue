<!--
 * @Author: chenjiang <EMAIL>
 * @Date: 2025-02-08 13:32:09
 * @LastEditors: chenjiang <EMAIL>
 * @LastEditTime: 2025-02-08 14:18:20
 * @FilePath: \citybrain-service-platform-web\src\views\serverMessageCenter\components\headInformation.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- http 加头信息 -->
 <template>
  <div class="flex flex-col gap-y-2">
    <div v-for="(item, index) in header" :key="index" class="item flex">
      <!-- key -->
      <el-input class="mr-2" v-model="item.key" placeholder="Header Key" clearable />
      <!-- 值 -->
      <el-input class="" v-model="item.value" placeholder="Header Value" clearable />
      <span class="w-[120px] text-[#409EFF] cursor-pointer ml-2" @click="addHeader" v-if="index === 0">继续添加</span>
      <span class="w-[80px] cursor-pointer ml-2 text-[#409EFF]" @click="removeHeader(index)" v-if="header.length > 1 && index !== 0">删除</span>
    </div>
  </div>
 </template>
 
 <script setup>
  import { ref } from 'vue'

  const header = defineModel('header')

  const addHeader = () => {
    header.value.push({ key: '', value: '' })
  }

  const removeHeader = (index) => {
    if (header.value.length > 1) {
      header.value.splice(index, 1)
    }
  }
 </script>
 
 <style lang="scss" scoped>
 .el-input {
  width: 180px;
 }
 </style>
<template>
    <el-select :model-value="value" :placeholder="placeholder" filterable remote-show-suffix clearable remote 
        :remote-method="handleRemoteMethod" @change="handleChange" :loading="loading" style="width: 166px;">
        <el-option v-for="item in options" :key="item[defaultProp.key]" :label="item[defaultProp.label]"
            :value="item[defaultProp.value]" />
    </el-select>
</template>

<script setup>
import { $getStreamNodeList1 } from "@/api/message";
import { ref } from "vue";
const props = defineProps({
    value:{
        type:String,
        default:''
    },
    placeholder:{
        type:String,
        default:'请选择'
    },
    defaultProp:{
        type:Object,
        default:{
            label:'label',
            value:'value',
            key:"id"
        }
    }
})

const $emit = defineEmits(['update:value', 'change'])

const options = ref([])
const loading = ref(false)

handleRemoteMethod()

function handleRemoteMethod(query=null) {
    loading.value = true
    let option = { current: 1, size:100 }
    if (query) {
        option = { ...option, nameOrIp : query}
    }else{
        option = { current: 1, size: 100 }
    }
    $getStreamNodeList1(option).then(res=>{
        if(res.code==0){
            options.value = res.data.records.filter(item=>item.name.includes(query))
        }else{
            options.value = []
        }
        loading.value = false
    })
}

function handleChange(val){
    console.log('change改变');
    $emit('update:value', val)
    $emit('change')
}

</script>

<style lang="scss" scoped>

</style>
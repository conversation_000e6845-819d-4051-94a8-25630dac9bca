<template>
  <div
    class="w-full h-full flex flex-row items-center justify-between gap-x-[16px]"
  >
    <div class="w-full flex flex-col gap-y-[16px]">
      <div class="flex flex-col gap-y-[16px]">
        <div class="font-bold">视频列表</div>
        <div class="flex flex-row gap-x-[16px]">
          <el-input
            @input="debounceParamsChange"
            clearable
            v-model="deviceParams.keyword"
            placeholder="名称、地址或位点"
            style="width: 120px"
          />
          <div class="flex flex-row gap-x-[5px] items-center">
            <div>所属节点:</div>
            <JiedianSelect
              v-model="deviceParams.nodeId"
              :defaultProp="{ label: 'name', value: 'id', key: 'id' }"
              @change="debounceParamsChange"
            />
          </div>
          <div class="flex flex-row gap-x-[5px] items-center">
            <div>类型:</div>
            <el-tree-select
              v-model="deviceParams.deviceCategoryId"
              clearable
              :data="deviceTypeList"
              filterable
              :props="{ label: 'name', value: 'id' }"
              style="width: 150px"
              @change="debounceParamsChange"
            />
          </div>
          <div class="flex flex-row gap-x-[5px] items-center">
            <div>在线状态:</div>
            <el-select
              v-model="deviceParams.onlineFlag"
              placeholder="请选择"
              clearable
              filterable
              style="width: 150px"
              @change="debounceParamsChange"
            >
              <el-option key="1" label="在线" value="1"></el-option>
              <el-option key="0" label="离线" value="0"></el-option>
            </el-select>
          </div>
          <el-button type="primary" @click="resetDeviceList">重置</el-button>
        </div>
      </div>
      <ElementTable
        style="height: 500px"
        ref="deviceTableRef"
        :data="devicetableData"
        :table-title="deviceTabelTitle"
        :page-config="devicepageConfig"
        max-height="500px"
        @selectChange="clickEve"
      ></ElementTable>
    </div>
    <div>
      <div>
        <el-button type="primary" @click="checkTranslate"
          ><el-icon><ArrowRightBold /></el-icon
        ></el-button>
      </div>
    </div>
    <div class="w-full flex flex-col gap-y-[16px]">
      <div class="flex flex-col gap-y-[16px]">
        <div class="font-bold">已选视频列表</div>
        <div class="flex flex-row gap-x-[16px]">
          <el-input
            clearable
            v-model="checkedDeviceListFilterParams.keyword"
            placeholder="名称、地址或位点"
            style="width: 120px"
          />
          <div class="flex flex-row gap-x-[5px] items-center">
            <div>在线状态:</div>
            <el-select
              v-model="checkedDeviceListFilterParams.onlineFlag"
              placeholder="请选择"
              clearable
              filterable
              style="width: 120px"
            >
              <el-option key="1" label="在线" value="1"></el-option>
              <el-option key="0" label="离线" value="0"></el-option>
            </el-select>
          </div>
          <div>
            <el-button
              type="primary"
              @click="resetCheckedDeviceListFilterParams"
              >重置</el-button
            >
            <el-button type="danger" @click="deleteCheckedDeviceListFun"
              >删除</el-button
            >
          </div>
        </div>
      </div>
      <ElementTable
        style="height: 500px"
        ref="checkedDeviceTableRef"
        :data="checkedDeviceFilterListOfPage"
        :table-title="tableTitle"
        :page-config="pageConfig"
        max-height="500px"
        @selectChange="clickCheckEve"
      ></ElementTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PageConfigType } from '@/components/ElementTable/index.vue'
import { $_getDeviceList, $getDeviceTypeList } from '@/api/message'
import { useMessageStore } from '@/store/modules/message'
import JiedianSelect from './JiedianSelect.vue'
import { debounce, cloneDeep } from 'lodash-es'

const { checkedDeviceList, setCheckedDeviceList, deleteCheckedDeviceList } =
  useMessageStore()

const deviceTabelTitle: Array<Object | any> = [
  {
    type: 'selection'
  },
  {
    label: '名称',
    prop: 'name',
    type: 'text',
    width: 200
  },
  {
    label: '品牌',
    prop: 'brandName',
    type: 'text'
  },
  {
    label: '类型',
    prop: 'categoryName',
    type: 'text'
  },
  {
    label: 'IP地址',
    prop: 'address',
    type: 'text'
  },
  {
    label: '地址',
    prop: 'siteAddress',
    type: 'text'
  },
  {
    label: '所属组织',
    prop: 'belongName',
    type: 'text'
  },
  {
    label: '所属节点',
    prop: 'nodeName',
    type: 'text'
  },
  {
    label: '状态',
    prop: 'onlineFlag',
    type: 'status',
    options: [
      {
        value: 0,
        type: 'danger',
        text: '离线'
      },
      {
        value: 1,
        type: 'success',
        text: '在线'
      }
    ]
  }
]

const tableTitle: Array<Object | any> = [
  {
    type: 'selection'
  },
  {
    label: '名称',
    prop: 'name',
    type: 'text',
    width: 200
  },
  {
    label: '品牌',
    prop: 'brandName',
    type: 'text'
  },
  {
    label: '类型',
    prop: 'categoryName',
    type: 'text'
  },
  {
    label: 'IP地址',
    prop: 'address',
    type: 'text'
  },
  {
    label: '地址',
    prop: 'siteAddress',
    type: 'text'
  },
  {
    label: '所属组织',
    prop: 'belongName',
    type: 'text'
  },
  {
    label: '所属节点',
    prop: 'nodeName',
    type: 'text'
  },
  {
    label: '状态',
    prop: 'onlineFlag',
    type: 'status',
    options: [
      {
        value: 0,
        type: 'danger',
        text: '离线'
      },
      {
        value: 1,
        type: 'success',
        text: '在线'
      }
    ]
  }
]

const devicetableData = ref<any[]>([])

const devicepageConfig = reactive<PageConfigType>({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => {
    devicepageConfig.currentPage = 1
    devicepageConfig.pageSize = e
    getDeviceList()
  },
  handleCurrentChange: (e: any) => {
    devicepageConfig.currentPage = e
    getDeviceList()
  }
})

const pageConfig = reactive<PageConfigType>({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => {
    pageConfig.currentPage = 1
    pageConfig.pageSize = e
  },
  handleCurrentChange: (e: any) => {
    pageConfig.currentPage = e
  }
})

let deviceParams = reactive<{
  [key: string]: any
}>({
  keyword: '',
  nodeId: '',
  pageQuery:true,
  deviceCategoryId: '',
  onlineFlag: ''
})

// 获取设备列表
const getDeviceList = async () => {
  let obj = {
    current: devicepageConfig.currentPage,
    size: devicepageConfig.pageSize
  } as any
  for (let i of Object.keys(deviceParams)) {
    if (deviceParams[i] !== '') {
      obj[i] = deviceParams[i]
    }
  }
  let res: any = await $_getDeviceList(obj)
  devicepageConfig.total = res.data.total
  devicetableData.value = res.data.records
  showCheckedDeviceList()
}

// 已选中设备列表
const checkedDeviceListItems = ref<any[]>([])

// 获取已选中设备
const clickEve = (vals: any) => {
  checkedDeviceListItems.value = vals
}

// 设备类型
const deviceTypeList = ref<any[]>([])

function getDeviceTypeList() {
  $getDeviceTypeList().then((res: any) => {
    deviceTypeList.value = res.data
  })
}

// 重置设备选项值
const resetDeviceList = () => {
  deviceParams.keyword = ''
  deviceParams.nodeId = ''
  deviceParams.deviceCategoryId = ''
  deviceParams.onlineFlag = ''
  devicepageConfig.currentPage = 1
  getDeviceList()
}

// 选项值变化
const paramsChange = () => {
  devicepageConfig.currentPage = 1
  getDeviceList()
}

const debounceParamsChange = debounce(paramsChange, 300)

// 已选设备过滤项
const checkedDeviceListFilterParams = reactive<{
  [key: string]: any
}>({
  keyword: '',
  onlineFlag: ''
})

// 过滤后的已选设备
const checkedDeviceFilterList = ref<any[]>([])

// 当页已选设备选中列表
const checkedDeviceFilterListOfPage = ref<any[]>([])

watch(
  [checkedDeviceListFilterParams, checkedDeviceList, pageConfig],
  ([value, value2, value3]) => {
    checkedDeviceFilterList.value.length = 0
    checkedDeviceFilterList.value = cloneDeep(value2)
    value.keyword !== '' &&
      (checkedDeviceFilterList.value = cloneDeep(
        checkedDeviceFilterList.value
      ).filter((item: any) => item.name.includes(value.keyword)))
    value.onlineFlag !== '' &&
      value.onlineFlag !== undefined &&
      (checkedDeviceFilterList.value = cloneDeep(
        checkedDeviceFilterList.value
      ).filter((item: any) => item.onlineFlag == value.onlineFlag))
    pageConfig.total = checkedDeviceFilterList.value.length
    checkedDeviceFilterListOfPage.value = cloneDeep(
      checkedDeviceFilterList.value
    ).slice(
      (value3.currentPage - 1) * value3.pageSize,
      value3.currentPage * value3.pageSize
    )
  },
  {
    deep: true,
    immediate: true
  }
)

// 重置已选设备过滤项
const resetCheckedDeviceListFilterParams = () => {
  checkedDeviceListFilterParams.keyword = ''
  checkedDeviceListFilterParams.onlineFlag = ''
}

// 需要删除设备ids
const preDeleteIds = ref<any[]>([])

// 已选列表选中callback
const clickCheckEve = (vals: any) => {
  preDeleteIds.value = cloneDeep(vals).reduce((arr: any, key: any) => {
    arr.push(key.id)
    return arr
  }, [])
}

// 删除已选选项
const deleteCheckedDeviceListFun = () => {
  deleteCheckedDeviceList(preDeleteIds.value)
  showCheckedDeviceList()
}

// 移动选中值
const checkTranslate = () => {
  setCheckedDeviceList(cloneDeep(checkedDeviceListItems.value))
}

// 设备列表ref
const deviceTableRef = ref<any>()

// 已选设备列表ref
const checkedDeviceTableRef = ref<any>()

// 获取已选设备列表并更改选中状态
const showCheckedDeviceList = async () => {
  const checkedDeviceIds = checkedDeviceList.reduce((arr: any, key: any) => {
    arr.push(key.id)
    return arr
  }, [])
  // 清空当前页设备选中状态
  await nextTick()
  if(deviceTableRef.value){
    deviceTableRef.value?.clearSelectionFun()
  const selectRows = devicetableData.value.filter((item: any) =>
    checkedDeviceIds.includes(item.id)
  )
  if (selectRows.length > 0) {
    selectRows.forEach((item) => {
      deviceTableRef.value?.toggleRowSelectionFun(item, true)
    })
  }
  }
}

onMounted(async () => {
 await getDeviceList()
 await getDeviceTypeList()
})
</script>

<style lang="scss" scoped></style>

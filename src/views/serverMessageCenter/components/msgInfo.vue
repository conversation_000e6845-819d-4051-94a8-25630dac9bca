<template>
  <DialogView title="详情" width="800px" ref="dialogview" :needOkBtn="false">
      <el-form label-width="120px">
          <!-- <el-form-item label="消息ID">{{ data.bizId }}</el-form-item> -->
          <el-form-item label="消息名称">{{data.title}}</el-form-item>
          <el-form-item label="消息类型">{{ tabsList.find(it => it.value === data.templateType)?.desc }}</el-form-item>
          <el-form-item label="消息内容">{{data.msgContent ? JSON.parse(data.msgContent).content : '' }}</el-form-item>
          <el-form-item label="来源">{{ data.sourceServerName }}</el-form-item>
          <!-- <el-form-item label="状态">
              <el-tag type="success" v-if="data.status == 2">成功</el-tag>
              <el-tag type="warning" v-else-if="data.status == 3">失败</el-tag>
          </el-form-item> -->
          <el-form-item label="发送时间">{{ formatTime(data.createTime) }}</el-form-item>
          <el-form-item label="有效时间">
            <div v-if="data.lifespan == 0">--</div>
            <div class="text-[#7f8c8d]" v-else-if="data.lifespan == -1">已过期</div>
            <div v-else>{{ data.lifespan }}天</div>
          </el-form-item>
      </el-form>
  </DialogView>
</template>

<script setup>
import { ref } from 'vue';
import DialogView from '@/components/DialogView/index.vue';

const dialogview = ref(null);

const data = ref({});

const tabsList = ref([]);

function open(row, list){
  tabsList.value = list;
  data.value = row;
  console.log("🚀 ~ open ~ data.value:", data.value)
  dialogview.value.open();
}


// 格式化时间
function formatTime(time) {
  if (!time) return ''
  return time.replace(/T/g, ' ').replace(/\..+/, '')
}


defineExpose({
  open
});
</script>

<style lang="scss" scoped>

</style>
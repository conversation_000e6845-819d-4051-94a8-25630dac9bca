<template>
  <div class="containerBox">
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border"
    >
      <div class="flex flex-row gap-x-[16px]">
        <div class="flex flex-row gap-x-[8px] items-center">
          <div>消息类别</div>
          <el-select
            v-model="receiveType"
            clearable
            placeholder="请选择"
            style="width: 150px"
            @clear="
              () => {
                pageConfig.page = 1
                getMessageList()
              }
            "
          >
            <el-option
              v-for="item in receiveTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div>
          <el-button
            type="primary"
            @click="
              () => {
                pageConfig.page = 1
                getMessageList()
              }
            "
            >查询</el-button
          >
        </div>
      </div>
      <div><el-button type="primary" @click="addEnv">新增订阅</el-button></div>
      <div class="f1">
        <ElementTable
          ref="table"
          :data="tableData"
          :table-title="tableTitle"
          :page-config="pageConfig"
          :delMethod="$_deleteMessageDiscription"
          :delParams="openDelDialog"
          :refreshMethod="getMessageList"
        >
          <template v-slot:way="scoped">
            {{
              scoped?.data?.row?.receiveType
                .split(',')
                ?.reduce((arr: any, key: any) => {
                  arr.push(massageType.find((item) => item.code == key)?.desc)
                  return arr
                }, [])
                .join(',')
            }}
          </template>
          <template v-slot:createTime="scoped">
            {{ format(scoped?.data?.row?.createTime) }}
          </template>
          <template v-slot:address="scoped">
            <div
              v-html="
                addressHandle(JSON.parse(scoped?.data?.row?.receiveChannel))
              "
            ></div>
          </template>
        </ElementTable>
      </div>
    </div>
  </div>

  <!-- 新增订阅 -->
  <dialog-view
    ref="dialogViewRef"
    :title="dialogTitle"
    width="30%"
    @confirm="saveAdd"
    :okBtnText="'保存'"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-row>
        <el-col :span="24">
          <el-form-item label="订阅规则">
            <el-select
              v-model="formData.templateId"
              clearable
              placeholder="请选择"
              @change="templateChange"
            >
              <el-option
                v-for="item in messageRoleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="订阅名称">
            <el-input v-model="formData.name" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12" class="pl-[10px]">
          <el-form-item label="订阅级别">
            <el-select
              v-model="formData.subType"
              placeholder="请选择"
              style="width: 150px"
            >
              <el-option
                v-for="item in subTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12" v-if="formData.subType == 1">
          <el-form-item label="订阅清单">
            <el-button @click="showShuttleTable"
              >已选设备（{{ checkedDeviceList.length }}）</el-button
            >
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="消息接收方式">
            <el-select
              v-model="receiveChannelInfoDtoChecked"
              :disabled="formData.templateId == null"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              max-collapse-tags="5"
              placeholder="请选择"
            >
              <el-option
                v-for="item in receiveChannelInfoDtoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="showInfo('email')" :span="24">
          <el-form-item
            label="邮箱"
            prop="receiveChannelInfoDto.emailDtos.address"
          >
            <el-input
              v-model="formData.receiveChannelInfoDto.emailDtos.address"
              placeholder="邮箱地址"
              clearable
            />
          </el-form-item>
          <hr class="my-2">
        </el-col>
        <el-col v-if="showInfo('https')" :span="24">
          <el-form-item label="https">
            <div class="grid grid-cols-3 gap-x-2">
              <el-input
                v-model="
                  formData.receiveChannelInfoDto.httpSendInfoDtos.ipAddress
                "
                placeholder="ip"
                clearable
              />
              <el-input
                v-model="formData.receiveChannelInfoDto.httpSendInfoDtos.port"
                placeholder="port"
                clearable
              />
              <el-input
                v-model="formData.receiveChannelInfoDto.httpSendInfoDtos.uri"
                placeholder="url"
                clearable
              />
            </div>
          </el-form-item>
          <el-form-item label="https头信息">
            <headInformation v-model:header="formData.receiveChannelInfoDto.httpSendInfoDtos.header"/>
          </el-form-item>
          <hr class="my-2">
        </el-col>
        <el-col v-if="showInfo('sms')" :span="24">
          <el-form-item
            label="短信"
            prop="receiveChannelInfoDto.smsSendInfoDtos.phone"
          >
            <el-input
              v-model="formData.receiveChannelInfoDto.smsSendInfoDtos.phone"
              placeholder="短信接收手机号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col v-if="showInfo('mq')" :span="24">
          <!-- host ip 地址输入 -->
          <el-form-item label="host" prop="receiveChannelInfoDto.mqSendDtos.host">
            <el-input v-model="formData.receiveChannelInfoDto.mqSendDtos.host" placeholder="http://ip:port" clearable />
          </el-form-item>
          <!-- topic -->
          <el-form-item label="topic" prop="receiveChannelInfoDto.mqSendDtos.topic">
            <el-input v-model="formData.receiveChannelInfoDto.mqSendDtos.topic" placeholder="请输入topic" clearable />
          </el-form-item>
          <!-- 类型 type -->
          <el-form-item label="类型" prop="receiveChannelInfoDto.mqSendDtos.type">
            <SelectByDict v-model="formData.receiveChannelInfoDto.mqSendDtos.type"  placeholder="请选择类型"
            :reqFn="() => $getDictType('mq_type')" :multiple="false"
            :defaultOptions="{ key: 'value', value: 'value', label: 'label' }" />
          </el-form-item>
          <!-- 用户名 -->
          <el-form-item label="用户名" prop="receiveChannelInfoDto.mqSendDtos.username">
            <el-input v-model="formData.receiveChannelInfoDto.mqSendDtos.username" placeholder="请输入用户名" clearable />
          </el-form-item>
          <!-- 密码 -->
          <el-form-item label="密码" prop="receiveChannelInfoDto.mqSendDtos.password">
            <el-input v-model="formData.receiveChannelInfoDto.mqSendDtos.password" type="password" placeholder="请输入密码" clearable />
          </el-form-item>
          <hr class="my-2">
        </el-col>
      </el-row>
    </el-form>
  </dialog-view>

  <!-- 订阅清单弹窗 -->
  <!-- <cameraSubscription ref="cameraSubscriptionRef" /> -->
</template>

<script setup lang="ts">
import headInformation from './components/headInformation.vue'
import { PageConfigType } from '@/components/ElementTable/index.vue'
import dialogView from '@/components/DialogView/index.vue'
import cameraSubscription from './components/cameraSubscription.vue'
import {
  $_getMessageTypeList,
  $_getMessageList,
  $_getMessageRuleList,
  $_addMessageDiscription,
  $_editMessageDiscriptionType,
  $_deleteMessageDiscription,
  $_getDeviceList
} from '../../api/message/index'
import { error, success } from '@/utils/toast'
import { useUserStore } from '@/store/modules/user'
import { useMessageStore } from '@/store/modules/message'
import { format } from '@/utils/dayjs'
import { cloneDeep } from 'lodash-es'
import { validate } from 'uuid'
import { ref, watch, computed, reactive } from 'vue'
import { $getDictType } from '@/api/dict/index'
import { json } from 'stream/consumers'
import SelectByDict from '@/components/SelectByDict/index.vue'

const { checkedDeviceList, setCheckedDeviceList } = useMessageStore()

const massageType = ref<any[]>([])

// 获取消息类别列表
const getMessageTypeList = async () => {
  let res = await $_getMessageTypeList()
  massageType.value = res.data
}

//消息类别选项
const receiveTypeOptions = computed(() => {
  return massageType.value.map((item: any) => {
    return {
      label: item.desc,
      value: item.code
    }
  })
})

// 消息订阅级别选项
// const subTypeOptions = [
//   {
//     label: '全部',
//     value: 0
//   },
//   {
//     label: '部分',
//     value: 1
//   }
// ]
// 消息模板选项
const messageRoleOptions = ref<any[]>([])

// 获取订阅消息列表
const getMessageRuleList = async () => {
  let res: any = await $_getMessageRuleList()
  messageRoleOptions.value.length = 0
  res.data.records.reduce((arr: any, key: any) => {
    arr.push({
      ...key,
      label: key.name,
      value: key.id
    })
    return arr
  }, messageRoleOptions.value as any[])
}

// 已选订阅消息类型
const receiveChannelInfoDtoChecked = ref<any[]>([])

// 参数显影控制变量
const showInfo = computed(() => {
  return (val: string) => {
    return receiveChannelInfoDtoChecked.value.includes(val)
  }
})


// 订阅清单弹窗
const cameraSubscriptionRef = ref<any>()

const showShuttleTable = () => {
  cameraSubscriptionRef.value.open()
}

interface receiveChannelInfoDtoType {
  emailDtos: {
    address: string
  }
  httpSendInfoDtos: {
    ipAddress: string
    port: string
    uri: string,
    header: any[],
    headers: {}
  }
  smsSendInfoDtos: {
    phone: string
  }
  mqSendDtos: {
    host: string
    topic: string
    type: string | number
    username: string
    password: string
  }
  sseSendInfoDtos: {
    sseKey: string | null
  }
}

const validateEmail = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入邮箱地址'))
  } else if (
    !/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)
  ) {
    callback(new Error('请输入正确的邮箱地址'))
  } else {
    callback()
  }
}

const validatePhone = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入手机号'))
  } else if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}

// 表单验证规则
const rules = {
  receiveChannelInfoDto: {
    emailDtos: {
      address: [
        {
          validator: validateEmail,
          message: '请输入正确的邮箱地址',
          trigger: 'blur'
        }
      ]
    },
    smsSendInfoDtos: {
      phone: [
        {
          validator: validatePhone,
          message: '请输入正确的手机号',
          trigger: 'blur'
        }
      ]
    },
    mqSendDtos: {
      host: [
        {
          required: true,
          message: '请输入host',
          trigger: 'blur'
        },
        {
          // 正则表达式 必须是ip地址 + 端口
          pattern: /^(2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2}(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}:[0-9]+$/,
          message: '请输入正确的IP地址',
          trigger: 'blur'
        }
      ],
      type: [
        {
          required: true,
          message: '请选择类型',
          trigger: 'blur'
        }
      ],
      // username: [
      //   {
      //     required: true,
      //     message: '请输入用户名',
      //     trigger: 'blur'
      //   }
      // ],
      // password: [
      //   {
      //     required: true,
      //     message: '请输入密码',
      //     trigger: 'blur'
      //   }
      // ],
      topic: [
        {
          required: true,
          message: '请输入topic',
          trigger: 'blur'
        }
      ]
    }
  },
}

// 新增订阅
const formData = reactive<{
  templateId: number | null
  name: string
  subType: number
  userIds: string
  devIds: string
  receiveChannelInfoDto: receiveChannelInfoDtoType
}>({
  templateId: null,
  subType: 0,
  name: '',
  devIds: '',
  userIds: useUserStore().userBaseInfo?.userId,
  receiveChannelInfoDto: {
    emailDtos: {
      address: ''
    },
    httpSendInfoDtos: {
      ipAddress: '',
      port: '',
      uri: '',
      headers: {},
      header: [{key: '', value: ''}],
    },
    smsSendInfoDtos: {
      phone: ''
    },
    mqSendDtos: {
      host: '',
      topic: '',
      type: '',
      username: '',
      password: ''
    },
    sseSendInfoDtos: {
      sseKey: ''
    }
  }
})

watch(() => receiveChannelInfoDtoChecked.value, (val) => {
  if(val.length > 0 && val.some(it => it == 'sse')) {
    let userInfo = localStorage.getItem('userInfo')
    if(userInfo) {
      userInfo = JSON.parse(userInfo).user_id || ''
      formData.receiveChannelInfoDto.sseSendInfoDtos.sseKey = userInfo
    }
  } else {
    formData.receiveChannelInfoDto.sseSendInfoDtos.sseKey = ''
  }
})

// 监听订阅消息列表改变
const templateChange = (val: any) => {
  formData.name =
    messageRoleOptions.value.find((item: any) => item.id == val).name || ''
  receiveChannelInfoDtoChecked.value.length = 0
}

// 获取新增列表接收方式选项
const receiveChannelInfoDtoOptions = ref<any[]>([])
watch(
  () => formData.templateId,
  (val: any) => {
    receiveChannelInfoDtoOptions.value.length = 0
    if (val) {
      let res: any = messageRoleOptions.value
        .find((item: any) => item.id == val)
        .supportSendType.filter((item:any)=>item.enabled).map((item:any)=>item.type)
      res.reduce((arr: any, key: any) => {
        arr.push({
          label: massageType.value.find((v) => v.code == key).desc,
          value: key
        })
        return arr
      }, receiveChannelInfoDtoOptions.value)
    } else {
      receiveChannelInfoDtoOptions.value.length = 0
    }
  },
  {
    deep: true
  }
)

// 新增订阅弹窗
const dialogViewRef = ref<any>()
// 弹窗名称
const dialogTitle = ref('')

// 待编辑消息
const editId = ref<any>()

const addEnv = () => {
  initInfo()
  dialogTitle.value = '新增订阅'
  editId.value = null
  dialogViewRef.value.open()
}

const tableData = ref<any[]>([])

const tableTitle: Array<Object | any> = [
  {
    label: '订阅名称',
    prop: 'name',
    type: 'text'
  },
  // {
  //   label: '订阅级别',
  //   name: 'level',
  //   type: 'custom'
  // },
  {
    label: '订阅推送方式',
    name: 'way',
    type: 'custom'
  },
  {
    label: '订阅方接收地址',
    name: 'address',
    type: 'custom'
  },
  {
    label: '订阅消息创建时间',
    name: 'createTime',
    type: 'custom'
  },
  {
    label: '订阅状态',
    prop: 'status',
    type: 'status',
    options: [
      {
        value: 0,
        type: 'success',
        text: '启用'
      },
      {
        value: 1,
        type: 'danger',
        text: '停用'
      }
    ]
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '编辑',
        click: (row: any) => {
          edit(row)
        }
      },
      {
        isLink: true,
        type: 'primary',
        name: '触发器',
        prop: 'status',
        options: [
          {
            value: 1,
            text: '启用订阅'
          },
          {
            value: 0,
            text: '取消订阅'
          }
        ],
        click: (row: any) => {
          $_editMessageDiscriptionType({
            subscribeId: parseInt(row.id, 10),
            open: row.status == 0 ? false : true
          }).then((res: any) => {
            getMessageList()
            success('修改成功')
          })
        }
      },
      {
        isLink: true,
        type: 'danger',
        name: '删除'
      }
    ]
  }
]

// 删除项
const openDelDialog = (row: any) => {
  return row.id
}

// 订阅地址数据处理
const addressHandle = (val: any) => {
  console.log("🚀 ~ addressHandle ~ val:", val)
  let str = ''
  for (let i = 0; i < Object.entries(val).length; i++) {
    const entry = Object.entries(val)[i]
    console.log("🚀 ~ addressHandle ~ entry:", entry)
    const value = entry[1] as any[]
    if (value !== null && entry[0] === 'emailDtos') {
      str += `<div>
      <span>邮箱：</span>
      <span>${value[0]?.address}</span>
      </div>`
    }
    if (value !== null && entry[0] === 'httpSendInfoDtos') {
      str += `<div>
      <span>https:</span>
      <span>${value[0]?.ipAddress}:${value[0]?.port}${value[0]?.uri}</span>
      </div>`
    }
    if (value !== null && entry[0] === 'smsSendInfoDtos') {
      str += `<div>
      <span>短信：</span>
      <span>${value[0]?.phone}</span>
      </div>`
    }
    if (value !== null && entry[0] === 'sseSendInfoDtos') {
      str += `<div>
      <span>Mq：</span>
      <span>${value[0]?.userAccount}/${value[0]?.sseKey}</span>
      </div>`
    }
    if (value !== null && entry[0] === 'mqSendDtos') {
      str += `<div>
      <span>Mq：</span>
      <span>${value[0]?.host}/${value[0]?.topic}</span>
      </div>`
    }
  }
  return str
}

const pageConfig = reactive<PageConfigType>({
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => console.log(e),
  handleCurrentChange: (e: any) => console.log(e)
})

const receiveType = ref<any>('')

// 获取订阅列表
const getMessageList = async () => {
  const paramsObj = {
    current: pageConfig.currentPage,
    size: pageConfig.pageSize,
    receiveType: receiveType.value,
    userIds: useUserStore().userBaseInfo?.userId
  }
  let res: any = await $_getMessageList(paramsObj)
  pageConfig.total = res.data.total
  tableData.value = res.data.records
}

// 编辑订阅信息
const edit = (row: any) => {
  initInfo(row)
  dialogTitle.value = '编辑订阅'
  editId.value = row.id
  dialogViewRef.value.open()
}

// 所有设备列表
const devicetableData = ref<any[]>([])

// 获取所有设备列表
const getDeviceList = async () => {
  let res: any = await $_getDeviceList({
    queryPage: false
  })
  devicetableData.value = res.data.records
}

// 回显已选设备
const returnDevIdsValueFun = (devIds: string) => {
  setCheckedDeviceList([])
  let resList = cloneDeep(devicetableData.value).filter((item: any) => {
    return devIds.split(',').includes(item.id)
  })
  setCheckedDeviceList(cloneDeep(resList))
}

// 订阅信息回显
const initInfo = (row?: any) => {
  if (!row) {
    formData.templateId = null
    formData.subType = 0
    formData.devIds = ''
    formData.name = ''
    receiveChannelInfoDtoChecked.value = []
    formData.receiveChannelInfoDto = {
      emailDtos: {
        address: ''
      },
      httpSendInfoDtos: {
        ipAddress: '',
        port: '',
        uri: '',
        header: [{key: '', value: ''}],
        headers: {}
      },
      smsSendInfoDtos: {
        phone: ''
      },
      mqSendDtos: {
        host: '',
        topic: '',
        type: '',
        username: '',
        password: ''
      },
      sseSendInfoDtos: {
        sseKey: ''
      }
    }
  } else {
    formData.templateId = row.templateId
    formData.name = row.name
    formData.devIds = row.devIds || ''
    formData.subType = row.subType
    let data: any = JSON.parse(row.receiveChannel)
    receiveChannelInfoDtoChecked.value = row.receiveType.split(',')
    formData.receiveChannelInfoDto.emailDtos.address =
      (Array.isArray(data?.emailDtos) && data?.emailDtos[0]?.address) || ''
    formData.receiveChannelInfoDto.httpSendInfoDtos.ipAddress =
      (Array.isArray(data?.httpSendInfoDtos) &&
        data?.httpSendInfoDtos[0]?.ipAddress) ||
      ''
    formData.receiveChannelInfoDto.httpSendInfoDtos.port =
      (Array.isArray(data?.httpSendInfoDtos) &&
        data?.httpSendInfoDtos[0]?.port) ||
      ''
    formData.receiveChannelInfoDto.httpSendInfoDtos.uri =
      (Array.isArray(data?.httpSendInfoDtos) &&
        data?.httpSendInfoDtos[0]?.uri) ||
      ''
    if (data?.httpSendInfoDtos?.[0]?.headers) {
      formData.receiveChannelInfoDto.httpSendInfoDtos.header = Object.entries(data.httpSendInfoDtos[0].headers).map(([key, value]) => ({ key, value }));
    } else {
      formData.receiveChannelInfoDto.httpSendInfoDtos.header = [];
    }
    formData.receiveChannelInfoDto.smsSendInfoDtos.phone =
      (Array.isArray(data?.smsSendInfoDtos) &&
        data?.smsSendInfoDtos[0]?.phone) ||
      ''
    formData.receiveChannelInfoDto.mqSendDtos.host =
      (Array.isArray(data?.mqSendDtos) && data?.mqSendDtos[0]?.host) || ''
    formData.receiveChannelInfoDto.mqSendDtos.type =
      (Array.isArray(data?.mqSendDtos) && data?.mqSendDtos[0]?.type) || ''
    formData.receiveChannelInfoDto.mqSendDtos.topic =
      (Array.isArray(data?.mqSendDtos) && data?.mqSendDtos[0]?.topic) || ''
    formData.receiveChannelInfoDto.mqSendDtos.username =
      (Array.isArray(data?.mqSendDtos) && data?.mqSendDtos[0]?.username) || ''
    formData.receiveChannelInfoDto.mqSendDtos.password =
      (Array.isArray(data?.mqSendDtos) && data?.mqSendDtos[0]?.password) || ''
  }
  returnDevIdsValueFun(formData.devIds)
}

const formRef = ref<any>()

// 保存订阅信息
const saveAdd = async () => {
  await formRef.value.validate()
  if (formData.templateId == null) {
    error('请选择订阅模板', '保存失败')
    return
  }
  if (formData.subType == 1 && checkedDeviceList.length == 0) {
    error('请选择设备', '保存失败')
    return
  }
  formData.devIds = checkedDeviceList
    .reduce((arr: any, key: any) => {
      arr.push(key.id)
      return arr
    }, [])
    .join(',')
  const obj = cloneDeep(formData)
  if (receiveChannelInfoDtoChecked.value.length > 0) {
    if (!receiveChannelInfoDtoChecked.value.includes('https')) {
      delete obj.receiveChannelInfoDto.httpSendInfoDtos
    }
    if (!receiveChannelInfoDtoChecked.value.includes('mq')) {
      delete obj.receiveChannelInfoDto.mqSendDtos
    }
    if (!receiveChannelInfoDtoChecked.value.includes('sms')) {
      delete obj.receiveChannelInfoDto.smsSendInfoDtos
    }
    if (!receiveChannelInfoDtoChecked.value.includes('email')) {
      delete obj.receiveChannelInfoDto.emailDtos
    }
    if(!receiveChannelInfoDtoChecked.value.includes('sse')) {
      delete obj.receiveChannelInfoDto.sseSendInfoDtos
    }
    let isWhole = true
    Object.values(obj.receiveChannelInfoDto).forEach((item: any) => {
      // 获取对象的key和value
      Object.entries(item).forEach(([key, value]: [string, any]) => {
        // 如果是password或username,跳过空值检查
        if (key !== 'password' && key !== 'username' && value === '') {
          isWhole = false
        }
      })
    })
    // 如果当前选择的是 http 并且填写了header 必须有值
    if (obj.receiveChannelInfoDto.httpSendInfoDtos.header.length > 0) {
      const hasEmptyField = obj.receiveChannelInfoDto.httpSendInfoDtos.header.some((item: any) => (item.key === '' && item.value !== '') || (item.value === '' && item.key !== ''));
      if (hasEmptyField) {
        error('请填写完整的头信息', '保存失败');
        return;
      } else {
        const headersMap = new Map();
        // 遍历原始的 headers 数组并转换为 Map
        obj.receiveChannelInfoDto.httpSendInfoDtos.header.forEach((item: any) => {
          if (item.key) {
            headersMap.set(item.key, item.value);
          }
        });
        console.log("🚀 ~ saveAdd ~ headersMap:", headersMap)
        obj.receiveChannelInfoDto.httpSendInfoDtos.headers =  Object.fromEntries(headersMap);
      }
    }
    if (!isWhole) {
      error('请填写完整接收渠道信息', '保存失败')
      return
    }
    if (editId.value !== null) {
      Object.assign(obj, {
        id: editId.value
      })
    }
    let res: any = await $_addMessageDiscription(obj)
    if (res.ok) {
      success('保存成功')
      pageConfig.currentPage = 1
      getMessageList()
      dialogViewRef.value.close()
    }
  } else {
    error('请选择接收渠道', '保存失败')
  }
}

onMounted(() => {
  getMessageTypeList()
  getMessageRuleList()
  getMessageList()
  getDeviceList()
})
</script>

<style scoped></style>

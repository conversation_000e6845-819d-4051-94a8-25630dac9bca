<template>
  <div class="containerBox flex flex-col">
    <!-- <div
      class="w-[182px] h-full fn border-r border-r-[1px] border-[#ddd] bg-white rounded df fdc overflow-hidden"
    >
      <div class="w-full p-[16px] pb-[40px] f1">
        <TreeList
          type="1"
          class="h-full"
          @nodeClick="handleNodeClick"
          @switchChange="handleSwitchChange"
          ref="leftTreeRef"
          :showChange="true"
          @showOtherTree="handleShowOtherTree"
        />
      </div>
    </div> -->

    <!-- <div
      v-if="showTree"
      class="w-[182px] h-full fn border-r border-r-[1px] border-[#ddd] bg-white df fdc overflow-hidden"
    >
      <div class="w-full p-[16px] pb-[40px] f1">
        <TreeList
          type="0"
          class="h-full"
          @nodeClick="handleNodeClick1"
          @switchChange="handleSwitchChange"
          ref="leftTreeRef1"
          :fillId="fillId"
        />
      </div>
    </div> -->
    <div
      class="h-full bg-white ml-[4px] flex-1 fdc flex overflow-hidden relative p-[16px] box-border"
    >
      <div class="content-header">
        <div
          class="header-item"
          style="flex-direction: column; justify-content: center"
        >
          <div class="flex justify-between w-full">
            <span>在线总数:</span>
            <span>90</span>
          </div>
          <div class="flex justify-between w-full">
            <span>物联设备总数:</span>
            <span>100</span>
          </div>
        </div>
        <div
          class="header-item"
          style="flex-direction: column; justify-content: center"
        >
          <div class="flex justify-between w-full">
            <span>自有物联设备在线:</span>
            <span>45</span>
          </div>
          <div class="flex justify-between w-full">
            <span>自有物联设备总数:</span>
            <span>50</span>
          </div>
        </div>
        <div
          class="header-item"
          style="flex-direction: column; justify-content: center"
        >
          <div class="flex justify-between w-full">
            <span>公共物联设备在线:</span>
            <span>45</span>
          </div>
          <div class="flex justify-between w-full">
            <span>公共物联设备总数:</span>
            <span>50</span>
          </div>
        </div>
      </div>
      <div
        class="h-full bg-white ml-[4px] f1 fdc flex overflow-hidden relative p-[16px] box-border"
      >
        <div
          class="flex-shrink-0 w-full px-[24px] flex flex-wrap head"
          ref="searchBox"
          style="height: 66px"
        >
          <el-form
            :model="form"
            inline
            label-position="left"
            style="height: 100%"
            label-width="auto"
            class="flex items-center flex-wrap"
          >
            <el-form-item class="m-[0px] mr-[34px] items-center">
              <el-input
                v-model="form.text"
                clearable
                style="width: 166px"
                placeholder="名称或地址"
              />
            </el-form-item>
            <el-form-item
              label="权限类型"
              class="mr-[34px] items-center m-[0px]"
            >
              <el-select
                v-model="form.option"
                placeholder="请选择"
                style="width: 166px"
                clearable
                filterable
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="m-[0px] items-center">
              <el-button type="primary" style="width: 84px">查询</el-button>
              <el-button style="width: 84px" plain>重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <ElementTable
          ref="table"
          :table-title="tableTitle"
          :page-config="pageConfig"
        ></ElementTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TreeList from './components/TreeList.vue'
import { PageConfigType } from '@/components/ElementTable/index.vue'

const form = reactive<{
  option: string
  text: string
}>({
  option: '',
  text: ''
})

const options = [
  {
    value: 1,
    label: '自建'
  },
  {
    value: 2,
    label: '征用'
  }
]

const showTree = ref<boolean>(false)
const fillId = ref<string>('')

//树列表是否本级
const handleSwitchChange = (data: any) => {
  getFeatures()
}

//树列表点击事件 ,树节点初始加载成功后会触发一次
const handleNodeClick = ({ data }: any) => {
  if (!showTree.value) {
    getFeatures(data)
  } else {
    fillId.value = data.keyFullId
  }
}
const handleNodeClick1 = ({ data }: any) => {
  console.log('组织机构', data)
  getFeatures(data)
}

function handleShowOtherTree(params: any) {
  showTree.value = params
}

const selectType = ref<any>([])
const keyword = ref<string>('')

const leftTreeRef = ref<any>(null)
const leftTreeRef1 = ref<any>(null)

//获取要素列表
function getFeatures(data: any = null) {
  let options: any = {
    keyword: keyword.value,
    curLevel: leftTreeRef.value.switches
  }
  if (showTree.value) {
    //是否本级
    options.curLevel = leftTreeRef1.value.switches
  } else {
    options.curLevel = leftTreeRef.value.switches
  }
  console.log('节点选中数据', leftTreeRef.value.changeItem, data)
  //isAr  0:机构 ， 1:区域
  if (data) {
    //是否点击树节点触发的此次事件
    if (data.isAr == 0) {
      options.orgTreeId = data.keyFullId
    } else {
      options.areaTreeId = data.keyFullId
    }
  } else {
    console.log('xxxxxsss', leftTreeRef.value.getChangeItem())
    if (showTree.value) {
      //判断以哪个树的激活节点为参数
      if (leftTreeRef1.value.getChangeItem().isAr == 0) {
        //判断是否是组织节点
        options.orgTreeId = leftTreeRef1.value.getChangeItem().keyFullId
      } else {
        options.areaTreeId = leftTreeRef1.value.getChangeItem().keyFullId
      }
    } else {
      if (leftTreeRef.value.getChangeItem().isAr == 0) {
        //判断是否是组织节点
        options.orgTreeId = leftTreeRef.value.getChangeItem().keyFullId
      } else {
        options.areaTreeId = leftTreeRef.value.getChangeItem().keyFullId
      }
    }
  }
  options.types = selectType.value
  options.keyword = keyword.value
  console.log('查询', options)
}

const tableTitle: Array<Object | any> = [
  {
    label: '设备ID',
    prop: 'name',
    type: 'text'
  },
  {
    label: '名称',
    prop: 'account',
    type: 'text'
  },
  {
    label: 'IP地址',
    prop: 'orgName',
    type: 'text'
  },
  {
    label: '位点地址',
    prop: 'roleName',
    type: 'text'
  },
  {
    label: '权限类型',
    prop: 'roleName',
    type: 'text'
  },
  {
    label: '状态',
    prop: 'roleName',
    type: 'text'
  },
  {
    label: '操作',
    prop: 'roleName',
    type: 'text'
  }
]

const pageConfig: PageConfigType = {
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => console.log(e),
  handleCurrentChange: (e: any) => console.log(e)
}

const table: any = ref()

onMounted(() => {})
</script>

<style lang="scss" scoped>
.content-header {
  @apply h-120px  p-[16px] pl-0 box-border rounded flex;
  .header-item {
    @apply flex items-center justify-between bg-white w-300px mr-32px border box-border pl-42px pr-42px w-100%;
    span {
      @apply font-weight-bold;
      font-size: 18px;
    }
  }
}
</style>

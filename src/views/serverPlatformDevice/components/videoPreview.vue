<template>
  <dialog-view
    ref="dialogViewRef"
    width="40%"
    class="flex"
    title="视频预览"
    @cancel="cancel"
  >
    <div class="w-full h-400px flex flex-col">
      <div class="left w-[100%] h-full border-r-1 pr-2 flex-shrink-0">
        <common-video :sn="clickItem.sn"></common-video>
      </div>
      <div
        class="right flex-1 h-full flex-shrink-0 mt-4 flex flex-col justify-between pl-4"
      >
        <div class="flex">
          <div class="w-80px text-right mr-4">视频名称</div>
          <div class="whitespace-pre-line">{{ clickItem?.name }}</div>
        </div>
        <div class="flex">
          <div class="w-80px text-right mr-4">协议:</div>
          <div>{{ clickItem?.protocolToken }}</div>
        </div>
        <div class="flex">
          <div class="w-80px text-right mr-4">地址:</div>
          <div>{{ clickItem?.gbid }}</div>
        </div>
        <div class="flex">
          <div class="w-80px text-right mr-4">位点:</div>
          <div>{{ clickItem?.siteAddress }}</div>
        </div>
        <div class="flex">
          <div class="w-80px text-right mr-4">分辨率:</div>
          <div>
            {{
              videoInfo.w && videoInfo.h ? videoInfo.w + '*' + videoInfo.h : '-'
            }}
          </div>
        </div>
        <div class="flex">
          <div class="w-80px text-right mr-4">码流:</div>
          <div>
            {{ videoInfo.bitrate ? videoInfo.bitrate + 'kbps' : '0kbps' }}
          </div>
        </div>
        <div class="flex">
          <div class="w-80px text-right mr-4">帧率:</div>
          <div>{{ videoInfo.fps ? videoInfo.fps + 'fps' : '0fps' }}</div>
        </div>
        <div class="flex">
          <div class="w-80px text-right mr-4">编码格式:</div>
          <div>{{ videoInfo.type || '-' }}</div>
        </div>
      </div>
    </div>
  </dialog-view>
</template>

<script setup lang="ts">
import DialogView from '@/components/DialogView/index.vue'
import commonVideo from './commonVideo.vue'

// 弹窗实例
const dialogViewRef = ref<any>(null)

// 点击项
const clickItem = ref<any>({})

const videoRef = ref<any>(null)

// 视频信息
const videoInfo = ref<any>({})

const infoCallback = (data: any) => {
  videoInfo.value = data
}

/**
 *
 * @param row 当前点击预览的项
 */
const open = (row: any) => {
  clickItem.value = row
  clickItem.value.ip = 'citybrainvideo.xycores.cn'
  clickItem.value.port = '5103'
  dialogViewRef.value?.open()
  nextTick(() => {
    videoRef.value?.startConnect(clickItem.value)
  })
}

const cancel = () => {
  videoInfo.value = {}
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

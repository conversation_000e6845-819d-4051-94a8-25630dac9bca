<!-- 由于播放器部分bug 需要二次封装 -->
<template>
  <div class="w-full h-full bg-black video-cont" :class="isFullScreen ? 'ffixed' : 'rere'">
    <div class="w-full h-full flex items-center justify-center text-[#ecf0f1] text-[14px]" v-if="props.videoUrl == '0'" >
      拖拽视频到此处播放
    </div>
    <!-- 视频播放器 -->
    <video-play ref="videoPlayRef" v-else :sn="props.sn" @timeOut="timeOutEve" @loadingEve="loadingEve" @kbpsEve="(val) => kbps = val" ></video-play>
    <!-- 加载状态 -->
    <div class="loading flex flex-col z-999999" v-if="isLoading">
      <div class="lds-ring"><div></div><div></div><div></div><div></div></div>
      <p class="text-[14px] text-[#ecf0f1]">加载中..</p>
    </div>
    <!-- 错误状态 -->
     <div class="loading errorDiv flex flex-col" v-if="isError">
      <div class="lds-ring">
        <svg-icon name="videoError" style="width: 60%; height: 60%;"></svg-icon>
      </div>
      <p class="text-[14px] text-[#ecf0f1]">视频加载失败，请检查网络或重新加载</p>
     </div>
     <!-- 播放器功能按钮 -->
      <div class="control-btn flex items-center px-4 z-99999" v-if="isControl">
      <!-- <div class="control-btn flex items-center px-4 z-99999"> -->
        <div class="left h-full flex-1 flex items-center">
          <!-- 左边播放 -->
          <div class="btn cursor-pointer">
            <img src="@/assets/images/xgStop.png" v-show="!isPlaying" alt="" title="播放" @click="playOrPauseEve(1)">
            <img src="@/assets/images/xgPlay.png" v-show="isPlaying" alt="" title="暂停" @click="playOrPauseEve(0)">
          </div>
          <div class="h-full flex items-center text-[#fff] text-[14px] ml-4">{{kbps}}/kb</div>
        </div>
        <div class="right h-full flex-1"></div>
          <!-- 云台 -->
          <div class="btn cursor-pointer" v-if="props.videoInfo?.categoryName == '球机'">
            <img src="@/assets/images/yuntai.png" title="云台控制" alt="" @click="yuntaiEve">
          </div>
          <!-- 截屏 -->
          <div class="btn cursor-pointer">
            <img src="@/assets/images/screenshotEve.png" title="截屏" alt="" @click="screenshotEve">
          </div>
          <div class="btn cursor-pointer">
            <img src="@/assets/images/videoRotate.png" title="视频旋转" alt="" @click="rotateView">
          </div>
          <!-- 声音 -->
          <div class="btn cursor-pointer">
            <img src="@/assets/images/sound.png" v-show="!isMute" title="静音" alt="" @click="muteOrUnMuteEve(1)">
            <img src="@/assets/images/nosound.png" v-show="isMute" title="取消静音" alt="" @click="muteOrUnMuteEve(0)">
          </div>
          <!-- 右边全屏 -->
          <div class="btn cursor-pointer">
            <img src="@/assets/images/cssScroll.png" v-show="!isFullScreen" title="全屏" alt="" @click="fullScreenEve(1)">
            <img src="@/assets/images/exitFull.png" v-show="isFullScreen" title="退出全屏" alt="" @click="fullScreenEve(0)">
          </div>
      </div>
  </div>
</template>

<script setup>
import videoPlay from "@/components/CommonVideo/jcVideo.vue"
import { onUnmounted } from "vue";
const $emit = defineEmits('fullEvent','yuntaiEve')
const props = defineProps({
  sn: {
    type: String,
  },
  // 播放器下标
  index: {
    type: Number,
    default: 0,
  },
  videoInfo: {
    type: Object,
  }
})
const videoPlayRef = ref(null)
// 是否加载中
const isLoading = ref(false)
const loadingEve = (val) => {
  console.log('23123123123123123123123123123', val)
  isError.value = false
  isLoading.value = val
  isControl.value = !val
}
// 是否加载错误
const isError = ref(false)

const timeOutEve = () => {
  isLoading.value = false
  isError.value = true
  isControl.value = false
}
// 播放还是暂停
const isPlaying = ref(true)
// 是否暂时控制栏 播放中才显示
const isControl = ref(false)

/**
 * 播放或暂停事件
 * @param {number} val 1: 播放 0: 暂停  
 */
const playOrPauseEve = (val) => {
  // 调用播放器的播放事件
  if(!!val && videoPlayRef.value?.jessibuca) {
    videoPlayRef.value.playVideo()
  }
  if(!val && videoPlayRef.value?.jessibuca) {
    videoPlayRef.value.pauseVideo()
  }
  isPlaying.value = val
}

// 当前是否全屏
const isFullScreen = ref(false)

/**
 * 全屏事件
 * @param {number} val 1: 全屏 0: 退出全屏  
 */
const fullScreenEve = (val) => {
  isFullScreen.value = !!val
  $emit('fullEvent', {type: isFullScreen.value, index: props.index})
}

// 是否静音
const isMute = ref(true)

/**
 * 静音或取消静音事件
 * @param {number} val 1: 静音 0: 取消静音  
 */
const muteOrUnMuteEve = (val) => {
  console.log("🚀 ~ muteOrUnMuteEve ~ val:", val)
  // 调用播放器的静音事件
  videoPlayRef.value.muteVideo(val)
  isMute.value = !!val
}

// 截屏事件
const screenshotEve = () => {
  // 调用播放器的截屏事件
  videoPlayRef.value.screenshotEve()
}

const roatatDeg = ref(0)

// 视频旋转事件
const rotateView = () => {
  if(roatatDeg.value == 270) {
    roatatDeg.value = 0
  } else {
    roatatDeg.value += 90
  }
  videoPlayRef.value.rotateVideo(roatatDeg.value)
}

// 网速
const kbps = ref(0)

// 监听键盘 esc事件退出全屏
// const handleEsc = (event) => {
//   event.preventDefault()
//   if (event.keyCode === 27 && isFullScreen.value) {
//     isFullScreen.value = false
//   }
// }

// 云台事件
const yuntaiEve = () => {
  $emit('yuntaiEve')
}

// 监听键盘事件
// window.addEventListener('keydown', handleEsc)

defineExpose({
  playOrPauseEve
})

onUnmounted(() => {
  // window.removeEventListener('keydown', handleEsc)
})

watch(()=> props.videoInfo, (val) => {
  console.log("🚀 ~ file: commonVideo.vue ~ line 102 ~ watch ~ val", val)
})

</script>

<style lang="scss" scoped>
.video-cont {
  &:hover .control-btn {
    bottom: 0px !important;
  }
}
.ffixed {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 99999 ; // 无穷大
  overflow: hidden;
}
.rere {
  position: relative;
  overflow: hidden;
}
.loading {
  @apply w-full h-[20%] absolute top-[50%] transform -translate-y-1/2 flex items-center justify-center;
  z-index: 9999999;
  .lds-ring,
  .lds-ring div {
    box-sizing: border-box;
  }
  .lds-ring {
    display: inline-block;
    position: relative;
    aspect-ratio: 1/1;
    height: 50%;
  }
  .lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 80%;
    height: 80%;
    // margin: 80%;
    border: 4px solid #ecf0f1;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #ecf0f1 transparent transparent transparent;
  }
  .lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
  }
  .lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
  }
  .lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
  }
  @keyframes lds-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
:deep(.jessibuca-loading) {
  display: none !important;
}

.control-btn {
  @apply  h-[40px] w-full absolute bottom-[-41px] left-0;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 50%, rgb(70, 70, 70) 100%);
  transition: all 0.3s ease-in-out;
  .btn {
    @apply h-[30px] w-[30px] mx-2;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>

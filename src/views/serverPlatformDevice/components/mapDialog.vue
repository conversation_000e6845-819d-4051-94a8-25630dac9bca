<template>
  <dialog-view
    ref="mapDialogRef"
    width="40%"
    class="flex"
    :title="clickItem?.name + '摄像机-地图位置'"
    @cancel="cancel"
    @confirm="confirm"
  >
    <div class="flex mt-4 mb-4 items-center">
      <span>坐标:</span>
      <div class="ml-4 mr-4">
        <span>经度: </span>
        <el-input
          v-model="location.jindu"
          clearable
          style="width: 166px"
          placeholder="经度"
        />
      </div>
      <div class="mr-4">
        <span>纬度: </span>
        <el-input
          v-model="location.weidu"
          clearable
          style="width: 166px"
          placeholder="纬度"
        />
      </div>
      <el-button style="width: 84px" plain @click="changeLocation"
        >确定</el-button
      >
    </div>

    <div class="w-full h-[600px]">
      <mapCom
        ref="mapEL"
        @mapLeftClick="handleClick"
        :mapLng="longitude"
        :mapLat="latitude"
        @update:mapLng="updateLongitude"
        @update:mapLat="updateLatitude"
        width="100%"
        height="100%"
      ></mapCom>
    </div>
  </dialog-view>
</template>

<script setup lang="ts">
import DialogView from '@/components/DialogView/index.vue'
import mapCom from '@/components/mapCom/index.vue'

// 地址
const location = ref<any>({
  jindu: '',
  weidu: ''
})
// 地图部分
const mapEL = ref<any>(null)
//地图点击事件

// 初始坐标
const longitude = ref<any>(101.546242)
const latitude = ref<any>(25.032945)

// 点击位置坐标
const clickedLng = ref<any>(null)
const clickedLat = ref<any>(null)
// 更新坐标的方法
const changeLocation = () => {
  clickedLng.value = +location.value.jindu
  clickedLat.value = +location.value.weidu
  // 自动在该位置添加标记点
  if (mapEL.value) {
    // 先移除旧标记点
    if (mapEL.value.marker) {
      mapEL.value.removePoint(mapEL.value.marker)
    }
    // 添加新标记点
    mapEL.value.marker = mapEL.value.addPoint(
      clickedLat.value,
      clickedLng.value
    )
    mapEL.value.setCenterAndZoom([clickedLng.value, clickedLat.value], 14)
  }
}
const updateLongitude = (val: any) => {
  console.log('更新经度', val)
  longitude.value = val
}
const updateLatitude = (val: any) => {
  console.log('更新纬度', val)

  latitude.value = val
}
const handleClick = (data: any) => {
  console.log('点击', longitude.value)
  clickedLng.value = data.latlng.lng.toFixed(6)
  clickedLat.value = data.latlng.lat.toFixed(6)
  // 自动在该位置添加标记点
  if (mapEL.value) {
    // 先移除旧标记点
    if (mapEL.value.marker) {
      mapEL.value.removePoint(mapEL.value.marker)
    }
    // 添加新标记点
    mapEL.value.marker = mapEL.value.addPoint(data.latlng.lat, data.latlng.lng)
    mapEL.value.setCenterAndZoom([data.latlng.lng, data.latlng.lat], 14)
  }
}

// 弹窗实例
const mapDialogRef = ref<any>(null)

// 点击项
const clickItem = ref<any>({})
/**
 *
 * @param row 当前点击预览的项
 */
const open = (row: any) => {
  console.log(row, '传过来的数据')
  clickItem.value = row
  mapDialogRef.value?.open()
}

const cancel = () => {}
const confirm = () => {
  mapDialogRef.value?.close()
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

<template>
  <div class="flex flex-col w-full h-[100%]">
    <div class="df fdc jcsb pr-[5px] flex-shrink-0" style="align-items: end">
      <div class="df aic cursor-pointer text-[#606266]" v-if="showChange">
        <div
          class="mr-[4px]"
          :style="{ color: checked ? '#3665FF' : '' }"
          @click="handleChangeOrg(!checked)"
        >
          按设备所属组织显示
        </div>
        <el-checkbox v-model="checked" @change="handleChange" />
      </div>
      <div
        class="df aic mb-[5px] cursor-pointer text-[#606266]"
        v-if="!showSwitch"
      >
        <div
          class="mr-[8px]"
          @click="handelSwitchChange1()"
          :style="{ color: switches ? '#3665FF' : '' }"
        >
          仅显示本级
        </div>
        <el-checkbox v-model="switches" @change="handelSwitchChange" />
      </div>
    </div>
    <div
      class="h-[40px] flex-shrink-0 flex w-full justify-center items-center"
      v-if="!hiddenSearch"
    >
      <el-input v-model="search" placeholder="" style="width: 160px">
        <template #suffix>
          <el-icon class="el-input__icon" @click="handelSelect">
            <Search />
          </el-icon>
        </template>
      </el-input>
    </div>
    <div class="pb-[2px] overflow-hidden h-full">
      <el-scrollbar>
        <el-tree
          ref="treeRef"
          highlight-current
          style="width: 100%"
          node-key="id"
          :data="treeList || treeData"
          :default-expanded-keys="openNodeList"
          :props="treeProps"
          :current-node-key="changeNode"
          accordion
          :expand-on-click-node="false"
          :check-on-click-node="true"
          @node-click="handleNodeClick"
          :filter-node-method="filterNode"
        >
          <template #default="{ node, data }">
            <slot
              :node="node"
              :data="data"
              :tree="treeList || treeData"
              v-if="slots.default"
            />
            <div class="w-[120px] truncate" v-else :title="data.name">
              {{ data.name }}
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { computed, nextTick, ref, watch, useSlots } from 'vue'
import { $getOrganizationPageTree } from '@/api/user-organization/index'
import { findTreeNode } from '@/utils/Tools'

const slots: any = useSlots()

const props = defineProps({
  // 树数据
  treeList: {
    //树数据 可不填 自动获取
    type: Array,
    default: null
  },
  type: {
    //选择树类型   1行政区域 0组织机构 2行政区域、政府机构
    type: String,
    default: null,
    required: true
  },
  treeProps: {
    //映射
    type: Object,
    default: () => ({
      children: 'children',
      label: 'name',
      value: 'id'
    })
  },
  // 是否展示 本级按钮
  showSwitch: {
    type: Boolean,
    default: false
  },
  // 是否展示搜索框
  hiddenSearch: {
    type: Boolean,
    default: false
  },
  openNode: {
    //默认展开节点
    type: Array,
    default: () => []
  },
  fillId: {
    //父级fullId
    type: String,
    default: null
  },
  showChange: {
    type: Boolean,
    default: false
  }
})

const $emit = defineEmits(['nodeClick', 'switchChange', 'showOtherTree'])

//选中节点
const changeNode = <any>ref(null)
const treeRef = ref<any>(null)
const switches = ref(false)
const checked = ref(false)
const search = ref('')

const openNodeList: any = computed(() => {
  if (props.openNode.length > 0) {
    return props.openNode
  } else {
    return treeData.value?.map((item: any) => item.id)
  }
})

function handelSelect() {
  if (!treeRef.value) return
  treeRef.value.filter(search.value)
}

//树节点过滤
function filterNode(value: any, data: any) {
  if (!value) return true
  // console.log('节点过滤', value, data);
  return data.name.includes(value)
}

watch(
  () => search.value,
  (val) => {
    if (!treeRef.value) return
    treeRef.value.filter(val)
  }
)

//默认选择第一个节点
watch(
  () => props.treeList,
  (value: any) => {
    if (value?.length <= 0) return
    nextTick(() => {
      if (value?.length > 0) {
        changeNode.value = value[0].id
      } else {
        changeNode.value = null
      }
    })
  },
  { deep: true, immediate: true }
)

//节点点击事件
function handleNodeClick(data: any, node: any, treeNode: any, event: any) {
  openNodeList.value = [data.id]
  changeNode.value = data.id
  $emit('nodeClick', { data, node, treeNode, event })
}

function handelSwitchChange() {
  getTreeData()
  $emit('switchChange', switches.value)
}
function handelSwitchChange1() {
  switches.value = !switches.value
  getTreeData()
  $emit('switchChange', switches.value)
}

function handleChange() {
  $emit(
    'showOtherTree',
    checked.value,
    findTreeNode(treeData.value, changeNode.value, 'id')
  )
}

const treeData = ref<any[]>([])

watch(
  () => props.fillId,
  () => {
    getTreeData()
  },
  { deep: true, immediate: true }
)

getTreeData()
function getTreeData() {
  let path = props.type
  let option = {
    isCurLevel: switches.value
  } as any
  console.log('props.fillId', props.fillId)

  if (props.fillId) {
    option.parentFullId = props.fillId
  }
  $getOrganizationPageTree(path, option).then((res: any) => {
    treeData.value = res.data
    nextTick(() => {
      setTimeout(() => {
        if (treeData.value?.length > 0) {
          changeNode.value = treeData.value[0]?.id
        } else {
          changeNode.value = null
        }
        $emit('nodeClick', {
          data: treeData.value[0],
          node: null,
          treeNode: null,
          event: null
        })
      }, 500)
    })
  })
}

function handleChangeOrg(val: any) {
  checked.value = val
  $emit('showOtherTree', val, changeNode.value)
}

const refushTreeData = () => {
  let path = props.type
  let option = {
    isCurLevel: switches.value
  } as any
  if (props.fillId) {
    option.fillId = props.fillId
  }
  $getOrganizationPageTree(path, option).then((res) => {
    treeData.value = res.data
  })
}

const changeItem = computed(() => {
  if (changeNode.value) {
    nextTick(() => {
      // $emit('nodeClick', { data: treeData.value[0], node: null, treeNode: null, event: null })
      console.log('选中节点', treeRef.value.getCurrentNode())

      return treeRef.value.getCurrentNode()
    })
  } else {
    return {}
  }
})

function getChangeItem() {
  return treeRef.value.getCurrentNode()
}

defineExpose({
  treeData, //树数据
  switches, //本级按钮状态
  changeItem, //当前节点数据
  getChangeItem,
  getTreeData,
  refushTreeData
})
</script>

<style lang="scss" scoped>
.active {
  color: #3665ff;
}

.el-input {
  --el-input-bg-color: #f4f5f7;

  :deep(.el-input__suffix) {
    cursor: pointer;
    padding-right: 11px;
  }

  :deep(.el-input__wrapper) {
    padding-right: 0;
  }
}

:deep(
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content
) {
  // 设置颜色
  background-color: rgba(
    135,
    206,
    235,
    0.2
  ); // 透明度为0.2的skyblue，作者比较喜欢的颜色
  color: #409eff; // 节点的字体颜色
  font-weight: bold; // 字体加粗
}
</style>

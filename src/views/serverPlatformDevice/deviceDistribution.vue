<template>
  <div class="containerBox flex">
    <div class="h-full f1 fdc flex overflow-hidden relative">
      <div
        class="tool top-center df absolute w-[570px] h-[82px] flex justify-evenly"
        style="z-index: 999; right: 40px; top: 40px; backdrop-filter: blur(5px)"
      >
        <div
          class="top-item w-[56px] h-[72px] flex justify-center items-center flex-col"
          v-for="it in options"
        >
          <div
            class="top text-[12px] text-[#5D6A96] h-[21px] w-full flex-shrink-0 flex items-center justify-center"
          >
            {{ it.title }}
          </div>
          <div
            class="bottom text-[14px] text-[#3665ff] flex-1 w-full flex items-center justify-center"
          >
            {{ it.value }}
          </div>
        </div>
      </div>
      <div class="tool top-left">
        <div class="df">
          <el-select
            v-model="selectType"
            @change="handleSelect"
            multiple
            clearable
            tags
            placeholder="请选择"
            style="width: 206px"
          >
            <el-option key="1" label="位点" value="1" />
            <el-option key="2" label="摄像机" value="2" />
          </el-select>
          <div class="ml-[12px]">
            <el-input
              v-model="keyword"
              @change="handleSelect"
              placeholder="请输入关键字"
              style="width: 166px"
              clearable
            ></el-input>
          </div>
        </div>
      </div>
      <!-- </div> -->
      <mapCom
        ref="mapEL"
        @mapLeftClick="handleClick"
        width="100%"
        height="100%"
      />
    </div>
    <!-- 弹窗 -->
    <dialog-view
      :needOkBtn="false"
      cancelBtnText="关闭"
      :title="'&nbsp;'"
      ref="dialogRef"
      width="1000px"
    >
      <div class="df" v-if="showData.type != 1">
        <div class="video-container">
          <H264View ref="videoRef" :data="urlData" />
        </div>
        <div class="right-container">
          <div class="item">
            <div class="title">摄像机名称</div>
            <div class="name">{{ showData.name }}</div>
          </div>
          <div class="item">
            <div class="title">IP地址</div>
            <div class="name">{{ showData.gbId }}</div>
          </div>
          <div class="item">
            <div class="title">所属行政区</div>
            <div class="name">{{ showData.areaAddress }}</div>
          </div>
          <div class="item">
            <div class="title">位点地址</div>
            <div class="name">{{ showData.siteAddress }}</div>
          </div>
          <div class="item">
            <div class="title">经纬度</div>
            <div class="name">
              {{ showData.longitude }},{{ showData.latitude }}
            </div>
          </div>
          <div class="item">
            <div class="title">设备所属</div>
            <div class="name">{{ showData.nodeName }}</div>
          </div>
          <div class="item">
            <div class="title">状态</div>
            <div class="name">
              <div v-if="showData.onlineFlag == 1" class="online">在线</div>
              <div v-else class="offline">离线</div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>{{ showData }}</div>
    </dialog-view>
  </div>
</template>

<script setup lang="ts">
import H264View from '@/components/CommonVideo/h264.vue'
import mapCom from '@/components/mapCom/index.vue'
import { $_deviceDistribution } from '@/api/map'
import TreeList from './components/TreeList.vue'
import DialogView from '@/components/DialogView/index.vue'
import L from 'leaflet'
import { $getCameraDetailById } from '@/api/videoCenter'
import { $getWeather } from '@/api/weatherService'
import axios from 'axios'
// import addressCoordinates from '/static/mapLoctaion/addressCoordinates.json'
const showTree = ref<boolean>(false)
const fillId = ref<string>('')

const options = ref([
  { title: '天气', key: 'text', value: '', suffix: '' },
  { title: '温度', key: 'temp', value: '', suffix: '℃' },
  { title: '湿度', key: 'humidity', value: '', suffix: '%' },
  { title: '气压', key: 'pressure', value: '', suffix: 'Hpa' },
  // { title: "光照", key:'', value: 'weather' },
  { title: '雨量', key: 'precip', value: '', suffix: 'mm' },
  { title: '风速', key: 'windSpeed', value: '', suffix: 'm/s' },
  { title: '风向', key: 'wind360', value: '', suffix: '°' }
])

let cityCode = JSON.parse(localStorage.getItem('areaCode') as string) || ''
getWeather()
function getWeather(params?: any) {
  $getWeather({ cityCode }).then((res: any) => {
    console.log('res', res.data)
    options.value = options.value.map((v) => {
      v.value = res.data[v.key]
      return v
    })
  })
}

function handleShowOtherTree(params: any) {
  showTree.value = params
}

const leftTreeRef = ref<any>(null)
const leftTreeRef1 = ref<any>(null)
const dialogRef = ref<any>(null)
const mapEL = ref<any>(null)
// 播放器实力
const videoRef = ref<any>(null)

onMounted(() => {
  // dialogRef.value.open()
  // console.log('test', addressCoordinates);
  if (cityCode) {
    axios
      .get('/static/mapLoctaion/addressCoordinates.json')
      .then((res: any) => {
        console.log('test', res.data)
        let location = res.data.find((v: any) => v.code == cityCode).loction
        mapEL.value.setCenterAndZoom(location, 16)
      })
  }
})

const deviceList = ref<any>([])
const selectType = ref<any>([])
const keyword = ref<string>('')

//获取要素列表
function getFeatures(data: any = null) {
  let options: any = {
    keyword: keyword.value,
    curLevel: leftTreeRef.value.switches
  }
  if (showTree.value) {
    //是否本级
    options.curLevel = leftTreeRef1.value.switches
  } else {
    options.curLevel = leftTreeRef.value.switches
  }
  console.log('节点选中数据', leftTreeRef.value.changeItem, data)
  //isAr  0:机构 ， 1:区域
  if (data) {
    //是否点击树节点触发的此次事件
    if (data.isAr == 0) {
      options.orgTreeId = data.keyFullId
    } else {
      options.areaTreeId = data.keyFullId
    }
  } else {
    console.log('xxxxxsss', leftTreeRef.value.getChangeItem())
    if (showTree.value) {
      //判断以哪个树的激活节点为参数
      if (leftTreeRef1.value.getChangeItem().isAr == 0) {
        //判断是否是组织节点
        options.orgTreeId = leftTreeRef1.value.getChangeItem().keyFullId
      } else {
        options.areaTreeId = leftTreeRef1.value.getChangeItem().keyFullId
      }
    } else {
      if (leftTreeRef.value.getChangeItem().isAr == 0) {
        //判断是否是组织节点
        options.orgTreeId = leftTreeRef.value.getChangeItem().keyFullId
      } else {
        options.areaTreeId = leftTreeRef.value.getChangeItem().keyFullId
      }
    }
  }
  options.types = selectType.value
  options.keyword = keyword.value
  console.log('查询', options)

  $_deviceDistribution(options).then((res: any) => {
    // console.log('请求结果', res)
    if (res.code == 0) {
      console.log('设备列表', res.data)
      deviceList.value = res.data
      if (keyword.value == '' && selectType.value.length == 0) {
        deviceList.value = []
      }
      handleChangeDevice()
    }
  })
}

onMounted(() => {
  // handleChangeDevice()
})

function handleSelect() {
  console.log('触发')
  getFeatures()
}

let mapLayer: any = null

//图层选中
function handleChangeDevice() {
  if (mapLayer) {
    mapEL.value.removeLayer(mapLayer)
    mapLayer = null
  }
  let layeroptions = {
    spiderfyOnMaxZoom: false,
    showCoverageOnHover: false,
    zoomToBoundsOnClick: false
  }
  mapLayer = mapEL.value.getClusterLayer(layeroptions)
  // data.map(v => {
  // let markData = filterDeviceList(deviceList.value, v)
  deviceList.value.map((item: any) => {
    if (item.latitude && item.longitude) {
      let markerStyle = null
      if (item.type == 1) {
        markerStyle = mapEL.value.setMarkerStyle(
          '/static/mapIcon/wei.png',
          null
        )
      } else {
        markerStyle = mapEL.value.setMarkerStyle(
          '/static/mapIcon/video.png',
          null
        )
      }
      let mark = mapEL.value.getMarker(
        item.latitude,
        item.longitude,
        { icon: markerStyle },
        item
      )
      // layer.addLayer(mark)
      mark.on('click', function (data: any) {
        console.log('marker click', data.target.data)
        if (data.target.data.type == 1) {
          //位点坐标不需弹窗
          var popup = L.popup()
            .setLatLng([item.latitude, item.longitude])
            .setContent(
              `<div style="width:268px;font-size:16px">${data.target.data.name}</div>`
            )
          mapEL.value.setPopups(popup)
        } else {
          //摄像机弹窗
          showPopup(data.target.data)
        }
      })
      mark.addTo(mapLayer)
    }
  })
  // })
}

const showData = ref<any>({})
const urlData = ref<any>({})

async function showPopup(data: any) {
  const res = await $getCameraDetailById({ gbId: data.gbId })
  showData.value = res.data
  urlData.value = {
    ip: 'citybrainvideo.xycores.cn',
    port: '5103',
    token: res.data.sn
  }
  // showData.value = data
  dialogRef.value.open()
  nextTick(() => {
    videoRef.value.startConnect(urlData.value)
  })
  // videoRef.value.startConnect(urlData.value)
}

//筛选设备列表
function filterDeviceList(data: any, type: any) {
  if (!type) return data
  if (type.length == 0) return data
  return data.filter((v: any) => v.type == type)
}

//树列表是否本级
const handleSwitchChange = (data: any) => {
  getFeatures()
}

//树列表点击事件 ,树节点初始加载成功后会触发一次
const handleNodeClick = ({ data }: any) => {
  if (!showTree.value) {
    getFeatures(data)
  } else {
    fillId.value = data.keyFullId
  }
}
const handleNodeClick1 = ({ data }: any) => {
  console.log('组织机构', data)
  getFeatures(data)
}

//地图点击事件
function handleClick(data: any) {
  console.log('地图点击事件', data)
}
</script>

<style lang="scss" scoped>
.online {
  color: #00db58;
}

.offline {
  color: red;
}

.video-container {
  background: #949494;
  width: 598px;
  height: 399px;
}

.right-container {
  margin-left: 40px;

  .item {
    margin-bottom: 11px;

    .title {
      position: relative;
      color: #676767;
      margin-bottom: 4px;

      &::before {
        position: absolute;
        content: '';
        width: 8px;
        height: 8px;
        background: #3665ff;
        top: 50%;
        border-radius: 50%;
        transform: translateY(-50%);
        left: -10px;
      }
    }

    .name {
      color: #333333;
    }
  }
}

.tool {
  position: absolute;
  z-index: 999;
}

.top-left {
  top: 25px;
  left: 25px;
}

.top-right {
  top: 20px;
  right: 50px;
}

.top-center {
  background-image: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 20%,
    #bdcdff 100%,
    #cfdaff
  );

  .top-item {
    // background: url('@/assets/weatherItem.png') center / 100% no-repeat;
  }
}

:deep(.el-select) {
  border: none !important;
}

:deep(.el-tree-node__content) {
  :hover .tree-node-actions {
    display: flex;
  }
}

:deep(.tree-node-actions) {
  display: none;
}
</style>

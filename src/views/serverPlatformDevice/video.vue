<template>
  <div class="containerBox flex w-full h-full overflow-hidden">
    <!-- <div
      class="w-[182px] h-[100%] border-r border-r-[1px] border-[#ddd] bg-white rounded"
    >
      <TreeList
        type="1"
        class="h-full"
        @nodeClick="handleNodeClick"
        @switchChange="handleSwitchChange"
        ref="leftTreeRef"
        :showChange="true"
        @showOtherTree="handleShowOtherTree"
      />
    </div> -->
    <div v-if="showTree" class="w-[182px] bg-white h-full overflow-hidden">
      <TreeList
        type="0"
        class="h-full"
        @nodeClick="handleNodeClick1"
        @switchChange="handleSwitchChange"
        ref="leftTreeRef1"
        :fillId="fillId"
      />
    </div>
    <div
      class="h-full bg-white ml-[4px] flex-1 fdc flex overflow-hidden relative p-[16px] box-border"
    >
      <div class="content-header">
        <div
          class="header-item"
          style="flex-direction: column; justify-content: center"
        >
          <div class="flex justify-between w-full">
            <span>在线总数:</span>
            <span>90</span>
          </div>
          <div class="flex justify-between w-full">
            <span>视频总数:</span>
            <span>100</span>
          </div>
        </div>
        <div
          class="header-item"
          style="flex-direction: column; justify-content: center"
        >
          <div class="flex justify-between w-full">
            <span>自有视频在线:</span>
            <span>45</span>
          </div>
          <div class="flex justify-between w-full">
            <span>自有视频总数:</span>
            <span>50</span>
          </div>
        </div>
        <div
          class="header-item"
          style="flex-direction: column; justify-content: center"
        >
          <div class="flex justify-between w-full">
            <span>公共视频在线:</span>
            <span>45</span>
          </div>
          <div class="flex justify-between w-full">
            <span>公共视频总数:</span>
            <span>50</span>
          </div>
        </div>
      </div>
      <div
        class="flex-shrink-0 w-full flex flex-wrap head"
        ref="searchBox"
        style="height: 46px"
      >
        <el-form
          :model="tablePagination"
          inline
          label-position="left"
          style="height: 100%"
          label-width="auto"
          class="flex items-center flex-wrap"
        >
          <el-form-item class="m-[0px] mr-[34px] items-center">
            <el-input
              v-model="tablePagination.keyword"
              clearable
              style="width: 166px"
              placeholder="名称或地址"
              @change="getCameraList(tablePagination)"
            />
          </el-form-item>
          <el-form-item label="权限类型" class="mr-[34px] items-center m-[0px]">
            <select-by-dict
              v-model="tablePagination.usePerm"
              placeholder="请选择"
              style="width: 166px"
              :reqFn="() => $getDictType('use_perm')"
              :multiple="false"
              :defaultOptions="{ key: 'value', value: 'value', label: 'label' }"
              @change="getCameraList(tablePagination)"
            />
          </el-form-item>
          <el-form-item label="行政区划" class="mr-[34px] items-center m-[0px]">
            <el-tree-select
              v-model="areaTreeName"
              :data="areaTreeList"
              :render-after-expand="false"
              node-key="name"
              @node-click="handleNodeClick1"
              check-strictly
              style="width: 240px"
            >
              <template #default="{ data }"> {{ data.name }}</template>
            </el-tree-select>
          </el-form-item>
          <el-form-item class="m-[0px] items-center">
            <!-- <el-button type="primary" style="width: 84px">查询</el-button> -->
            <el-button style="width: 84px" plain @click="resetEve"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div
        class="flex-shrink-0 w-full flex justify-between head"
        ref="searchBox"
        style="height: 46px"
      >
        <div class="flex">
          <!-- <el-button style="width: 84px" plain @click="resetEve"
            >添加摄像机</el-button
          > -->
          <el-button style="width: 84px" plain @click="resetEve"
            >日志</el-button
          >
          <!-- <el-button style="width: 84px" plain @click="resetEve"
            >删除</el-button
          > -->
        </div>
        <el-button style="width: 84px" plain @click="resetEve">导出</el-button>
      </div>
      <div class="flex-1 overflow-hidden w-full">
        <ElementTable
          ref="table"
          :table-title="tableTitle"
          :page-config="pageConfig"
          :data="cameraList"
        >
          <template #status="{ data: { row } }">
            <div :class="row.onlineFlag ? 'normal' : 'abnormal'">
              {{ row.onlineFlagMark }}
            </div>
          </template>
        </ElementTable>
      </div>
    </div>
    <video-preview ref="dialogViewRef" />
    <map-dialog ref="mapDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import TreeList from './components/TreeList.vue'
import { PageConfigType } from '@/components/ElementTable/index.vue'
import { $getCameraList } from '@/api/videoCenter'
import { $getDictType } from '@/api/dict'
import SelectByDict from '@/components/SelectByDict/index.vue'
import VideoPreview from './components/videoPreview.vue'
import mapDialog from './components/mapDialog.vue'
import { $getOrganizationPageTree } from '@/api/user-organization/index'
import { size } from 'lodash-es'

const showTree = ref<boolean>(false)
const fillId = ref<string>('')
onMounted(() => {
  getCameraList(tablePagination.value)
  getAreaTreeList()
})
// 获取行政区划
const areaTreeList = ref<any>([])
const areaTreeName = ref<any>('')
const getAreaTreeList = () => {
  let path = '1'
  let option = {
    isCurLevel: false
  } as any
  $getOrganizationPageTree(path, option).then((res: any) => {
    areaTreeList.value = res.data
  })
}
//树列表是否本级
const handleSwitchChange = (data: any) => {
  getFeatures()
}

//树列表点击事件 ,树节点初始加载成功后会触发一次
const handleNodeClick = ({ data }: any) => {
  if (!showTree.value) {
    if (!leftTreeRef.value) return
    if (data?.isAr == '1') {
      // tablePagination.value.areaTreeId = leftTreeRef.value.changeItem?.keyFullId
      tablePagination.value.areaTreeId = data?.keyFullId
      tablePagination.value.orgTreeId = ''
    } else {
      // tablePagination.value.orgTreeId = leftTreeRef.value.changeItem?.keyFullId
      tablePagination.value.orgTreeId = data?.keyFullId
      tablePagination.value.areaTreeId = ''
    }
    tablePagination.value.curLevel = leftTreeRef.value.switches
    getCameraList(tablePagination.value)
  }
  // else{
  fillId.value = data.keyFullId
}
// 结构树点击事件
const handleNodeClick1 = (e: any) => {
  console.log('🚀 ~ handleNodeClick1 ~ data:', e)
  if (e?.isAr == '1') {
    tablePagination.value.areaTreeId = e?.keyFullId
  }
  getCameraList(tablePagination.value)
}

function handleShowOtherTree(params: any, val: any) {
  console.log('🚀 ~ handleShowOtherTree ~ params:', params, val)
  showTree.value = params
  if (!params) {
    tablePagination.value.areaTreeId = val?.keyFullId
    tablePagination.value.orgTreeId = ''
    tablePagination.value.curLevel = leftTreeRef1.value.switches
    getCameraList(tablePagination.value)
  }
}

const selectType = ref<any>([])
const keyword = ref<string>('')

const leftTreeRef = ref<any>(null)
const leftTreeRef1 = ref<any>(null)

//获取要素列表
function getFeatures(data: any = null) {
  let options: any = {
    keyword: keyword.value,
    curLevel: leftTreeRef.value.switches
  }
  if (showTree.value) {
    //是否本级
    options.curLevel = leftTreeRef1.value.switches
  } else {
    options.curLevel = leftTreeRef.value.switches
  }
  console.log('节点选中数据', leftTreeRef.value.changeItem, data)
  //isAr  0:机构 ， 1:区域
  if (data) {
    //是否点击树节点触发的此次事件
    if (data.isAr == 0) {
      options.orgTreeId = data.keyFullId
    } else {
      options.areaTreeId = data.keyFullId
    }
  } else {
    console.log('xxxxxsss', leftTreeRef.value.getChangeItem())
    if (showTree.value) {
      //判断以哪个树的激活节点为参数
      if (leftTreeRef1.value.getChangeItem().isAr == 0) {
        //判断是否是组织节点
        options.orgTreeId = leftTreeRef1.value.getChangeItem().keyFullId
      } else {
        options.areaTreeId = leftTreeRef1.value.getChangeItem().keyFullId
      }
    } else {
      if (leftTreeRef.value.getChangeItem().isAr == 0) {
        //判断是否是组织节点
        options.orgTreeId = leftTreeRef.value.getChangeItem().keyFullId
      } else {
        options.areaTreeId = leftTreeRef.value.getChangeItem().keyFullId
      }
    }
  }
  options.types = selectType.value
  options.keyword = keyword.value
  console.log('查询', options)
}

const tableTitle: Array<Object | any> = [
  // {
  //   type: 'selection'
  // },
  {
    label: '名称',
    prop: 'name',
    type: 'text'
  },
  {
    label: '类型',
    prop: 'categoryName',
    type: 'text'
  },
  {
    label: 'IP地址',
    prop: 'gbId',
    type: 'text'
  },
  {
    label: '所在行政区划',
    prop: 'siteAddress',
    type: 'text'
  },
  {
    label: '位点地址',
    prop: 'siteAddress',
    type: 'text'
  },
  {
    label: '服务算法数量',
    prop: 'siteAddress',
    type: 'text'
  },
  {
    label: '权限类型',
    prop: 'usePermMark',
    type: 'text'
  },
  // {
  //   label: '添加时间',
  //   prop: 'usePermMark',
  //   type: 'text'
  // },
  // {
  //   label: '授权有效期',
  //   prop: 'usePermMark',
  //   type: 'text'
  // },
  {
    label: '状态',
    prop: 'onlineFlagMark',
    name: 'status',
    type: 'custom'
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '预览',
        click: (row: any) => previewEve(row, '预览')
      }
      // {
      //   isLink: true,
      //   type: 'primary',
      //   name: '编辑',
      //   click: (row: any) => previewEve(row, '编辑')
      // },
      // {
      //   isLink: true,
      //   type: 'primary',
      //   name: '地图位置',
      //   click: (row: any) => previewEve(row, '地图位置')
      // },
      // {
      //   isLink: true,
      //   type: 'danger',
      //   name: '删除',
      //   click: (row: any) => previewEve(row, '删除')
      // }
    ]
  }
]

const pageConfig = reactive<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => pageChange(e)
})

const table: any = ref()
let orgObj = JSON.parse(localStorage.getItem('userBaseInfo')!).userBaseInfo

// 请求列表参数数据
const tablePagination: any = ref({
  areaTreeId: '',
  grantDataIds: [orgObj.orgId],
  powerSet: true,
  // curLevel: 0,
  // roleId: +orgObj.userId,
  // pageQuery: true,
  current: 1,
  size: 20,
  keyword: ''
})

// 摄像机列表
const cameraList: any = ref([])

// 获取摄像机列表
const getCameraList = (reqObj: any = {}) => {
  $getCameraList(reqObj).then((res: any) => {
    cameraList.value = res.data.records
    pageConfig.total = res.data.total
  })
}

// 重置表格事件
const resetEve = () => {
  console.log('cccccccc')
  pageConfig.currentPage = 1
  tablePagination.value.keyword = ''
  tablePagination.value.areaTreeId = ''
  areaTreeName.value = ''

  getCameraList(tablePagination.value)
}

// 表内预览事件
const dialogViewRef = ref<any>(null)
const mapDialogRef = ref<any>(null)
const previewEve = (row: any, type: any) => {
  switch (type) {
    case '预览':
      console.log(row)
      dialogViewRef.value?.open(row)
      break
    case '编辑':
      console.log(row)
      break
    case '地图位置':
      mapDialogRef.value?.open(row)
      break
    case '删除':
      console.log(row)
      break
  }
}

// 分页事件
const sizeChange = (e: any) => {
  pageConfig.pageSize = e
  tablePagination.value.size = e
  getCameraList(tablePagination.value)
}
const pageChange = (e: any) => {
  pageConfig.currentPage = e
  tablePagination.value.current = e
  getCameraList(tablePagination.value)
}
</script>

<style lang="scss" scoped>
.content-header {
  @apply h-120px  p-[16px] pl-0 box-border rounded flex;
  .header-item {
    @apply flex items-center justify-between bg-white w-300px mr-32px border box-border pl-42px pr-42px w-100%;
    span {
      @apply font-weight-bold;
      font-size: 18px;
    }
  }
}
.head {
  // border-bottom: 1px solid #e2e2e2;
  // :deep(.el-form) {
  // }
  // :deep(.el-form-item) {
  //   // margin-top: 0 !important;
  //   // margin-bottom: 0 !important;
  // }
  :deep(.cjj) {
    width: 250px !important;
    .el-tree {
      width: 250px !important;
    }
  }
}
</style>

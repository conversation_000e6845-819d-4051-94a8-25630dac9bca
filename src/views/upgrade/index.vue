<template>
  <el-card shadow="always">
    <template #header>
      <div>
        <span style="font-size: 700; font-size: 18px">系统升级</span>
      </div>
    </template>
    <el-form
      ref="formRef"
      label-position="left"
      :model="form"
      status-icon
      :rules="rules"
      label-width="150px"
      style="max-width: 460px"
      class="demo-ruleForm"
    >
      <el-form-item label="升级文件:" prop="file">
        <el-upload
          ref="upRef"
          :auto-upload="false"
          :on-change="beforeUpload"
          :limit="1"
          accept=".zip"
        >
          <div class="up-btn">
            <span style="margin-right: 5px">请选择要上传的文件</span>
            <el-icon><FolderOpened /></el-icon>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="btnLoading"
          color="#334054"
          @click="submitEve"
          style="margin-top: 15px; width: 140px"
          >{{ btnTxt }}</el-button
        >
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { $updateFrontProject } from '@/api/upgrade'
import { ref } from 'vue'
import { success } from '@/utils/toast.js'
import { FormInstance } from 'element-plus/es/components/form/index.mjs'

const btnLoading = ref<any>(false)
const formRef = ref<FormInstance>()
const upRef = ref<any>(null)
const form = ref({
  file: ''
})

const btnTxt = ref('点击升级')

const rules = ref({
  file: [{ required: true, message: '请选择要上传的文件', trigger: 'blur' }]
})

// 文件提交
const submitEve = () => {
  formRef.value?.validate((val: any) => {
    if (val) {
      btnLoading.value = btnTxt.value = '升级中...'
      const formData = new FormData()
      formData.append('file', form.value.file)
      $updateFrontProject(formData)
        .then((res: any) => {
          console.log(res.data.code)
          if (res.code === 0) {
            success(res.data)
            form.value.file = ''
            upRef.value.clearFiles()
          }
        })
        .finally(() => {
          btnLoading.value = false
          btnTxt.value = '点击升级'
        })
        .catch((err: any) => {
          console.log(err.msg)
        })
    }
  })
}

// 上传文件之前的方法
const beforeUpload = (file: any) => {
  form.value.file = file.raw
  return true
}
</script>

<style lang="scss" scoped>
.up-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 250px;
  height: 32px;
  border: 1px dashed #f4f4f4;
  color: #ababab;
}
</style>

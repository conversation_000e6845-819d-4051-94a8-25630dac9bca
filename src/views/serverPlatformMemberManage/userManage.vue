<template>
  <div class="containerBox">
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border"
    >
      <div class="flex flex-shrink-0 flex-row gap-x-[16px]">
        <el-input
          style="width: 150px"
          v-model="query.name"
          placeholder="账号、角色、组织机构"
          clearable
          @change="getData"
        >
        </el-input>
        <div>
          <el-button type="" @click="resetEve">重置</el-button>
        </div>
      </div>
      <div class="flex-shrink-0">
        <el-button type="primary" @click="addEve(null)">新增成员</el-button>
        <el-button type="primary" @click="logEve">日志</el-button>
        <!-- <el-button @click="resetPassword">重置密码</el-button>
        <el-button type="danger" @click="batchDelete">删除</el-button> -->
      </div>
      <div class="flex-1">
        <ElementTable
          ref="table"
          :data="tableData"
          :table-title="tableTitle"
          :page-config="pageConfig"
          @selectChange="handleSelectionChange"
          :delMethod="$_deleteMember"
          :refreshMethod="getData"
          :delParams="delParams"
        >
          <template #roleList="{ data: { row } }">
            <el-tag v-for="it in row.roleList">
              {{ it.roleName }}
            </el-tag>
          </template>
        </ElementTable>
      </div>
    </div>
    <!-- 新增用户 -->
    <user-add ref="userAddRef" @confirm="getData"></user-add>
    <!-- 登录密码 -->
    <dialog-view
      title="登录密码"
      ref="passworddialogRef"
      width="300px"
      :footer="false"
    >
      <div class="flex mb-[16px]">
        <div class="tit">用户姓名:</div>
        <div class="tit ml-[16px] font-normal">{{ currentRow.name }}</div>
      </div>
      <div class="flex mb-[16px]">
        <div class="tit">登录账号:</div>
        <div class="tit ml-[16px] font-normal">{{ currentRow.username }}</div>
      </div>
      <div class="flex mb-[16px]">
        <div class="tit">登录密码:</div>
        <div class="tit ml-[16px] font-normal">
          {{ currentRow.unchangedPassword }}
        </div>
      </div>
    </dialog-view>
    <!-- 重置密码 -->
    <dialog-view
      title="重置密码"
      ref="resetPassworddialogRef"
      width="500px"
      @confirm="confirmAddUser"
      @cancel="cancelAddUser"
    >
      <el-form
        :model="resetPasform"
        ref="formRef"
        label-width="auto"
        :rules="rules"
        style="max-width: 600px"
      >
        <el-form-item label="登录密码" prop="password">
          <el-input
            v-model="resetPasform.password"
            type="password"
            placeholder="请输入登录密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetPasform.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>
      </el-form>
    </dialog-view>
    <!-- 批量删除 -->
    <auto-del
      ref="delAllRef"
      :delMethod="$_deleteMember"
      :refreshMethod="getData"
    />

    <!-- 日志 -->
    <Log-model ref="logViewRef" modular="user"></Log-model>
  </div>
</template>

<script setup lang="ts">
import { PageConfigType } from '@/components/ElementTable/index.vue'
import DialogView from '@/components/DialogView/index.vue'
import { ref } from 'vue'
import LogModel from '@/components/LogModel/index.vue'
import {
  $_getMemberList,
  $_resetUserPassword,
  $_deleteMember
} from '@/api/memberManagement'
import userAdd from './components/userAdd.vue'
import { success, warning } from '@/utils/toast.ts'
import autoDel from '@/components/DelDialog/autoDel.vue'

const tableTitle: Array<Object | any> = [
  {
    label: '序号',
    prop: 'index',
    type: 'index',
    width: 60
  },
  {
    label: '账号',
    prop: 'username',
    type: 'text'
  },
  {
    label: '组织机构',
    prop: 'deptName',
    type: 'text'
  },
  {
    label: '角色',
    prop: 'roleName',
    type: 'custom',
    name: 'roleList'
  },
  {
    label: '姓名',
    prop: 'name',
    type: 'text'
  },
  {
    label: '联系电话',
    prop: 'phone',
    type: 'text'
  },
  {
    label: '电子邮箱',
    prop: 'email',
    type: 'text'
  },
  {
    label: '创建时间',
    prop: 'email',
    type: 'text'
  },
  {
    label: '操作',
    type: 'operate',
    width: 250,
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '重置密码',
        click: (row: any) => resetPassword(row)
      },
      {
        isLink: true,
        type: 'primary',
        name: '编辑',
        click: (row: any) => addEve(row)
      },
      {
        isLink: true,
        type: 'primary',
        name: '日志',
        click: (row: any) => logEve(row)
      },
      {
        isLink: true,
        type: 'danger',
        name: '删除'
      }
    ]
  }
]

const pageConfig = ref<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})

// 重置密码事件
const resetPassworddialogRef = ref<any>(null)
const resetPasform = ref<any>({})
const formRef = ref<any>(null)

const resetPassword = (row: any) => {
  resetPasform.value = {}
  resetPassworddialogRef.value.open([row.userId])
}

const confirmAddUser = (val: any) => {
  formRef.value.validate((valid: any) => {
    if (valid) {
      $_resetUserPassword({
        ids: val,
        ...resetPasform.value
      }).then((res: any) => {
        if (res.code == 0) {
          resetPassworddialogRef.value.close()
          getData()
          success('重置密码成功!')
        }
      })
    }
  })
}

const cancelAddUser = () => {}
const rules = ref({
  confirmPassword: [
    { required: true, message: '登录密码不能为空', trigger: 'blur' },
    {
      validator: (
        rule: any,
        value: any,
        callback: (arg0: Error | undefined) => void
      ) => {
        if (value !== resetPasform.value.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback(undefined)
        }
      },
      trigger: ['blur']
    }
  ],
  password: [{ required: true, message: '登录密码不能为空', trigger: 'blur' }]
})

const tableData = ref([])

// 查询参数
const query = ref<any>({
  current: pageConfig.value.currentPage,
  size: pageConfig.value.pageSize,
  name: ''
})

// 重置
const resetEve = () => {
  query.value = {
    current: pageConfig.value.currentPage,
    size: pageConfig.value.pageSize
  }
  getData()
}

// 获取用户列表
const getData = () => {
  $_getMemberList(query.value).then((res: any) => {
    if (res.code == 0) {
      console.log('🚀 ~ $_getMemberList ~ res:', res)
      tableData.value = res.data.records
      pageConfig.value.total = res.data.total
    }
  })
}

// 新增用户弹窗
const userAddRef = ref<any>(null)

// 新增用户
const addEve = (row: any = null) => {
  userAddRef.value.open(row)
}

// 登录密码样式
const rowStyle = (row: any) => {
  if (row.unchangedPassword) {
    return null
  }
  return {
    color: '#bdc3c7'
  }
}

const currentRow = ref<any>({})

// 查看密码
const passworddialogRef = ref<any>(null)
const showPassWord = (row: any) => {
  if (!row.unchangedPassword) {
    warning('密码已修改，无法查看！')
    return
  } else {
    // 打开弹窗 展示密码
    currentRow.value = row
    passworddialogRef.value.open()
  }
}

// 已选中数据
const selectList = ref<any>([])

// 选中方法
const handleSelectionChange = (val: any) => {
  selectList.value = val
}

// 批量删除
const delAllRef = ref<any>(null)
const batchDelete = () => {
  if (selectList.value.length == 0) {
    warning('请先选择用户')
    return
  }
  delAllRef.value.open(selectList.value.map((item: any) => item.userId))
}

// 单个删除参数处理
const delParams = (row: any) => {
  return [row.userId]
}

/**
 * 每条页数改变方法
 * @param e 每页条数
 */
const sizeChange = (e: any) => {
  pageConfig.value.pageSize = e
  getData()
}

/**
 * 页码改变方法
 * @param e 页数
 */
const cuttentChange = (e: any) => {
  pageConfig.value.currentPage = e
  getData()
}

// 日志事件
const logViewRef = ref<any>(null)

const logEve = (row: any) => {
  logViewRef.value.open(row ? row.roleId : '')
}

onMounted(() => {
  getData()
})
</script>

<style scoped></style>

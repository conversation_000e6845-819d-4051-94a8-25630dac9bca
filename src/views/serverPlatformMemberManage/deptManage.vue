<template>
  <div class="w-full h-full flex">
    <div class="right h-full flex flex-col bg-col" style="width: 100%">
      <div class="top flex h-[50px] flex-shrink-0 bg-white">
        <div class="flex gap-x-[60px] items-center text-sm px-[20px]">
          <div @click="handleTabChange(0)" :class="activeTab == 0 ? 'tab_active' : 'tab'">政府机构</div>
          <div @click="handleTabChange(1)" :class="activeTab == 1 ? 'tab_active' : 'tab'">企业</div>
          <div @click="handleTabChange(2)" :class="activeTab == 2 ? 'tab_active' : 'tab'">事业单位</div>
        </div>
      </div>
      <div class="flex-1 bg-white">
        <div class="h-full">
          <div v-show="activeTab == 0" class="bottom w-full flex" style="height: 100%">
            <div class="left w-[181px] flex-shrink-0 py-4 px-2 bg-[#FFF]">
              <leftTreeList type="1" class="h-full" @nodeClick="handleNodeClick" ref="leftTreeRef" :showChange="false"
                showSwitch hiddenSearch />
            </div>
            <div class="flex-1 flex flex-col">
              <div class="h-[66px] flex items-center">
                <div class="flex flex-row flex-shrink-0 gap-x-[16px]">
                  <el-input style="width: 150px" placeholder="角色名称" clearable>
                  </el-input>
                  <el-button type="primary">查询</el-button>
                  <el-button type="primary">日志</el-button>
                </div>
              </div>
              <div class="flex-1 w-[99%]">
                <ElementTable ref="table" :table-title="tableTitle" :data="tableData" :page-config="pageConfig">
                </ElementTable>
              </div>
            </div>
          </div>
          <div v-show="activeTab == 1" class="bottom w-full flex px-[20px] box-border mt-[20px]" style="height: 100%">
            <div class="flex-1 flex flex-col">
              <div class="h-[66px] flex items-center">
                <div class="flex flex-row flex-shrink-0 gap-x-[16px]">
                  <el-input style="width: 150px" placeholder="角色名称" clearable>
                  </el-input>
                  <el-button type="primary">查询</el-button>
                  <el-button type="primary">日志</el-button>
                </div>
              </div>
              <div class="flex-1 w-[99%]">
                <ElementTable ref="table" :table-title="tableTitle1" :data="tableData1" :page-config="pageConfig1">
                </ElementTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 日志弹窗 -->
    <Log-model ref="logViewRef" modular="algorithm_task"></Log-model>
    <!-- 平台资源授权 -->
    <platformResAuth ref="platformResAuthRef"></platformResAuth>
    <!-- 自有资源授权 -->
    <ownResAuth ref="ownResAuthRef"></ownResAuth>
    <!-- 权限清单 -->
    <authList ref="authListRef"></authList>
  </div>
</template>

<script setup lang="ts">
import { PageConfigType } from "@/components/ElementTable/index.vue";
import LogModel from "@/components/LogModel/index.vue";
import leftTreeList from "@/components/TreeLIst/index.vue";
import platformResAuth from "./components/platformResAuth.vue";
import ownResAuth from "./components/ownResAuth.vue";
import authList from "./components/authList.vue";
import { $queryOrganizationAuthList } from "@/api/organizationAuth";

// tabs切换
const activeTab = ref<number>(0);

// 表格数据
const tableData = ref<any>([])

// 表单数据
const tableTitle: Array<Object | any> = [
  {
    label: '序号',
    prop: 'index',
    type: 'index',
  },
  {
    label: '政府机构',
    prop: 'name',
    type: 'text'
  },
  {
    label: '区域',
    prop: 'areaName',
    type: 'text'
  },
  {
    label: '用户数量',
    prop: 'userCount',
    type: 'text'
  },
  {
    label: '授权视频数量',
    prop: 'videoCount',
    type: 'text'
  },
  {
    label: '授权传感器数量',
    prop: 'sensorCount',
    type: 'text'
  },
  {
    label: '授权服务数量',
    prop: 'serviceCount',
    type: 'text'
  },
  {
    label: '操作',
    type: 'operate',
    width: '350',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '平台资源授权',
        click: (row: any) => opPlatformResAuth(row),
      },
      {
        isLink: true,
        type: 'primary',
        name: '自有资源授权',
        click: (row: any) => opOwnResAuth(row),
      },
      {
        isLink: true,
        type: 'primary',
        name: '权限清单',
        click: (row: any) => opAuthList(row),
      },

      {
        isLink: true,
        type: 'primary',
        name: '日志',
        click: (row: any) => logEve(row),
      }
    ]
  }
]

const pageConfig = ref<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 0,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})

// 表格数据
const tableData1 = ref<any>([])

// 表单数据
const tableTitle1: Array<Object | any> = [
  {
    label: '序号',
    prop: 'index',
    type: 'index',
  },
  {
    label: '企业名称',
    prop: 'name',
    type: 'text'
  },
  {
    label: '注册地',
    prop: 'areaName',
    type: 'text'
  },
  {
    label: '用户数量',
    prop: 'userCount',
    type: 'text'
  },
  {
    label: '授权视频数量',
    prop: 'videoCount',
    type: 'text'
  },
  {
    label: '授权传感器数量',
    prop: 'sensorCount',
    type: 'text'
  },
  {
    label: '授权服务数量',
    prop: 'serviceCount',
    type: 'text'
  },
  {
    label: '操作',
    type: 'operate',
    width: '350',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '平台资源授权',
        click: (row: any) => opPlatformResAuth(row),
      },
      {
        isLink: true,
        type: 'primary',
        name: '自有资源授权',
        click: (row: any) => opOwnResAuth(row),
      },
      {
        isLink: true,
        type: 'primary',
        name: '权限清单',
        click: (row: any) => opAuthList(row),
      },

      {
        isLink: true,
        type: 'primary',
        name: '日志',
        click: (row: any) => logEve(row),
      }
    ]
  }
]

const pageConfig1 = ref<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 0,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})

/**
 * 每条页数改变方法
 * @param e 每页条数
 */
const sizeChange = (e: any) => {
  pageConfig.value.pageSize = e
}

/**
 * 页码改变方法
 * @param e 页数
 */
const cuttentChange = (e: any) => {
  pageConfig.value.currentPage = e
}



const form = ref({
  keyword: '',
  orgType: activeTab.value,
  cityCode: ''
})

// 获取组织授权列表
const getOrganizationAuthList = async () => {
  const data = {
    current: pageConfig.value.currentPage,
    size: pageConfig.value.pageSize
  } as any

  Object.entries(form.value).forEach(([key, value]) => {
    if (value || value === 0) {
      data[key] = value;
    }
  });

  const res: any = await $queryOrganizationAuthList(data);
  if (res.code === 0) {
    tableData.value = res.data.records;
    pageConfig.value.total = res.data.total;
  }
}

const handleTabChange = (tab: number) => {
  activeTab.value = tab
  form.value.keyword = ''
  form.value.orgType = tab
  if (activeTab.value !== 2) {
    form.value.cityCode = ''
  }
  getOrganizationAuthList()
}

onMounted(() => {
  getOrganizationAuthList();
})

const handleNodeClick = (node: any) => {
  if (
    form.value.cityCode != node.data.cityCode &&
    node.data.cityCode != "532301000000"
  ) {
    form.value.cityCode = node.data.cityCode;
    getOrganizationAuthList();
  } else {
    form.value.cityCode = "";
    getOrganizationAuthList();
  }
};

const logViewRef = ref<any>()
const logEve = (row: any) => {
  logViewRef.value.open(row ? row.roleId : '')
}

// 打开平台资源授权弹窗
const platformResAuthRef = ref<any>(null);

const opPlatformResAuth = (row: any) => {
  platformResAuthRef.value?.open(row)
}

// 打开自有资源授权弹窗
const ownResAuthRef = ref<any>(null);

const opOwnResAuth = (row: any) => {
  ownResAuthRef.value?.open(row)
}

// 打开权限清单弹窗
const authListRef = ref<any>(null);

const opAuthList = (row: any) => {
  authListRef.value?.open(row)
}
</script>

<style lang="scss" scoped>
.tab {
  cursor: pointer;
  color: #999;
  padding: 0 20px;
  height: 100%;
  line-height: 66px;
}

.tab_active {
  cursor: pointer;
  color: #409eff;
  padding: 0 20px;
  height: 100%;
  line-height: 66px;
  border-bottom: 2px solid #409eff;
}
</style>
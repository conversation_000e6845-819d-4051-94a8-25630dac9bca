<template>
  <div class="containerBox flex-col">
    <div class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border">
      <div class="flex flex-row flex-shrink-0 gap-x-[16px]">
        <el-input style="width: 150px" placeholder="角色名称" clearable>
        </el-input>
        <div>
          <el-button type="primary">查询</el-button>
        </div>
      </div>
      <div class="flex-shrink-0">
        <el-button type="primary" @click="addEve(null)">新增角色</el-button>
        <el-button type="primary" @click="logEve">日志</el-button>
      </div>
      <div class="flex-1">
        <ElementTable ref="table" :table-title="tableTitle" :data="tableData" :page-config="pageConfig"
          :delMethod="$_deleteRole" :refreshMethod="getData" :delParams="delParams">
        </ElementTable>
      </div>
    </div>
    <!-- 新增编辑角色 -->
    <role-add ref="roleAddRef" @confirm="getData"></role-add>
    <!-- 分配视频 -->
    <assign-videos ref="assignVideosRef" :refresh="getData"></assign-videos>
    <!-- 分配服务 -->
    <assign-service ref="assignServiceRef" :refresh="getData"></assign-service>
    <!-- 分配权限 -->
    <set-role ref="setRoleRef"></set-role>
    <!-- 日志 -->
    <Log-model ref="logViewRef" modular="role"></Log-model>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import roleAdd from './components/roleAdd.vue'
import LogModel from "@/components/LogModel/index.vue";
import { $_getRoleList, $_changeRoleStatus, $_deleteRole } from '@/api/memberManagement'
import { PageConfigType } from '@/components/ElementTable/index.vue'
import assignVideos from './components/assignVideos.vue'
import assignService from './components/assignService.vue'
import setRole from './components/setRole.vue'

const tableTitle: Array<Object | any> = [
  {
    label: '序号',
    prop: 'index',
    type: 'index',
    width: 60
  },
  {
    label: '角色名称',
    prop: 'roleName',
    type: 'text'
  },
  {
    label: '组织机构',
    prop: 'roleName',
    type: 'text'
  },
  {
    label: '用户数量',
    prop: 'roleName',
    type: 'text'
  },
  // {
  //   label: '数据权限类型',
  //   prop: 'dsTypeName',
  //   type: 'text'
  // },
  {
    label: '角色状态',
    prop: 'status',
    type: 'switch',
    click: (val: any, row: any) => changeStatus(val, row)
  },
  {
    label: '操作',
    type: 'operate',
    actions: [
      {
        isLink: true,
        type: 'primary',
        name: '编辑',
        click: (row: any) => addEve({ ...row, dsType: row.dsType + '' }),
      },
      {
        isLink: true,
        type: 'primary',
        name: '功能权限',
        click: (row: any) => setRoleEve(row),
      },
      {
        isLink: true,
        type: 'primary',
        name: '日志',
        click: (row: any) => logEve(row),
      },
      // {
      //   isLink: true,
      //   type: 'primary',
      //   name: '分配服务',
      //   click: (row: any) => assignServiceEve(row),
      // },
      // {
      //   isLink: true,
      //   type: 'primary',
      //   name: '分配视频',
      //   click: (row: any) => assignVideoEve(row),
      // },
      // {
      //   isLink: true,
      //   type: 'primary',
      //   name: '分配传感器',
      //   click: (row: any) => console.log('123123'),
      // },
      {
        isLink: true,
        type: 'danger',
        name: '删除',
        click: (row: any) => console.log('123123'),
      }
    ]
  }
]

// 表格数据
const tableData = ref<any>([])

// 获取表格数据
const getData = () => {
  $_getRoleList(query.value).then((res: any) => {
    if (res.code == 0) {
      tableData.value = res.data.records
      pageConfig.value.total = res.data.total
    }
  })
}

const pageConfig = ref<PageConfigType>({
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
})

// 查询参数
const query = ref<any>({
  current: pageConfig.value.currentPage,
  size: pageConfig.value.pageSize,
  roleName: ''
})

/**
 * 每条页数改变方法
 * @param e 每页条数
 */
const sizeChange = (e: any) => {
  pageConfig.value.pageSize = e
  getData()
}

/**
 * 页码改变方法
 * @param e 页数
 */
const cuttentChange = (e: any) => {
  pageConfig.value.currentPage = e
  getData()
}

// 改变当前行状态
const changeStatus = (val: any, row: any) => {
  row.loading = true
  $_changeRoleStatus({ id: row.roleId, type: val == 0 ? true : false }).then((res: any) => {
    if (res.code != 0) {
      row.status = val == 0 ? '9' : '0'
    }
  }).finally(() => {
    row.loading = false
  })
}

// 新增编辑弹窗实例
const roleAddRef = ref<any>(null)

// 新增事件
const addEve = (data: any) => {
  roleAddRef.value.open(data)
}

// 分配视频
const assignVideosRef = ref<any>(null)
const assignVideoEve = (row: any) => {
  console.log(assignVideosRef.value)
  assignVideosRef.value.open(row)
}

// 分配服务
const assignServiceRef = ref<any>(null)
const assignServiceEve = (row: any) => {
  assignServiceRef.value.open(row)
}

// 分配权限
const setRoleRef = ref<any>(null)
const setRoleEve = (row: any) => {
  setRoleRef.value.open(row)
}

// 删除事件
const delParams = (row: any) => {
  return [row.roleId]
}



// 日志事件
const logViewRef = ref<any>(null)

const logEve = (row:any) => {
  logViewRef.value.open(row ? row.roleId : '')
}

onMounted(() => {
  getData()
})
</script>

<style scoped></style>

<template>
  <dialog-view :title="currentItem?.name + ' -- ' + '平台资源授权'" class="bxx" ref="dialogRef" width="50%" :needOkBtn="false"
    cancelBtnText="关闭">
    <div class="content">
      <div class="content-header w-full flex items-center gap-x-[20px]">
        <el-input placeholder="操作账号" v-model="form.keyword" clearable style="width: 166px" />
        <date-picker style="width: 400px;" v-model:startAt="form.startAt" v-model:endAt="form.endAt"
          :showStartAfterTime="false" :showEndAfterTime="false" ref="dataPickerRef" />
        <el-button type="primary">重置</el-button>
      </div>
      <div class="content-body">
        <div class="h-[66px] flex items-center">
          <el-button type="primary" @click="openAddAuth(null)">新增授权</el-button>
        </div>
        <table-view :tableData="tableData" rowKey="userId" :total="tableTotal" :needPagination="true"
          :currentPage="tablePagination.current" :pageSize="tablePagination.size" v-loading="tableLoading"
          @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelect">
          <el-table-column prop="projectName" label="授权项目名称"></el-table-column>
          <el-table-column prop="contractName" label="合同名称"></el-table-column>
          <el-table-column prop="contractNumber" label="合同编号"></el-table-column>
          <el-table-column prop="dateRange1" label="授权时间"></el-table-column>
          <el-table-column prop="dateRange2" label="有效期"></el-table-column>
          <el-table-column label="操作" fixed="right">
            <template #default="{ row }">
              <div class="flex w-full flex-col">
                  <div class="px-2 borr relative">
                    <el-link type="primary" @click="openAddAuth(row)">编辑</el-link>
                  </div>
              </div>
            </template>
          </el-table-column>
        </table-view>
      </div>
    </div>
  </dialog-view>
  <!-- 新增授权 -->
  <addAuth ref="addAuthRef"></addAuth>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import TableView from '@/components/tableView/index.vue'
import DialogView from '@/components/DialogView/index.vue'
import addAuth from './addAuth.vue';
import datePicker from '@/components/DatePicker/index.vue';
import {
  $queryOrderContractList,
} from "@/api/organizationAuth";
import { useTableData } from "@/hooks/tableData";

const currentItem = ref<any>(null)
const dialogRef = ref<any>(null)

const form = reactive({
  keyword: '',
  startAt: '',
  endAt: ''
})

const {
  tableData,
  tableTotal,
  tablePagination,
  getTableData,
  handleSizeChange,
  handleCurrentChange,
  handleSelect,
  tableLoading,
} = useTableData({
  reqFn: $queryOrderContractList,
  fn: (res: { data: { records: any[]; total: any; }; }) => {
    console.log(res);
    tableData.value = res.data.records.map((it: any) => {
      return {
        ...it,
        switchLoading: false,
      };
    });
    tableTotal.value = res.data.total;
  },
});
const open = (row: any) => {
  currentItem.value = row
  tablePagination.value.orgId = row.id
  getTableData();
  dialogRef.value?.open()
}

// 打开新增授权弹窗
const addAuthRef = ref<any>(null);

const openAddAuth = (data:any) => {
  addAuthRef.value?.open({ ...currentItem.value, HETONEID: data })
}

defineExpose({
  open
})
</script>

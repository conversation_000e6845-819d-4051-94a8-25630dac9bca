<!-- 设置权限 -->
 <!-- 角色新增 编辑 -->
<template>
  <dialog-view
    ref="dialogViewRef"
    width="40%"
    title="分配权限"
    @confirm="handleConfirm"
    @cancel="handleClose"
  >
  <div class="df p-2 box-border border border-solid border-[#E2E2E2]">
        <div class="h-[502px] w-[182px] mr-[24px] menuList overflow-hidden">
            <div class="df fdc h-full">
                <div class="fn text-[18px] text-[#3665FF] title aic">
                    <div class="f1">
                        菜单列表
                    </div>
                    <div><el-checkbox :model-value="changeTreeAllChecked"
                            :indeterminate="changeTreeIndeterminate" @change="handleTreeAllChange([])"
                            label="全选" />
                    </div>
                </div>
                <div class="f1 overflow-hidden py-[5px]">
                    <el-scrollbar>
                        <el-tree :props="props" :data="treedata" show-checkbox @check-change="handleCheckChange"
                                    default-expand-all ref="treeEl" node-key="id" :expand-on-click-node="false"
                                    check-on-click-node />
                    </el-scrollbar>
                </div>
            </div>
        </div>
        <div class="f1 h-[502px] overflow-hidden">
            <el-scrollbar>
                <div class="mr-[10px]">
                    <div v-for="item in menuBtnList">
                        <div class="btn-title df aic">
                            <div class="text-[#FF604F]">*</div>
                            <div class="f1">{{ item.name }}</div>
                            <div><el-checkbox :model-value="changeAllChecked(item.btns || [])"
                                    :indeterminate="changeIndeterminate(item.btns || [])"
                                    @change="handleAllChange(item.btns || [])" label="全选" />
                            </div>
                        </div>
                        <div class="df mb-[32px]">
                            <el-checkbox-group v-model="checkBtnList">
                                <el-checkbox v-for="btn in item.btns" :key="btn.id" :label="btn.name"
                                    :value="btn.id">
                                    {{ btn.name }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </div>
    </div>
  </dialog-view>
</template>

<script setup>
import { ref, computed } from 'vue'
import DialogView from '@/components/DialogView/index.vue'
import { $getMenuTree, $getRoleMenu } from "@/api/menu";
import { $_putRoleMenu } from "@/api/role/index.js";
import { cloneDeep } from 'lodash-es'
import { success } from '@/utils/toast.ts'


const props = {
    children: 'children',
    label: 'name',
}

const dialogViewRef = ref(null)
const roleForm = ref({})
const data = ref([])
const treeEl = ref(null)
const checkBtnList = ref([])
const menuBtnList = ref([])

const treedata = computed(() => {
    let temp = filterTreeData(data.value)
    console.log('筛选后', temp);
    return temp
})

//判断是否全部选中
function changeAllChecked(checked) {
    // console.log("checked",checked);
    // if(!checked) return false
    if (checked.length == 0) return false
    let allChecked = true
    for (let i = 0; i < checked.length; i++) {
        if (!checkBtnList.value.includes(checked[i].id)) {
            allChecked = false
            break
        }
    }
    return allChecked
}

//判断是否半选
function changeIndeterminate(checked) {
    let indeterminate = false
    for (let i = 0; i < checked.length; i++) {
        if (checkBtnList.value.includes(checked[i].id) && !changeAllChecked(checked)) {
            indeterminate = true
            break
        }
    }
    return indeterminate
}

const treeIdList = computed(() => {
    function getIds(data) {
        let trmp = []
        data.forEach(item => {
            if (item.children?.length > 0) {
                trmp.push(item.id)
                trmp = [...trmp, ...getIds(item.children)]
            } else {
                trmp.push(item.id)
            }
        })
        return trmp
    }
    if (treedata.value.length == 0) return []
    return getIds(treedata.value)
})

//是否树全选
const changeTreeAllChecked = computed(() => {
    console.log('菜单列表', menuBtnList.value);
    if (!treeEl.value) return false
    let ids = treeEl.value.getCheckedKeys()
    if (menuBtnList.value.length == 0) {
        return false
    } else {
        if (ids.length == treeIdList.value.length) {
            return true
        } else {
            return false
        }
    }
})

//菜单是否半选
const changeTreeIndeterminate = computed(() => {
    if (!treeEl.value) return false
    let ids = treeEl.value.getCheckedKeys()
    if (menuBtnList.value.length != 0) {
        let data = treeEl.value.getHalfCheckedKeys()
        if (data.length > 0) {
            return true
        } else if (ids.length == treeIdList.value.length) {
            return false
        } else {
            return true
        }
    }
    // return false
})

//状态切换
function handleTreeAllChange() {
    console.log("id集合", treeIdList.value);
    if (changeTreeAllChecked.value) {
        //全选状态
        treeEl.value.setCheckedKeys([])
    } else {
        if (changeTreeIndeterminate.value) {
            //半选状态
            treeEl.value.setCheckedKeys(treeIdList.value)
        } else {
            //全不选状态
            treeEl.value.setCheckedKeys(treeIdList.value)
        }
    }
}

//获取菜单列表
function getMenuList(id) {
    $getMenuTree().then(res => {
        if (res.code === 0) {
            data.value = res.data
            getRoleMenuList(id)
        }
    })
}

//筛选出树结构中所有的叶子节点leafIds和其他节点parentIds
function getIds(data, ids = { parentIds: [], leafIds: [] }) {
    data.forEach(item => {
        if (item.children?.length > 0) {
            ids.parentIds.push(item.id)
            ids = getIds(item.children, ids)
        } else {
            ids.leafIds.push(item.id)
        }
    })
    return ids
}

//获取角色菜单权限
function getRoleMenuList(roleId) {
    $getRoleMenu(roleId).then(res => {
        if (res.code === 0) {
            let menuListObj = getIds(treedata.value)
            let menuIds = res.data.filter(item => menuListObj.leafIds.includes(item))
            treeEl.value.setCheckedKeys(menuIds)
            checkBtnList.value = res.data.filter(item => !menuListObj.leafIds.includes(item) && !menuListObj.parentIds.includes(item))
            console.log('筛选节点', getIds(treedata.value), checkBtnList.value);
        }
    })
}


//右边全选按钮
function handleAllChange(item) {
    let ids = item.map(i => i.id)
    if (changeAllChecked(item)) {
        //全选状态
        checkBtnList.value = checkBtnList.value.filter(i => !ids.includes(i))
    } else {
        if (changeIndeterminate(item)) {
            //半选状态
            checkBtnList.value = checkBtnList.value.filter(i => !ids.includes(i))
            checkBtnList.value = [...checkBtnList.value, ...ids]
        } else {
            //全不选状态
            checkBtnList.value = [...checkBtnList.value, ...ids]
        }
    }
}

function handleCheckChange(data, node, checked) {
    console.log(data, node, checked);
    //checked为true时，表示该节点下有子节点被选中，此时不做任何操作
    if (checked) return
    //data.children.length大于零，表示该节点下有子节点，此时不做任何操作
    if (data?.children?.length > 0) {

    } else {
        //data.children.length等于零，表示该节点下没有子节点，此时将该节点的id添加到menuBtnList中
        menuBtnList.value = menuBtnList.value.filter(item => item.id != data.id)
        if (node) { //选中
            menuBtnList.value.push({ id: data.id, name: data.name, btns: getChildren(data.id) })
        } else {
            //取消菜单选中 去掉左边对应页面已选中的按钮权限
            console.log("xxx",getChildren(data.id));

            let ids = getChildren(data.id).map(item => item.id)
            checkBtnList.value = checkBtnList.value.filter(item => !ids.includes(item))
        }
    }
}

//递归去掉树结构中menuType不为1的节点
function filterTreeData(data) {
    let newData = []
    data.forEach(item => {
        if (item.menuType == 0) {
            let dataitem = cloneDeep(item)
            if (dataitem.children) {
                dataitem.children = filterTreeData(dataitem.children)
            }
            newData.push(dataitem)
        }
    })
    return newData
}
function getChildren(id, tree = data.value) {
    let children = []
    tree.forEach(item => {
        if (item.id == id) {
            if(item.children?.length > 0){
                children = item.children
            }else{
                children = []
            }
        } else {
            if (item.children) {
                let temp = getChildren(id, item.children)
                if (temp?.length > 0) {
                    children = temp
                }
            }
        }
    })
    return children
}

const open = (row) => {
    roleForm.value = cloneDeep(row)
    getMenuList(row.roleId)
    dialogViewRef.value.open()
}

function handleConfirm() {
    dialogViewRef.value.loading = true
    let menuIds = treeEl.value.getCheckedKeys()
    let menuIds1 = treeEl.value.getHalfCheckedKeys()
    let roleAllList = [...menuIds, ...menuIds1, ...checkBtnList.value].join(',')
    console.log('总权限列表', roleAllList);
    $_putRoleMenu({ roleId: roleForm.value.roleId, menuIds: roleAllList }).then(res => {
        if (res.code === 0) {
            success('权限设置成功')
            menuBtnList.value = []
            dialogViewRef.value.close()
        }
    }).finally(() => {
        dialogViewRef.value.loading = false
    })
}

function handleClose() {
    menuBtnList.value = []
}


defineExpose({
    open
})
</script>

<style lang="scss" scoped></style>

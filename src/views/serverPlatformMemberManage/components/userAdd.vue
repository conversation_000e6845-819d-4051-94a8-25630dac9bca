<!-- 人员管理  新增人员 -->
<!-- 部门新增 编辑 -->
<template>
  <dialog-view
    ref="dialogViewRef"
    width="450px"
    :title="title ? '编辑成员' : '新增成员'"
    @confirm="confirmEve"
  >
    <el-form
      :model="form"
      label-width="auto"
      ref="formRef"
      :rules="rules"
      style="max-width: 600px"
    >
      <el-form-item label="账号" prop="username">
        <el-input v-model="form.username" />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="部门" prop="deptId">
        <el-tree-select
          v-model="form.deptId"
          :data="treeData"
          :render-after-expand="false"
          :props="{
            value: 'id',
            label: 'name',
            children: 'children',
          }"
        />
      </el-form-item>
      <el-form-item label="角色" prop="role">
        <el-select
          v-model="form.role"
          placeholder="请选择角色"
          multiple
        >
          <el-option
            v-for="item in tableData"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId"
          />
        </el-select>
      </el-form-item>
      <!-- 所属部门传递当前登录用户的部门 -->
      <!-- <el-form-item label="所属部门" prop="name">
        <el-input v-model="form.name" />
      </el-form-item> -->
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="form.phone" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱"/>
      </el-form-item>
    </el-form>
  </dialog-view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DialogView from '@/components/DialogView/index.vue'
import { $_getRoleList, $_addMember,  $_editMember, $_getDeptMenuTree } from '@/api/memberManagement'
import { cloneDeep } from 'lodash-es'
import { success } from '@/utils/toast.ts'

const $emit = defineEmits(['confirm'])

// 弹窗实例
const dialogViewRef = ref<any>(null)

const title = ref<boolean>(false)

// 表单实例
const formRef = ref<any>(null)

// 表单数据
const form = ref<any>({})

// 验证规则
const rules = ref<any>({
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]{4,16}$/, message: '账号只能包含字母、数字、下划线、减号，长度4-16位', trigger: ['blur'] }
  ],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: ['blur'] }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { pattern: /^[0-9a-zA-Z_.-]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+)$/, message: '请输入合法的邮箱地址', trigger: ['blur'] }
  ],
  deptId: [{ required: true, message: '请选择部门', trigger: 'blur' }]
})

// 表单确定事件
const confirmEve = () => {
  // 验证表单
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      dialogViewRef.value.loading = true
      // form.value.role = form.value.roleList.join(',')
      // 部门 传递的是当前登录用户的部门id
      const userBaseInfo = localStorage.getItem('userBaseInfo');
      if (userBaseInfo) {
          form.value.orgId = JSON.parse(userBaseInfo).userBaseInfo.orgId;
      } else {
          // Handle the case where 'userBaseInfo' is not found in localStorage
          console.error('userBaseInfo not found in localStorage');
      }
      dialogViewRef.value.loading = true
      // 新增
      if (!title.value) {
        add()
        return
      }
      // 编辑
      edit()
    }
  })
}

// 新增事件
const add = () => {
  $_addMember(form.value).then((res: any) => {
    if(res.code == 0) {
      success('新增成功')
      $emit('confirm')
      dialogViewRef.value.close()
    }
  }).finally(() => {
    dialogViewRef.value.loading = false
  })
}

// 编辑事件
const edit = () => {
  $_editMember(form.value).then((res: any) => {
    if(res.code == 0) {
      success('编辑成功')
      $emit('confirm')
      dialogViewRef.value.close()
    }
  }).finally(() => {
    dialogViewRef.value.loading = false
  })
}

/**
 * 打开弹窗
 * @param data 部门数据
 */
const open = (data: any) => {
  form.value = {}
  // 编辑
  if (!!data) {
    title.value = true
    form.value = cloneDeep(data)
    form.value.role = form.value.roleList.map((it: any) => it.roleId)
    // 新增
  } else {
    title.value = false
  }
  dialogViewRef.value.open()
}


// 角色数据
const tableData = ref<any>([])

// 查询参数
const query = ref<any>({
  current: 1,
  size: 100,
})

// 获取角色数据
const getData = () => {
  $_getRoleList(query.value).then((res: any) => {
    if(res.code == 0) {
      tableData.value = res.data.records
    }
  })
}

const getdeptId = () => {
  const userBaseInfo = localStorage.getItem('userBaseInfo')
  if(userBaseInfo){
    const id = JSON.parse(userBaseInfo).userBaseInfo.deptId
    return id || '0'
  } 
}

// 查询参数
const queryObj = ref<any>({
  deptName: '',
  parentId: getdeptId()
})

// 部门数据(树结构)
const treeData = ref<any>([])

// 获取部门树结构
const getDeptMenuTree = () => {
  $_getDeptMenuTree(queryObj.value).then((res: any) => {
    console.log('🚀 ~ $_getDeptMenuTree ~ res:', res)
    if (res.code == 0) {
      treeData.value = res.data
    }
  })
}

onMounted(() => {
  getData()
  getDeptMenuTree()
})

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

<template>
  <dialog-view :title="currentItem?.name + ' -- ' + '平台资源授权' + ' -- ' + '新增授权'" class="bxx" ref="dialogRef" width="90%"
    :needOkBtn="true" cancelBtnText="关闭" @confirm="handleConfirm" @cancel="handleCancel">
    <div class="content">
      <div class="content-header">
        <div class="flex gap-x-[15px] items-center">
          <div>授权项目名称</div>
          <el-input placeholder="请输入授权名称" v-model="form.projectName" clearable style="width: 200px"
            @change="changeEve" />
        </div>
        <div class="flex gap-x-[15px] items-center">
          <div>授权时间</div>
          <date-picker style="width: 300px;" v-model:startAt="form.dateRange1" v-model:endAt="form.dateRange2"
            :showStartAfterTime="false" :showEndAfterTime="false" />
        </div>
      </div>
      <div class="content-body">
        <el-tabs type="border-card">
          <el-tab-pane label="服务授权">
            <div class="grid gap-x-[10px] grid-cols-5 h-[620px] overflow-y-auto">
              <div class="col-span-4">
                <table-box :max-height="500" :isSelectNum="true" @handleSelectBox="handleSelectBox1"
                  :DefaultSelect="leftTable1SelectList" ref="leftTableRef1" :apiFn="$queryOrganizationServiceAuthList">
                  <!-- 搜索框 todo -->
                  <template #search="{ tableObj: { tablePagination, clearEve, resetTable, getTableData } }">
                    <el-form-item label="来源" class="m-[0px] mr-[34px] items-center">
                      <el-select v-model="tablePagination.sourceCode" placeholder="请选择" style="width: 116px" filterable
                        @change="getTableData(tablePagination)" clearable>
                        <el-option v-for="item in apiSourceList" :key="item.sourceCode" :label="item.sourceName"
                          :value="item.sourceCode" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="类型" class="m-[0px] mr-[34px] items-center">
                      <el-select v-model="tablePagination.apiTypeId" placeholder="请选择" style="width: 116px" filterable
                        clearable @change="getTableData(tablePagination)">
                        <el-option v-for="item in apiDataList" :key="item.id" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <div class="h-full df aic">
                        <el-button @click="resetTableEve1('left')" style="width: 84px" plain>重置</el-button>
                      </div>
                    </el-form-item>
                  </template>
                  <!-- 表格列 -->
                  <el-table-column type="selection" width="55"></el-table-column>
                  <el-table-column property="apiServerName" label="服务名称" />
                  <el-table-column property="sourceName" label="来源" />
                  <el-table-column prop="apiTypeId" label="类型">
                    <template #default="{ row }">
                      {{apiDataList.filter(item => item.value == row.apiTypeId)[0]?.label}}
                      <!-- <apiTypeName :value="row.apiTypeId" /> -->
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="描述" width="200">
                    <template #default="{ row }">
                      <div class="w-180px truncate cursor-pointer" :title="row.description">
                        {{ row.description }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="isListed" label="上架状态">
                    <template #default="{ row }">
                      <span v-if="row.isListed == 0" style="color: #ff604f">未上架</span>
                      <span v-else-if="row.isListed == 1" style="color: #1a9bec">已上架</span>
                      <span v-else-if="row.isListed == 2" style="">编制中</span>
                      <span v-else>{{ row.isListed }}</span>
                    </template>
                  </el-table-column>
                </table-box>
              </div>
              <!-- 已选清单列表 -->
              <div
                class="col-span-1 flex flex-col border border-solid border-[#E5E7EB] rounded-[4px] mt-[68px] box-border  h-[500px]">
                <div class="flex items-center justify-between border-b border-solid border-[#E5E7EB]">
                  <div class="w-[140px] h-[40px] bg-[#3665FF] text-[#fff] flex items-center justify-center">
                    <span>已选中</span>
                    <span>&nbsp; ({{ leftTable1SelectList.length || 0 }})</span>
                  </div>
                  <div class="mr-[10px] mt-[5px]"> <el-icon style="font-size: 20px;"
                      @click="clearAllLeftTable1SelectList">
                      <Delete />
                    </el-icon></div>
                </div>
                <div class="flex flex-col gap-y-[20px] overflow-auto py-[10px]">
                  <div v-for="(item, index) in leftTable1SelectList" :key="index"
                    class="flex items-center justify-between px-[10px] h-[40px]">
                    <span>{{ item.apiServerName }}</span>
                    <el-icon @click="clearOneLeftTable1SelectList(item)" style="font-size: 20px;">
                      <Delete />
                    </el-icon>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="视频授权">
            <div class="grid gap-x-[10px] grid-cols-5  h-[750px] overflow-y-auto">
              <div class="col-span-4 h-full flex gap-x-[10px]">
                <leftTreeList style="width:200px" type="1" class="h-full" @nodeClick="handleNodeClick"
                  ref="leftTreeRef2" :showType="false" :showSwitches="false" :showSearch="false" />
                <table-box :max-height="500" :isSelectNum="true" :DefaultSelect="leftTable2SelectList"
                  @handleSelectBox="handleSelectBox2" tableTitle="已分配" ref="leftTableRef2" :apiFn="$getCameraList">
                  <!-- 搜索框 todo -->
                  <template #search="{ tableObj: { tablePagination, clearEve, resetTable, getTableData } }">
                    <el-form-item class="m-[0px] mr-[34px] items-center">
                      <el-input v-model="tablePagination.keyword" clearable @change="getTableData(tablePagination)"
                        style="width: 116px;" placeholder="名称" />
                    </el-form-item>
                    <el-form-item class="m-[0px] mr-[34px] items-center">
                      <el-tree-select v-model="tablePagination.orgIds" multiple collapse-tags collapse-tags-tooltip
                        :data="selectOrganization" filterable :loading="selectLoading" :render-after-expand="false"
                        check-strictly :props="treeOptions" placeholder="所属组织" style="width: 550px;"
                        :max-collapse-tags="2" clearable @change="getTableData(tablePagination)" />
                    </el-form-item>
                    <div class="h-full df aic">
                      <!-- <el-button type="primary" style="width: 84px" @click="getTableData(tablePagination)">查询</el-button> -->
                      <el-button @click="resetTableEve2('left')" style="width: 84px" type="primary" plain>重置</el-button>
                    </div>
                  </template>
                  <!-- 表格列 -->
                  <el-table-column type="selection" width="55"></el-table-column>
                  <el-table-column property="name" label="名称">
                    <template #="{ row }">
                      <div style="text-align: left; ">
                        {{ row.name }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column property="nodeName" label="所属节点" />
                  <el-table-column property="belongName" label="所属组织" />
                </table-box>
              </div>
              <!-- 已选清单列表 -->
              <div
                class="col-span-1 flex flex-col border border-solid border-[#E5E7EB] rounded-[4px] mt-[68px]  h-[650px]">
                <div class="flex items-center justify-between border-b border-solid border-[#E5E7EB]">
                  <div class="w-[140px] h-[40px] bg-[#3665FF] text-[#fff] flex items-center justify-center">
                    <span>已选中</span>
                    <span>&nbsp; ({{ leftTable2SelectList.length || 0 }})</span>
                  </div>
                  <div class="mr-[10px] mt-[5px]"> <el-icon @click="clearAllLeftTable2SelectList"
                      style="font-size: 20px;">
                      <Delete />
                    </el-icon></div>
                </div>
                <div class="flex flex-col gap-y-[20px] overflow-auto h-full">
                  <div v-for="(item, index) in leftTable2SelectList" :key="index"
                    class="flex items-center justify-between px-[10px] h-[40px]">
                    <span>{{ item.name }}</span>
                    <el-icon @click="clearOneLeftTable2SelectList(item)" style="font-size: 20px;">
                      <Delete />
                    </el-icon>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="物联授权">
          </el-tab-pane>
          <el-tab-pane label="数据授权">Task</el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </dialog-view>
</template>

<script setup>
import DialogView from '@/components/DialogView/index.vue'
import LeftTreeList from "@/components/LeftTreeLIst/index.vue";
import tableBox from '@/components/tabTransView/table-box.vue'
import datePicker from '@/components/DatePicker/index.vue';
import { $_getSysApiSourceList, $getOrganizationType } from "@/api/memberManagement";
import { $queryOrganizationServiceAuthList, $createOrderContract, $updateOrderContract } from '@/api/organizationAuth'
import { $getCameraList } from '@/api/videoCenter'
import { $getDeptPage } from '@/api/memberManagement'
import { success, warning } from "@/utils/toast";
import { nextTick, onMounted } from 'vue';
import dayjs from 'dayjs'

const currentItem = ref(null)
const dialogRef = ref(null)

const form = ref({
  id: null,
  orgId: null,
  projectName: '',
  contractName: '',
  contractNumber: '',
  remark: '',
  dateRange1: '',
  dateRange2: ''
})

const open = (row) => {
   form.value.id = row.HETONEID?.id || null
  form.value.orgId = row?.id || null
  currentItem.value = row
  if (row.HETONEID?.id) {
    form.value.projectName = row.HETONEID?.projectName || ''
    form.value.contractName = row.HETONEID?.contractName || ''
    form.value.contractNumber = row.HETONEID?.contractNumber || ''
    form.value.remark = row.HETONEID?.remark || ''
    form.value.dateRange1 = row.HETONEID?.dateRange1 ? dayjs(row.HETONEID?.dateRange1).format('YYYY-MM-DD HH:mm:ss') : '',
      form.value.dateRange2 = row.HETONEID?.dateRange2 ? dayjs(row.HETONEID?.dateRange2).format('YYYY-MM-DD 23:59:59') : ''
    getDetaInfoList()
  }
        dialogRef.value?.open()
    nextTick(() => {
    leftTableRef1.value.tablePagination.grantDataIds = [row.id]
    leftTableRef2.value.tablePagination.grantDataIds = [row.id]
    leftTableRef2.value.tablePagination.powerSet = true
    setTimeout(() => {
      leftTableRef1.value.getTableData()
      leftTableRef2.value.getTableData()
    }, 50)
  })
}

// 获取合同授权服务列表
const getDetaInfoList = () => {
  $queryOrganizationServiceAuthList({ current: 1, size: 9999, grantDataIds: [form.value.orgId], contractIds: [form.value.id] }).then(res => {
    if (res.code == 0) {
      leftTable1SelectList.value = res.data.records || []
    }
  })
  $getCameraList({ current: 1, size: 9999, grantDataIds: [form.value.orgId], contractIds: [form.value.id], powerSet: true }).then(res => {
    if (res.code == 0) {
      leftTable2SelectList.value = res.data.records || []
    }
  })
}

const apiSourceList = ref([]);
getApiSourceList();
//获取来源列表
function getApiSourceList() {
  $_getSysApiSourceList().then(res => {
    apiSourceList.value = res.data;
  })
}


// 左边表格实例
const leftTableRef1 = ref(null)

// 类型枚举
const apiDataList = ref([]);
const getListDataEmnu = () => {
  $getOrganizationType('api_server_type').then((res) => {
    if (res.code == 0) {
      apiDataList.value = res.data;
    } else {
      apiDataList.value = [];
    }
  });
};
onMounted(() => {
  getListDataEmnu()
})


const resetTableEve1 = (type) => {
  if (type == 'left') {
    leftTableRef1.value.tablePagination.name = ''
    leftTableRef1.value.tablePagination.sourceCode = ''
    leftTableRef1.value.tablePagination.apiTypeId = ''
    leftTableRef1.value.getTableData()
  }
}

const leftTable1SelectList = ref([])

const handleSelectBox1 = (selection, row, allList) => {
  if (!row) {
    if (selection.length === 0) {
      leftTable1SelectList.value = leftTable1SelectList.value.filter(item =>
        !allList.find(listItem => listItem[Object.keys(listItem)[0]] === item[Object.keys(item)[0]])
      )
    } else {
      const newSelections = selection.filter(item =>
        !leftTable1SelectList.value.find(it => it[Object.keys(it)[0]] === item[Object.keys(item)[0]])
      )
      leftTable1SelectList.value = [...leftTable1SelectList.value, ...newSelections]
    }
  } else {
    if (selection.includes(row)) {
      if (!leftTable1SelectList.value.find(it => it[Object.keys(it)[0]] === row[Object.keys(row)[0]])) {
        leftTable1SelectList.value.push(row)
      }
    } else {
      const index = leftTable1SelectList.value.findIndex(it => it[Object.keys(it)[0]] === row[Object.keys(row)[0]])
      if (index !== -1) {
        leftTable1SelectList.value.splice(index, 1)
        leftTableRef1.value.clearRowSelection(row, false)
      }
    }
  }
}

const clearAllLeftTable1SelectList = () => {
  leftTable1SelectList.value = []
  leftTableRef1.value.clearSelectListEve()
}

const clearOneLeftTable1SelectList = (item) => {
  const index = leftTable1SelectList.value.findIndex(it => it[Object.keys(it)[0]] === item[Object.keys(item)[0]])
  if (index !== -1) {
    leftTable1SelectList.value.splice(index, 1)
    leftTableRef1.value.clearRowSelection(item, false)
  }
}

const leftTreeRef = ref(null)

const treeOptions = {
  label: "name",
  value: "keyFullId",
  children: "children",
}

// 左边表格实例
const leftTableRef2 = ref(null)



// // 获取组织机构
const allOrganization = ref([]); // 所有组织机构
const selectOrganization = ref([]); // 选择的组织机构
const getOrganization = () => {
  $getDeptPage(0, {}).then(res => {
    allOrganization.value = res.data
  })
}
getOrganization()

// 根据区域获取机构
const getOrganizationByArea = (areaId) => {
  $getDeptPage(0, { parentFullId: areaId, isCurLevel: false }).then(res => {
    selectOrganization.value = res.data
  })
}

// 左边树点击事件 (影响的是左边的树  会改变树的请求参数)
const handleNodeClick = (data) => {
  // 需要改变左边的树的请求参数
  leftTableRef2.value.tablePagination.curLevel = false
  leftTableRef2.value.tablePagination.current = 1
  leftTableRef2.value.tablePagination.areaTreeId = data.data.keyFullId
  getOrganizationByArea(data.data.keyFullId)
  leftTableRef2.value.getTableData()
}


const resetTableEve2 = (type) => {
  if (type == 'left') {
    leftTreeRef.value.setChangeNode()
    leftTableRef2.value.tablePagination.areaTreeId = leftTreeRef.value.treeData[0].keyFullId
    getOrganizationByArea(leftTreeRef.value.treeData[0].keyFullId)
    leftTableRef2.value.tablePagination.keyword = ''
    leftTableRef2.value.tablePagination.orgTreeId = ''
    leftTableRef2.value.getTableData()
  }
}

const leftTable2SelectList = ref([])

const handleSelectBox2 = (selection, row, allList) => {
  if (!row) {
    if (selection.length === 0) {
      leftTable2SelectList.value = leftTable2SelectList.value.filter(item =>
        !allList.find(listItem => listItem[Object.keys(listItem)[0]] === item[Object.keys(item)[0]])
      )
    } else {
      const newSelections = selection.filter(item =>
        !leftTable2SelectList.value.find(it => it[Object.keys(it)[0]] === item[Object.keys(item)[0]])
      )
      leftTable2SelectList.value = [...leftTable2SelectList.value, ...newSelections]
    }
  } else {
    if (selection.includes(row)) {
      if (!leftTable2SelectList.value.find(it => it[Object.keys(it)[0]] === row[Object.keys(row)[0]])) {
        leftTable2SelectList.value.push(row)
      }
    } else {
      const index = leftTable2SelectList.value.findIndex(it => it[Object.keys(it)[0]] === row[Object.keys(row)[0]])
      if (index !== -1) {
        leftTable2SelectList.value.splice(index, 1)
        leftTableRef2.value.clearRowSelection(row, false)
      }
    }
  }
}

const clearAllLeftTable2SelectList = () => {
  leftTable2SelectList.value = []
  leftTableRef2.value.clearSelectListEve()
}

const clearOneLeftTable2SelectList = (item) => {
  const index = leftTable2SelectList.value.findIndex(it => it[Object.keys(it)[0]] === item[Object.keys(item)[0]])
  if (index !== -1) {
    leftTable2SelectList.value.splice(index, 1)
    leftTableRef2.value.clearRowSelection(item, false)
  }
}

const handleConfirm = () => {
  const obj = {
    ...form.value,
    dataList: [
      {
        dataType: 1,
        relateData: leftTable2SelectList.value.map(item => {
          return {
            id: item.id,
            num: 1
          }
        })
      },
      {
        dataType: 2,
        relateData: leftTable1SelectList.value.map(item => {
          return {
            id: item.id,
            num: 1
          }
        })
      }
    ]
  }
  if (!form.value.id) {
    delete form.value.id
    $createOrderContract(obj).then(res => {
      if (res.code === 0) {
        success('新增授权成功')
        dialogRef.value?.close()
      } else {
        warning(res.msg || '新增授权失败')
      }
    })
  } else {
    $updateOrderContract(obj).then(res => {
      if (res.code === 0) {
        success('更新授权成功')
        dialogRef.value?.close()
      } else {
        warning(res.msg || '更新授权失败')
      }
    })
  }
}

const $emit = defineEmits(['closeDialog'])

const handleCancel = () => {
  form.value = {
    id: null,
    orgId: null,
    projectName: '',
    contractName: '',
    startAt: '',
    dateRange1: '',
    dateRange2: ''
  }
  currentItem.value = null
  leftTable1SelectList.value = []
  leftTable2SelectList.value = []
  leftTableRef1.value.clearSelectListEve()
  leftTableRef2.value.clearSelectListEve()
  $emit('closeDialog')
}


defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  row-gap: 20px;

  .content-header {
    height: 66px;
    display: flex;
    column-gap: 20px;
  }

  .content-body {}
}
</style>

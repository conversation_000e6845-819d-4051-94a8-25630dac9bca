<!-- 角色新增 编辑 -->
<template>
  <dialog-view ref="dialogViewRef" width="450px" :title="title ? '编辑角色' : '新增角色'" @confirm="confirmEve">
    <el-form :model="form" label-width="auto" ref="formRef" :rules="rules" style="max-width: 600px">
      <!-- <el-form-item label="数据权限类型" prop="dsType">
        <select-by-dict v-model="form.dsType" placeholder="请选择" :reqFn="() => $getDictType(`sys_user_role_range`)"
          :multiple="false" :defaultOptions="{ key: 'value', value: 'value', label: 'label' }" />
      </el-form-item> -->
      <el-form-item label="所属组织" prop="orgId">
        <el-tree-select v-model="form.orgId" :disabled="title" :data="roleTree"
          placeholder="请选择" filterable style="width: 284px;" :render-after-expand="false" check-strictly :props="{
            children: 'children',
            label: 'name',
            value: 'keyId'
          }" />
      </el-form-item>
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="form.roleName" />
      </el-form-item>
      <!-- <el-form-item label="权限类型" prop="dsType">
        <el-input v-model="form.dsType" />
      </el-form-item> -->
      <el-form-item label="角色描述" prop="roleDesc">
        <el-input type="textarea" rows="2" v-model="form.roleDesc" />
      </el-form-item>
      <el-form-item label="角色状态" prop="roleDesc">
        <el-switch v-model="form.status" active-value="0" inactive-value="1" />
      </el-form-item>
    </el-form>
  </dialog-view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DialogView from '@/components/DialogView/index.vue'
import { $_addRole, $_editRole } from '@/api/memberManagement'
import SelectByDict from '@/components/SelectByDict/index.vue'
import { cloneDeep } from 'lodash-es'
import { $getDictType } from '@/api/dict'
import { success } from '@/utils/toast.ts'
import { $getOrganizationPageTree } from '@/api/user-organization'

const $emit = defineEmits(['confirm'])

// 弹窗实例
const dialogViewRef = ref<any>(null)

const title = ref<boolean>(false)

// 表单实例
const formRef = ref<any>(null)

// 表单数据
const form = ref<any>({})

// 验证规则
const rules = ref<any>({
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  // dsType: [
  //   { required: true, message: '请选择数据权限类型', trigger: 'blur' }
  // ]
})


const roleTree = ref<any>([])

getTreeData()
function getTreeData() {
  let path = 0;
  let option = {
    isCurLevel: true,
    includeEnterprise: true
  }
  $getOrganizationPageTree(path, option).then(res => {
    roleTree.value = res.data
  })
}

// 表单确定事件
const confirmEve = () => {
  // 验证表单
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      dialogViewRef.value.loading = true
      form.value.dsType = 0
      // 编辑
      if (title.value) {
        editOrAdd()
        // 新增
      } else {
        add()
      }
    }
  })
}

// 新增事件
const add = () => {
  $_addRole(form.value)
    .then((res: any) => {
      if (res.code == 0) {
        success('新增成功！')
        dialogViewRef.value.close()
        $emit('confirm')
      }
    })
    .finally(() => {
      dialogViewRef.value.loading = false
    })
}

// 编辑事件
const editOrAdd = () => {
  $_editRole(form.value).then((res: any) => {
    if (res.code == 0) {
      success('编辑成功！')
      dialogViewRef.value.close()
      $emit('confirm')
    }
  }).finally(() => {
    dialogViewRef.value.loading = false
  })
}


/**
 * 打开弹窗
 * @param data 部门数据
 */
const open = (data: any) => {
  console.log("🚀 ~ open ~ data:", data)
  form.value = {}
  // 编辑
  if (!!data) {
    title.value = true
    form.value = cloneDeep(data)
    form.value.roleId = data.roleId
    // 新增
  } else {
    title.value = false
  }
  dialogViewRef.value.open()
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

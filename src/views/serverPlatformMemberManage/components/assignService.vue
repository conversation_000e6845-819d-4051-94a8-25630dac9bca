<!-- 分配服务 -->
<template>
  <dialog-view title="分配服务" ref="assignVideosDialog" width="80%" :needOkBtn="false" cancelBtnText="关闭"  @cancel="handleCancel">
    <!-- 穿梭框 -->
    <tab-trans-view @moveEvent="handleMoveEvent" ref="tabTransRef">
      <template #left-table>
        <table-box ref="leftTableRef" :apiFn="$_getApiListByRole">
          <!-- 搜索框 todo -->
          <template #search="{ tableObj: { tablePagination, clearEve, resetTable, getTableData } }">
            <el-form-item class="m-[0px] mr-[34px] items-center">
              <el-input v-model="tablePagination.name" clearable @change="getTableData(tablePagination)"
                style="width: 116px;" placeholder="名称或地址" />
            </el-form-item>
            <el-form-item label="来源"  class="m-[0px] mr-[34px] items-center">
              <el-select v-model="tablePagination.sourceCode" placeholder="请选择" style="width: 116px" filterable  @change="getTableData(tablePagination)"
                clearable>
                <el-option v-for="item in apiSourceList" :key="item.sourceCode" :label="item.sourceName"
                  :value="item.sourceCode" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型" class="m-[0px] mr-[34px] items-center">
              <el-select v-model="tablePagination.apiTypeId" placeholder="请选择" style="width: 116px" filterable clearable  @change="getTableData(tablePagination)">
                <el-option v-for="item in apiDataList" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <div class="h-full df aic">
              <!-- <el-button type="primary" style="width: 84px" @click="getTableData(tablePagination)">查询</el-button> -->
              <el-button @click="resetTableEve('left')" style="width: 84px" plain>重置</el-button>
            </div>
          </template>
          <!-- 表格列 -->
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column property="apiServerName" label="服务名称"/>
            <el-table-column property="sourceName" label="来源"/>
            <el-table-column prop="apiTypeId" label="类型">
              <template #default="{ row }">
                {{ apiDataList.filter(item => item.value == row.apiTypeId)[0]?.label }}
                <!-- <apiTypeName :value="row.apiTypeId" /> -->
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" width="200">
              <template #default="{ row }">
                <div class="w-180px truncate cursor-pointer" :title="row.description">
                  {{ row.description }}
                </div>
                <!-- <TooltipView :content="row.description">
                  <div class="multiLineText">{{ row.description }}</div>
                </TooltipView> -->
              </template>
            </el-table-column>
            <el-table-column prop="isListed" label="上架状态">
              <template #default="{ row }">
                <span v-if="row.isListed == 0" style="color: #ff604f">未上架</span>
                <span v-else-if="row.isListed == 1" style="color: #1a9bec">已上架</span>
                <span v-else-if="row.isListed == 2" style="">编制中</span>
                <span v-else>{{ row.isListed }}</span>
              </template>
            </el-table-column>
        </table-box>
      </template>
      <template #right-table>
        <table-box ref="rightTableRef" tableTitle="已分配" :apiFn="$_getApiListByRole">
          <template #search="{ tableObj: { tablePagination, clearEve, resetTable, getTableData } }">
            <el-form-item class="m-[0px] mr-[34px] items-center">
              <el-input v-model="tablePagination.name" clearable @change="getTableData(tablePagination)"
                style="width: 116px;" placeholder="名称或地址" />
            </el-form-item>
            <el-form-item label="来源"  class="m-[0px] mr-[34px] items-center">
              <el-select v-model="tablePagination.sourceCode" placeholder="请选择" style="width: 116px" filterable @change="getTableData(tablePagination)"
                clearable>
                <el-option v-for="item in apiSourceList" :key="item.sourceCode" :label="item.sourceName"
                  :value="item.sourceCode" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型" class="m-[0px] mr-[34px] items-center">
              <el-select v-model="tablePagination.apiTypeId" placeholder="请选择" style="width: 116px" filterable clearable @change="getTableData(tablePagination)">
                <el-option v-for="item in apiDataList" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <div class="h-full df aic">
              <!-- <el-button type="primary" style="width: 84px" @click="getTableData(tablePagination)">查询</el-button> -->
              <el-button @click="resetTableEve('right')" style="width: 84px" plain>重置</el-button>
            </div>
          </template>
          <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column property="apiServerName" label="服务名称"/>
            <el-table-column property="sourceName" label="来源"/>
            <el-table-column prop="apiTypeId" label="类型">
              <template #default="{ row }">
                {{ apiDataList.filter(item => item.value == row.apiTypeId)[0]?.label }}
                <!-- <apiTypeName :value="row.apiTypeId" /> -->
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" width="200">
              <template #default="{ row }">
                <div class="w-180px truncate cursor-pointer" :title="row.description">
                  {{ row.description }}
                </div>
                <!-- <TooltipView :content="row.description">
                  <div class="multiLineText">{{ row.description }}</div>
                </TooltipView> -->
              </template>
            </el-table-column>
            <el-table-column prop="isListed" label="上架状态">
              <template #default="{ row }">
                <span v-if="row.isListed == 0" style="color: #ff604f">未上架</span>
                <span v-else-if="row.isListed == 1" style="color: #1a9bec">已上架</span>
                <span v-else-if="row.isListed == 2" style="">编制中</span>
                <span v-else>{{ row.isListed }}</span>
              </template>
            </el-table-column>
        </table-box>
      </template>
    </tab-trans-view>
  </dialog-view>
</template>

<script setup>
import DialogView from '@/components/DialogView/index.vue'
import TabTransView from '@/components/tabTransView/index.vue'
import tableBox from '@/components/tabTransView/table-box.vue'
// import { $_getSysApiList } from "@/api/sys-api-type/index";
import { $_addApiSourcePermission, $_getApiList, $_getApiListByRole } from '@/api/auth'
import { $_getSysApiSourceList, $getOrganizationType } from "@/api/memberManagement";
import { success, warning } from "@/utils/toast";
import { nextTick, onMounted } from 'vue';

const props = defineProps({
  powerUserType: {
    type: Number,
    default: '0' // 权限存储类型，0-用户，1-角色
  },
  refresh: {
    type: Function,
    required: true
  }
})

const getdeptId = () => {
  const userBaseInfo = localStorage.getItem('userBaseInfo')
  if(userBaseInfo){
    const id = JSON.parse(userBaseInfo).userBaseInfo.deptId
    return id || '0'
  }
  return '0'
}

const apiSourceList = ref([]);
getApiSourceList();
//获取来源列表
function getApiSourceList() {
  $_getSysApiSourceList().then(res => {
    apiSourceList.value = res.data;
  })
}

const tabTransRef = ref(null)

const assignVideosDialog = ref(null)

// 左边表格实例
const leftTableRef = ref(null)

// 右边表格实例
const rightTableRef = ref(null)

const userId = ref(null)

const roleId = ref(null)

const getRoleList = () => {
  return JSON.parse(localStorage.getItem('userBaseInfo')).userBaseInfo.roleList.map(it => it.roleId)
}

const open = (row) => {
  userId.value = JSON.parse(localStorage.getItem('userBaseInfo')).userBaseInfo.roleList.map(it => it.roleId)
  roleId.value = row.roleId
  assignVideosDialog.value.open()
  nextTick(() => {
    leftTableRef.value.tablePagination.exclude = false
    leftTableRef.value.tablePagination.roleId = roleId.value
    if(getdeptId() === '0') {
      leftTableRef.value.tablePagination.userType = '1'
      leftTableRef.value.tablePagination.userId = userId.value
    }else{
      leftTableRef.value.tablePagination.userType = '1'
      leftTableRef.value.tablePagination.userId = getRoleList()
    }


    rightTableRef.value.tablePagination.exclude = false
    rightTableRef.value.tablePagination.userId = [roleId.value]
    rightTableRef.value.tablePagination.userType = '1'
    leftTableRef.value.getTableData()
    rightTableRef.value.getTableData()
  })
}

// 类型枚举
const apiDataList = ref([]);
const getListDataEmnu = () => {
  // let option = { current: 1, size: 1000 };
  $getOrganizationType('api_server_type').then((res) => {
    if (res.code == 0) {
      apiDataList.value = res.data;
    } else {
      apiDataList.value = [];
    }
  });
};


// 穿梭框切换事件
const handleMoveEvent = (data) => {
  console.log('穿梭框切换事件', data)
  if(data == 'right') { // 移动到右边表格
    if(leftTableRef.value.selectList.length == 0) {
      warning('请先选择服务')
      return
    }
    // 调用接口
    tabTransRef.value.rigthLoading = true
    handleRightMove()
  } else { // 移动到左边表格
    if(rightTableRef.value.selectList.length == 0) {
      warning('请先选择服务')
      return
    }
    // 调用接口
    tabTransRef.value.leftLoading = true
    handelLeftMove()
  }
}

// 数据右移事件
const handleRightMove = () => {
  const reqObj = {
    userId: roleId.value,
    powerIds: leftTableRef.value.selectList.map(item => item.id),
    powerUserType: '1',
    isDelete: false
  }
  $_addApiSourcePermission(reqObj).then((res) => {
    if (res.code == 0) {
      success('分配成功')
      leftTableRef.value.clearSelectListEve()
      rightTableRef.value.getTableData()
      leftTableRef.value.getTableData()
    }
  }).finally(() => {
    tabTransRef.value.rigthLoading = false
  })
}

const handelLeftMove = () => {
  const reqObj = {
    userId: roleId.value,
    powerIds: rightTableRef.value.selectList.map(item => item.id),
    powerUserType: props.powerUserType,
    isDelete: true
  }
  $_addApiSourcePermission(reqObj).then((res) => {
    if (res.code == 0) {
      success('取消分配成功')
      rightTableRef.value.clearSelectListEve()
      rightTableRef.value.getTableData()
      leftTableRef.value.getTableData()
    }
  }).finally(() => {
    tabTransRef.value.leftLoading = false
  })
}

const resetTableEve = (type) => {
  if(type == 'left') {
    leftTableRef.value.tablePagination.name = ''
    leftTableRef.value.tablePagination.sourceCode = ''
    leftTableRef.value.tablePagination.apiTypeId = ''
    leftTableRef.value.getTableData()
  } else {
    rightTableRef.value.tablePagination.name = ''
    rightTableRef.value.tablePagination.sourceCode = ''
    rightTableRef.value.tablePagination.apiTypeId = ''
    rightTableRef.value.getTableData()
  }
}

const handleCancel = () => {
  props.refresh()
}


onMounted(() => {
  getListDataEmnu()
})

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
</style>

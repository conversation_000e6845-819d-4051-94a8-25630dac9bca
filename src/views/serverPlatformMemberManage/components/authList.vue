<template>
  <dialog-view :title="currentItem?.name + ' -- ' + '权限清单'" class="bxx" ref="dialogRef" width="70%"
    :needOkBtn="false" cancelBtnText="关闭">
    <el-tabs type="border-card" v-model="activeTab" @tab-change="tabChanged">
      <el-tab-pane label="服务" name="2"></el-tab-pane>
      <el-tab-pane label="视频" name="1"></el-tab-pane>
      <el-tab-pane label="物联" name="3"></el-tab-pane>
      <el-tab-pane label="数据" name="4"></el-tab-pane>
      <div class="auth-table">
        <div class="table-header">
          <div class="table-cell header-cell name-col">名称</div>
          <div class="table-cell header-cell time-col">授权时间</div>
          <div class="table-cell header-cell contract-col">所属合同</div>
        </div>
        <div class="table-body">
          <template v-if="tableData && tableData.length > 0">
            <div v-for="(item, index) in tableData" :key="item.dataId" class="table-item">
              <div class="table-row name-row" :style="{ gridRow: `span ${item.contracts.length}` }">
                <div class="table-cell name-col center-cell">
                  <span>{{ item.dataName || '未命名' }}</span>
                </div>
              </div>
              <div v-for="(contract, contractIndex) in item.contracts" 
                  :key="contract.contractId" 
                  class="table-row content-row">
                <div class="table-cell time-col">
                  {{ formatDate(contract.dateRange1) }} 至 {{ formatDate(contract.dateRange2) }}
                </div>
                <div class="table-cell contract-col">
                  {{ contract.contractName || '未命名' }}
                </div>
              </div>
            </div>
          </template>
          <div v-else class="empty-data">
            <el-empty description="暂无数据" />
          </div>
        </div>
      </div>
    </el-tabs>
  </dialog-view>
</template>

<script setup>
import DialogView from '@/components/DialogView/index.vue'
import {  ref } from 'vue'
import {$queryAuthTimeList} from '@/api/organizationAuth'

const currentItem = ref(null)
const dialogRef = ref(null)

const activeTab = ref('2')
const tableData = ref([])

const tabChanged = ()=>{
  getAuthList()
}

import dayjs from 'dayjs'

const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

// 获取权限清单数据
const getAuthList = async () => {
  try {
    const res = await $queryAuthTimeList({ orgId: currentItem.value.id, dataType: activeTab.value });
    if (res?.data) {
      tableData.value = res.data.filter(item => item.contracts && item.contracts.length > 0)
    }
  } catch (error) {
    console.error('获取权限清单失败:', error)
    tableData.value = []
  }
}

const open = (row) => {
  currentItem.value = row
   getAuthList()
  dialogRef.value?.open()
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.auth-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.table-header {
  display: grid;
  grid-template-columns: 200px 250px 1fr;
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-item {
  position: relative;
  display: grid;
  border-bottom: 1px solid #EBEEF5;

  &:last-child {
    border-bottom: none;
  }

  &:nth-child(even) {
    background-color: #FAFAFA;
  }
}

.table-row {
  display: grid;

  &.name-row {
    position: absolute;
    width: 200px;
    height: 100%;
    grid-template-columns: 200px;
  }

  &.content-row {
    margin-left: 200px;
    grid-template-columns: 250px 1fr;
    border-left: 1px solid #EBEEF5;
  }
}

.table-cell {
  padding: 12px;
  border-right: 1px solid #EBEEF5;
  display: flex;
  align-items: center;
  min-height: 48px; // 确保最小高度一致
  box-sizing: border-box;

  &:last-child {
    border-right: none;
  }

  &:empty::after {
    content: '\\00a0'; // 使用不换行空格保持高度
  }
}

.header-cell {
  font-weight: bold;
  justify-content: center;
}

.name-col {
  min-width: 100px; // 确保最小宽度

  &.center-cell {
    justify-content: center; // 水平居中
    align-items: center; // 垂直居中
    text-align: center;
    
    span {
      display: inline-block;
      max-width: 90%; // 防止文字太长
      word-break: break-word; // 允许在任意字符间换行
    }
  }
}

.time-col {
  min-width: 250px; // 确保日期显示完整
  justify-content: center; // 时间列也居中显示
}

.contract-col {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  padding-left: 20px; // 合同名称左对齐时留出一定间距
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
}
</style>
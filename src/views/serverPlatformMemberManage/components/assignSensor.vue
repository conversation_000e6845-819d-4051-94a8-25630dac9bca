<!-- 分配传感器 -->
<template>
  <dialog-view title="分配传感器" ref="assignVideosDialog" width="80%" cancelBtnText="关闭" :needOkBtn="false" @cancel="handleCancel">
    <!-- 穿梭框 -->
    <tab-trans-view @moveEvent="handleMoveEvent" ref="tabTransRef">
      <template #tree>
        <leftTreeList type="1" class="h-full" @nodeClick="handleNodeClick"
          ref="leftTreeRef" :showType="false" :showSwitches="false" :showSearch="false"/>
      </template>
      <template #left-table>
        <table-box ref="leftTableRef" :apiFn="$getVideoChannelListByid">
          <!-- 搜索框 todo -->
          <template #search="{ tableObj: { tablePagination, clearEve, resetTable, getTableData } }">
            <el-form-item class="m-[0px] mr-[34px] items-center">
              <el-input v-model="tablePagination.keyword" clearable @clear="handleClear('keyword')"
                style="width: 116px;" placeholder="名称或IP地址" />
            </el-form-item>
            <el-form-item
              label="所属组织"
              class="m-[0px] mr-[34px] items-center"
            >
              <el-tree-select
                v-model="tablePagination.orgTreeId"
                :data="selectOrganization"
                filterable
                :loading="selectLoading"
                :render-after-expand="false"
                check-strictly
                :props="treeOptions"
                :check-strictly="checkStrictly"
                no-data-text="请先选择所属区域"
                style="width: 216px;"

              />
            </el-form-item>
            <div class="h-full df aic">
              <el-button type="primary" style="width: 84px" @click="getTableData(tablePagination)">查询</el-button>
              <el-button @click="resetTableEve('left')" style="width: 84px" plain>重置</el-button>
            </div>
          </template>
          <!-- 表格列 -->
            <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
            <el-table-column property="name" label="名称" width="250" fixed="left">
              <template #="{ row }">
                <div style="text-align: left; ">
                  {{ row.name }}
                </div>
              </template>
            </el-table-column>
            <el-table-column property="nodeName" label="所属节点" width="120" />
            <el-table-column property="nodeName" label="类型" width="120" />
            <el-table-column property="belongName" label="所属组织" width="200" />
            <el-table-column property="belongName" label="IP地址" width="200" />
            <el-table-column property="belongName" label="位点地址" width="200" />
            <!-- <el-table-column prop="phone" label="管理员联系电话" width="180"></el-table-column>
            <el-table-column prop="email" label="管理员电子邮箱" width="180"></el-table-column>
            <el-table-column prop="orgName" label="所属组织" width="180"></el-table-column> -->
        </table-box>
      </template>
      <template #right-table>
        <table-box ref="rightTableRef" tableTitle="已分配" :apiFn="$getVideoChannelListByid">
          <!-- 搜索框 -->
          <template #search="{ tableObj: { tablePagination, clearEve, resetTable, getTableData } }">
            <el-form-item class="m-[0px] mr-[34px] items-center">
              <el-input v-model="tablePagination.keyword" clearable @clear="handleClear('keyword')"
                style="width: 116px;" placeholder="名称或IP地址" />
            </el-form-item>
            <el-form-item
              label="所属组织"
              class="m-[0px] mr-[34px] items-center"
            >
              <el-tree-select
                v-model="tablePagination.orgTreeId"
                :data="allOrganization"
                filterable
                :loading="selectLoading"
                :render-after-expand="false"
                check-strictly
                :props="treeOptions"
                :check-strictly="checkStrictly"
                no-data-text="请先选择所属区域"
                style="width: 216px;"

              />
            </el-form-item>
            <div class="h-full df aic">
              <el-button type="primary" style="width: 84px" @click="getTableData(tablePagination)">查询</el-button>
              <el-button @click="resetTableEve('right')" style="width: 84px" plain>重置</el-button>
            </div>
          </template>
          <!-- 表格列 -->
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column property="name" label="名称" width="250" fixed="left">
            <template #="{ row }">
              <div style="text-align: left; ">
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column property="nodeName" label="所属节点" width="120" />
          <el-table-column property="nodeName" label="类型" width="120" />
          <el-table-column property="belongName" label="所属组织" width="200" />
          <el-table-column property="belongName" label="IP地址" width="200" />
          <el-table-column property="belongName" label="位点地址" width="200" />
        </table-box>
      </template>
    </tab-trans-view>
  </dialog-view>
</template>

<script setup>
import tableBox from '@/components/tabTransView/table-box.vue';
import { $getVideoChannelListByid, $addUserAuthority } from '@/api/videoCenter';
import LeftTreeList from "@/components/LeftTreeLIst/index.vue";
import {  warning } from "@/utils/toast";

const props = defineProps({
  powerUserType: {
    type: Number,
    default: 0, // 0 用户 1 角色
  },
  refresh: {
    type: Function,
    required: true
  }
})

const leftTreeRef = ref(null)

const treeOptions = {
  label: "name",
  value: "keyFullId",
  children: "children",
}

const assignVideosDialog = ref(null)

// 穿梭框实例
const tabTransRef = ref(null)

// 左边表格实例
const leftTableRef = ref(null)

// 右边表格实例
const rightTableRef = ref(null)

const userId = ref(null)

const open = (row) => {
  console.log('打开分配视频弹窗', row)
  userId.value = row.userId
  assignVideosDialog.value.open()
  // 表格需要 userId 参数 左右表格都需要
  // leftTableRef.value.tablePagination.userId = row.id
  // leftTableRef.value.tablePagination.powerSet = false
  // rightTableRef.value.tablePagination.powerSet = true
  // rightTableRef.value.tablePagination.userId = row.id
}

// 获取组织机构
const allOrganization = ref([]); // 所有组织机构
const selectOrganization = ref([]); // 选择的组织机构
const getOrganization = () => {
  $getDeptPage(0, {}).then(res => {
    allOrganization.value = res.data
  })
}
getOrganization()

// 根据区域获取机构
const getOrganizationByArea = (areaId) => {
  $getDeptPage(0, {parentFullId: areaId, isCurLevel: false}).then(res => {
    selectOrganization.value = res.data
  })
}


// 左边树点击事件 (影响的是左边的树  会改变树的请求参数)
const handleNodeClick = (data) => {
  if(!leftTableRef.value.tablePagination.userId) {
    leftTableRef.value.tablePagination.userId = userId.value
    rightTableRef.value.tablePagination.userId = userId.value
    rightTableRef.value.tablePagination.powerUserType = props.powerUserType
    leftTableRef.value.tablePagination.powerUserType = props.powerUserType
    leftTableRef.value.tablePagination.powerSet = false
    rightTableRef.value.tablePagination.powerSet = true
  }
  // 需要改变左边的树的请求参数
  leftTableRef.value.tablePagination.curLevel = false
  leftTableRef.value.tablePagination.current = 1
  leftTableRef.value.tablePagination.areaTreeId = data.data.keyFullId
  rightTableRef.value.tablePagination.curLevel = false
  getOrganizationByArea(data.data.keyFullId)
  leftTableRef.value.getTableData()
  rightTableRef.value.getTableData()
}

// 穿梭框切换事件
const handleMoveEvent = (data) => {
  if(data == 'right') { // 移动到右边表格
    if(leftTableRef.value.selectList.length == 0) {
      warning('请先选择左边的视频')
      return
    }
    handleMoveToRight(leftTableRef.value.selectList)
  } else { // 移动到左边表格
    if(rightTableRef.value.selectList.length == 0) {
      warning('请先选择右边的视频')
      return
    }
    handleMoveToLeft(rightTableRef.value.selectList)
  }
}

// 移动到右边
const handleMoveToRight = (data) => {
  tabTransRef.value.rigthLoading = true
  const reqObj = {
    userId: userId.value,
    powerIds: data.map(item => item.id),
    powerUserType: props.powerUserType,
    isDelete: false
  }
  $addUserAuthority(reqObj).then(res => {
    if(res.code == 0) {
      leftTableRef.value.clearSelectListEve()
      leftTableRef.value.getTableData()
      rightTableRef.value.getTableData()
    }
  }).finally(() => {
    tabTransRef.value.rigthLoading = false
  })
}

// 移动到左边
const handleMoveToLeft = (data) => {
  tabTransRef.value.leftLoading = true
  const reqObj = {
    userId: userId.value,
    powerIds: data.map(item => item.id),
    powerUserType: props.powerUserType,
    isDelete: true
  }
  $addUserAuthority(reqObj).then(res => {
    if(res.code == 0) {
      rightTableRef.value.clearSelectListEve()
      leftTableRef.value.getTableData()
      rightTableRef.value.getTableData()
    }
  }).finally(() => {
    tabTransRef.value.leftLoading = false
  })
}

const resetTableEve = (type) => {
  if(type == 'left') {
    leftTreeRef.value.setChangeNode()
    leftTableRef.value.tablePagination.areaTreeId = leftTreeRef.value.treeData[0].keyFullId
    getOrganizationByArea( leftTreeRef.value.treeData[0].keyFullId)
    leftTableRef.value.tablePagination.keyword = ''
    leftTableRef.value.tablePagination.orgTreeId = ''
    leftTableRef.value.getTableData()
  } else {
    rightTableRef.value.tablePagination.keyword = ''
    rightTableRef.value.tablePagination.orgTreeId = ''
    rightTableRef.value.getTableData()
  }
}

// 取消事件
const handleCancel = () => {
  props.refresh()
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
</style>

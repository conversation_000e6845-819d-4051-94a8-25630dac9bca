<template>
  <dialog-view :title="currentItem?.name + ' -- ' + '自有资源授权'" class="bxx" ref="dialogRef" width="50%" :needOkBtn="true"
    cancelBtnText="关闭" @confirm="handleConfirm">
    <div class="flex flex-col gap-y-[20px]">
      <div>自有视频授权</div>
      <div class="border border-solid p-[20px] h-[150px] flex flex-col justify-between">
        <div class="flex items-center gap-x-[10px]">
          <div>视频路数：</div>
          <div><el-input v-model="form.selfDevNum" clearable style="width: 150px" type="number" /></div>
          <div>路</div>
        </div>
        <div class="flex items-center gap-x-[10px]">
          <div>有效期：</div>
          <div><date-picker style="width: 400px;" v-model:startAt="form.dateRange1" v-model:endAt="form.dateRange2"
              :showStartAfterTime="false" :showEndAfterTime="false" /></div>
        </div>
      </div>
    </div>
  </dialog-view>
</template>

<script setup>
import DialogView from '@/components/DialogView/index.vue'
import { nextTick, ref } from 'vue'
import { $queryOrganizationDeviceCount, $updateOrganizationDevice } from '@/api/organizationAuth'
import { success, warning } from '@/utils/toast'
import dayjs from 'dayjs'

const currentItem = ref(null)
const dialogRef = ref(null)

// 重置表单
const resetForm = () => {
  form.value = {
    selfDevNum: null,
    dateRange1: '',
    dateRange2: ''
  }
}

const open = async(row) => {
  currentItem.value = row
  resetForm() // 打开前先重置表单
  await getOwnResourceAuth()
  dialogRef.value?.open()
}

// 获取自有资源授权信息
const getOwnResourceAuth = async () => {
  const res = await $queryOrganizationDeviceCount({ orgId: currentItem.value.id })
  if (res?.data) {
    form.value = {
      selfDevNum: res.data.selfDevNum || 0,
      dateRange1: res.data.dateRange1 ? dayjs(res.data.dateRange1).format('YYYY-MM-DD HH:mm:ss') : '',
      dateRange2: res.data.dateRange2 ? dayjs(res.data.dateRange2).format('YYYY-MM-DD 23:59:59') : ''
    }
  }
}

const form = ref({
  selfDevNum: null,
  dateRange1: "",
  dateRange2: ""
})

const handleConfirm = (val) => {
  if (!form.value.dateRange1 && form.value.dateRange2) {
    warning('请选择开始时间')
    return
  }
  if (form.value.dateRange1 && !form.value.dateRange2) {
    warning('请选择结束时间')
    return
  }

  $updateOrganizationDevice({ 
    ...form.value, 
    orgId: currentItem.value.id,
    dateRange1: form.value.dateRange1 ? dayjs(form.value.dateRange1).format('YYYY-MM-DD HH:mm:ss') : '',
    dateRange2: form.value.dateRange2 ? dayjs(form.value.dateRange2).format('YYYY-MM-DD 23:59:59') : ''
  }).then(res => {
    if (res.code === 0) {
      success('编辑成功')
      resetForm()
      dialogRef.value?.close()
    }
  })
}

defineExpose({
  open
})
</script>
<template>
  <div class="containerBox">
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border"
    >
      <div class="flex flex-row gap-x-[16px]">
        <el-input
          v-model="queryParams.executorName"
          style="width: 150px"
          @blur="searchEvd"
          placeholder="执行人"
          clearable
        >
        </el-input>
        <div class="flex flex-row gap-x-[8px] items-center">
          <div>逾期状态</div>
          <el-select
            v-model="queryParams.overdue"
            clearable
            placeholder="请选择"
            style="width: 150px"
            @change="searchEvd"
          >
            <el-option
              v-for="item in overdueList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="flex flex-row gap-x-[8px] items-center">
          <div>处理状态</div>
          <el-select
            v-model="queryParams.status"
            clearable
            placeholder="请选择"
            style="width: 150px"
            @change="searchEvd"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div>
          <!-- <el-button type="primary">查询</el-button> -->
          <el-button @click="resetValue">重置</el-button>
        </div>
      </div>
      <div class="f1">
        <ElementTable
          ref="table"
          :table-title="tableTitle"
          :page-config="pageConfig"
          :data="tableData"
        >
          <template #linkBtn="scope">
            <!-- <div>{{ scope.data.row }}</div> -->
            <div
              style="
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: center;
              "
            >
              <el-button
                style="margin: 10px"
                v-if="scope.data.row.status == 2"
                type="danger"
                @click="linkClick('处置上报', scope.data.row)"
                >处置上报</el-button
              >
              <el-button
                style="margin: 10px"
                type="warning"
                v-if="scope.data.row.status == 1 || scope.data.row.status === 3"
                @click="linkClick('接单', scope.data.row)"
                >接单</el-button
              >
              <el-button
                style="margin: 10px"
                type="primary"
                v-if="scope.data.row.status == 1"
                @click="linkClick('转单', scope.data.row)"
                >转单</el-button
              >
              <el-button
                style="margin: 10px"
                type="primary"
                @click="linkClick('查看详情', scope.data.row)"
                >查看详情</el-button
              >
            </div>
          </template>
          <template #overdue="scope">
            <div>{{ scope.data.row.overdue ? '已逾期' : '未逾期' }}</div>
          </template></ElementTable
        >
      </div>
    </div>
    <!-- 处置上报及转单部分 -->
    <reminderAndReview
      ref="dialogRemAndReviref"
      :typeBoolean="typeBoolean"
      :executorList="executorList"
      @remindConfigBtn="remindConfigBtn"
    ></reminderAndReview>
    <!-- 工单追踪部分 -->
    <dialog-view
      ref="dialogTrackOrderRef"
      width="800px"
      class="flex"
      title="工单追踪"
      :needOkBtn="false"
    >
      <ElementTable
        ref="tableTrack"
        :table-title="tableTrackTitle"
        :data="tableTrackData"
      >
        <template #approved="scope">
          <div
            :style="scope.data.row.approved ? 'color:#95f202' : 'color:#d8001b'"
          >
            {{ scope.data.row.approved ? '验收完成' : '重新处理' }}
          </div>
        </template>
        <template #optType="scope">
          <div>
            {{ scope.data.row.optTypeMark}}
          </div>
        </template>
      </ElementTable>
    </dialog-view>
  </div>
</template>

<script setup lang="ts">
import { PageConfigType } from '@/components/ElementTable/index.vue'
import {
  $getMyOrderList,
  $getOrderDetail,
  $dealOrder,
  $transferOrder,
  $acceptOrder,
  $getOrderLog
} from '@/api/order/index.ts'
import DialogView from '@/components/DialogView/index.vue'
import { success } from '@/utils/toast.ts'
import { $_getMemberList } from '@/api/memberManagement'
import { ref, onMounted } from 'vue'
import reminderAndReview from './components/reminderAndReview.vue'

onMounted(() => {
  getTableData()
})

const getTableData = (parmasValue: any = queryParams.value) => {
  $getMyOrderList(parmasValue).then((res: any) => {
    console.log('我的工单', res)
    if (res.code == 0) {
      tableData.value = res.data.records
      tableData.value.forEach((item: any) => {})
      pageConfig.total = res.data.total
    }
  })
}
//查询事件
const searchEvd = () => {
  queryParams.value.current = 1
  getTableData()
}
//重置时间
const resetValue = () => {
  queryParams.value = {
    current: 1,
    size: 10
  }
  getTableData()
}
/**
 * 催单及审核弹窗部分
 */
const typeBoolean = ref<String>('处置上报')
const dialogRemAndReviref = ref<any>(null)
const linkClick = async (val: string, row: any) => {
  let obj = await getOrderInfo(row.id)
  switch (val) {
    case '处置上报':
      typeBoolean.value = '处置上报'
      dialogRemAndReviref.value.open(obj)
      break
    case '转单':
      await queryMemberList()
      typeBoolean.value = '转单'
      dialogRemAndReviref.value.open(obj)
      break
    case '接单':
      $acceptOrder(row.id).then((res: any) => {
        if (res.code == 0) {
          success('接单成功')
          getTableData()
        }
      })
      break
    case '查看详情':
      $getOrderLog(row.id).then((res: any) => {
        if (res.code == 0) {
          dialogTrackOrderRef.value.open()
          tableTrackData.value = res.data
        }
      })
      break
  }
}
// 催单及审核弹窗确认事件
const remindConfigBtn = (val: any, blean: String) => {
  dialogRemAndReviref.value.trueLoading()
  if (blean == '处置上报') {
    $dealOrder({
      orderId: val.id,
      remarks: val.remarks,
      filenames: val.upLodafilenames
    })
      .then((res: any) => {
        if (res.code == 0) {
          success('处置上报成功')
          dialogRemAndReviref.value.falseLoading()
          dialogRemAndReviref.value.close()
          getTableData()
        }
      })
      .catch(() => {
        dialogRemAndReviref.value.falseLoading()
      })
  } else if (blean == '转单') {
    let changeExecutorName = ''
    if (val.changeExecutorId) {
      executorList.value.forEach((item: any) => {
        if (item.userId == val.changeExecutorId) {
          changeExecutorName = item.name
        }
      })
    } else {
      changeExecutorName = ''
    }
    $transferOrder({
      orderId: val.id,
      remarks: val.remarks,
      executorId: val.changeExecutorId,
      executorName: changeExecutorName
    })
      .then((res: any) => {
        if (res.code == 0) {
          dialogRemAndReviref.value.falseLoading()
          dialogRemAndReviref.value.close()
          success('转单成功')
          getTableData()
        }
      })
      .catch(() => {
        dialogRemAndReviref.value.falseLoading()
      })
  }
}
// 获取工单详情
const getOrderInfo = async (id: string) => {
  let res: any = await $getOrderDetail(id)
  if (res.code == 0) {
    return res.data
  }
}
// 获取执行人列表
let executorList: any = ref([])
const queryMemberList = async () => {
  // 请求获取可选执行人
  let res: any = await $_getMemberList({ size: 999, current: 1 })
  if (res.code == 0) {
    executorList.value = res.data.records
  }
}
/**
 * 工单追踪部分
 */
const oyTypeCom = (val: any) => {
  const obj: any = {
    '1': '催单',
    '2': '审核',
    '3': '转单',
    '4': '审核',
    '5': '处理'
  }
  return obj[val]
}
const dialogTrackOrderRef = ref<any>(null)
const tableTrackData: any = ref([])
const tableTrackTitle: Array<Object | any> = [
  {
    label: '操作类型',
    name: 'optType',
    type: 'custom'
  },
  {
    label: '执行人',
    prop: 'executorName',
    type: 'text'
  },
  {
    label: '问题图片',
    prop: 'filenames',
    type: 'image',
    width: '180px'
  },
  {
    label: '备注',
    prop: 'remarks',
    type: 'text'
  },
  {
    label: '是否审核通过,',
    name: 'approved',
    type: 'custom'
  }
]
/**
 *表单部分
 */
let queryParams: any = ref({
  current: 1,
  size: 10
})
const tableData: any = ref([])
const tableTitle: Array<Object | any> = [
  {
    label: '工单编号',
    prop: 'workNo',
    type: 'text',
    width: '200px'
  },
  {
    label: '标题',
    prop: 'title',
    type: 'text'
  },
  {
    label: '工单来源',
    prop: 'sourceMark',
    type: 'text'
  },
  {
    label: '工单类型',
    prop: 'typeMark',
    type: 'text'
  },
  {
    label: '问题图片',
    prop: 'filenames',
    type: 'image',
    width: '180px'
  },
  {
    label: '任务内容',
    prop: 'content',
    type: 'text'
  },
  {
    label: '派单人',
    prop: 'creatorName',
    type: 'text'
  },
  {
    label: '创建时间',
    prop: 'finishTime',
    type: 'text'
  },
  {
    label: '要求完成时间',
    prop: 'finishTime',
    type: 'text'
  },
  {
    label: '执行人',
    prop: 'executorName',
    type: 'text'
  },
  {
    label: '逾期状态',
    name: 'overdue',
    type: 'custom'
  },
  {
    label: '处理状态',
    prop: 'statusMark',
    type: 'text'
  },
  {
    label: '操作',
    name: 'linkBtn',
    type: 'custom',
    width: '280px'
  }
]

const pageConfig: PageConfigType = {
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
}
const cuttentChange = (e: any) => {
  pageConfig.currentPage = e
  queryParams.value.current = e
  getTableData()
}

// 每页显示条数
const sizeChange = (e: any) => {
  pageConfig.pageSize = e
  queryParams.value.size = e

  getTableData()
}
const overdueList = [
  {
    value: false,
    label: '未逾期'
  },
  {
    value: true,
    label: '已逾期'
  }
]
const statusList = [
  {
    value: 1,
    label: '待接单'
  },
  {
    value: 2,
    label: '已接单'
  },
  {
    value: 3,
    label: '转单待接单'
  },
  {
    value: 4,
    label: '已完成'
  },
  {
    value: 5,
    label: '已处理待审核'
  }
]
</script>

<style scoped></style>

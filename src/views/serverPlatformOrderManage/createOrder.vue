<template>
  <div class="containerBox">
    <div
      class="w-full h-full bg-white flex flex-col gap-y-[16px] p-[16px] box-border"
    >
      <div class="flex flex-row gap-x-[16px]">
        <el-input
          v-model="queryParams.executorName"
          style="width: 150px"
          @blur="searchEvd"
          placeholder="执行人"
          clearable
        >
        </el-input>
        <div class="flex flex-row gap-x-[8px] items-center ml-[16px] mr-[16px]">
          <div>逾期状态</div>
          <el-select
            v-model="queryParams.overdue"
            clearable
            placeholder="请选择"
            style="width: 150px"
            @change="searchEvd"
          >
            <el-option
              v-for="item in overdueList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="flex flex-row gap-x-[8px] items-center mr-[16px]">
          <div>处理状态</div>
          <el-select
            v-model="queryParams.status"
            clearable
            placeholder="请选择"
            style="width: 150px"
            @change="searchEvd"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div>
          <!-- <el-button type="primary">查询</el-button> -->
          <el-button @click="resetValue">重置</el-button>
        </div>
      </div>
      <div class="mt-[16px] mb-[16px]">
        <el-button type="primary" @click="createOrder">创建工单</el-button>
        <el-button type="primary" @click="authorFn">申请平台授权</el-button>
      </div>
      <div class="f1">
        <ElementTable
          ref="table"
          :table-title="tableTitle"
          :page-config="pageConfig"
          :data="tableData"
        >
          <template #linkBtn="scope">
            <!-- <div>{{ scope.data.row }}</div> -->
            <div>
              <el-button
                type="danger"
                v-if="scope.data.row.status == 2"
                @click="linkClick('催单', scope.data.row)"
                >催单</el-button
              >
              <el-button
                type="warning"
                @click="linkClick('工单追踪', scope.data.row)"
                >工单追踪</el-button
              >
              <el-button
                type="primary"
                v-if="scope.data.row.status == 5"
                @click="linkClick('审核', scope.data.row)"
                >审核</el-button
              >
            </div>
          </template>
          <template #overdue="scope">
            <div>{{ scope.data.row.overdue ? '已逾期' : '未逾期' }}</div>
          </template>
        </ElementTable>
      </div>
    </div>
    <!-- 申请平台授权 -->
    <authorizeDia v-model:showDrawer="showDrawer"></authorizeDia>
    <!-- 创建工单弹窗 -->
    <dialog-view
      ref="dialogCreatOrderRef"
      width="800px"
      class="flex"
      title="创建工单"
      @confirm="confirmEve"
    >
      <el-form
        :model="formData"
        label-width="120px"
        ref="formRef"
        inline
        :rules="rules"
        style="max-width: 800px"
      >
        <div class="df">
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入工单标题"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="工单类型" prop="type">
            <SelectByDict
              v-model="formData.type"
              placeholder="请输入工单类型"
              style="width: 200px"
              :multiple="false"
              :defaultOptions="{ key: 'value', value: 'value', label: 'label' }"
              :reqFn="() => $getDictType(`work_order_type`)"
            >
            </SelectByDict>
          </el-form-item>
        </div>

        <el-form-item label="任务内容" prop="content">
          <el-input
            type="textarea"
            v-model="formData.content"
            :rows="5"
            placeholder="请输入任务内容"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="问题照片" prop="filenames">
          <uploadImg
            v-model:fileList="formData.filenames"
            :baseUrl="baseUrl"
          ></uploadImg>
        </el-form-item>
        <div class="df">
          <el-form-item label="执行人" prop="executorId">
            <el-select
              v-model="formData.executorId"
              placeholder="请选择执行人"
              size="large"
              style="width: 200px"
              @change="changeExecutor"
            >
              <el-option
                v-for="item in executorList"
                :key="item.userId"
                :label="item.name"
                :value="item.userId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="要求完成时间" prop="finishTime">
            <el-date-picker
              v-model="formData.finishTime"
              style="width: 200px"
              type="datetime"
              placeholder="请选择时间"
              format="YYYY/MM/DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </div>
      </el-form>
    </dialog-view>
    <!-- 催单及审核部分 -->
    <reminderAndReview
      ref="dialogRemAndReviref"
      :typeBoolean="typeBoolean"
      @remindConfigBtn="remindConfigBtn"
    ></reminderAndReview>
    <!-- 工单追踪部分 -->
    <dialog-view
      ref="dialogTrackOrderRef"
      width="800px"
      class="flex"
      title="工单追踪"
      :needOkBtn="false"
    >
      <ElementTable
        ref="tableTrack"
        :table-title="tableTrackTitle"
        :data="tableTrackData"
      >
        <template #approved="scope">
          <div
            :style="scope.data.row.approved ? 'color:#95f202' : 'color:#d8001b'"
          >
            {{ scope.data.row.approved ? '验收完成' : '重新处理' }}
          </div>
        </template>
        <template #optType="scope">
          <div>
            {{ scope.data.row.optTypeMark }}
          </div>
        </template>
      </ElementTable>
    </dialog-view>
  </div>
</template>

<script setup lang="ts">
import { PageConfigType } from '@/components/ElementTable/index.vue'
import { ref, onMounted } from 'vue'
import {
  $getOrderList,
  $createOrder,
  $getOrderDetail,
  $urgeOrder,
  $getOrderLog,
  $auditOrder,
  $getOrderLastData
} from '@/api/order/index.ts'
import DialogView from '@/components/DialogView/index.vue'
import uploadImg from './components/upLoadImg.vue'
import reminderAndReview from './components/reminderAndReview.vue'
import { $getDictType } from '@/api/dict'
import { success } from '@/utils/toast.ts'
import SelectByDict from '@/components/SelectByDict/index.vue'
import { $_getMemberList } from '@/api/memberManagement'
import authorizeDia from './components/authorizeDia.vue'
onMounted(() => {
  getTableData()
})
const baseUrl = import.meta.env.VITE_RESOURCE_URL
const value = ref('')
//查询事件
const searchEvd = () => {
  queryParams.value.current = 1
  getTableData()
}
//重置时间
const resetValue = () => {
  queryParams.value = {
    current: 1,
    size: 10
  }
  getTableData()
}
/**
 * 申请平台授权
 */
const showDrawer = ref<any>(false)
const authorFn = () => {
  showDrawer.value = true
}
/**
 * 工单追踪部分
 */
const oyTypeCom = (val: any) => {
  const obj: any = {
    '1': '催单',
    '2': '审核',
    '3': '转单',
    '4': '审核',
    '5': '处理'
  }
  return obj[val]
}
const dialogTrackOrderRef = ref<any>(null)
const tableTrackData: any = ref([])
const tableTrackTitle: Array<Object | any> = [
  {
    label: '操作类型',
    name: 'optType',
    type: 'custom'
  },
  {
    label: '执行人',
    prop: 'executorName',
    type: 'text'
  },
  {
    label: '问题图片',
    prop: 'filenames',
    type: 'image',
    width: '180px'
  },
  {
    label: '备注',
    prop: 'remarks',
    type: 'text'
  },
  {
    label: '是否审核通过,',
    name: 'approved',
    type: 'custom'
  }
]
/**
 * 催单及审核弹窗部分
 */
const typeBoolean = ref<String>('催单')
const dialogRemAndReviref = ref<any>(null)
const linkClick = async (val: string, row: any) => {
  let obj = await getOrderInfo(row.id)
  switch (val) {
    case '催单':
      typeBoolean.value = '催单'
      dialogRemAndReviref.value.open(obj)
      break
    case '审核':
      $getOrderLastData(row.id).then((res: any) => {
        if (res.code == 0) {
          obj.process = res.data
          typeBoolean.value = '审核'
          dialogRemAndReviref.value.open(obj)
        }
      })

      break
    case '工单追踪':
      $getOrderLog(row.id).then((res: any) => {
        if (res.code == 0) {
          dialogTrackOrderRef.value.open()
          tableTrackData.value = res.data
        }
      })

      break
  }
}
// 获取工单详情
const getOrderInfo = async (id: string) => {
  let res: any = await $getOrderDetail(id)
  if (res.code == 0) {
    return res.data
  }
}
// 催单及审核弹窗确认事件
const remindConfigBtn = (val: any, blean: String) => {
  console.log(val, blean, '弹窗确认事件')
  dialogRemAndReviref.value.trueLoading()
  if (blean == '催单') {
    $urgeOrder({ orderId: val.id, remarks: val.remarks })
      .then((res: any) => {
        if (res.code == 0) {
          success('催单成功')
          dialogRemAndReviref.value.falseLoading()
          dialogRemAndReviref.value.close()
          getTableData()
        }
      })
      .catch((err: any) => {
        dialogRemAndReviref.value.falseLoading()
      })
  } else if (blean == '审核') {
    $auditOrder({
      orderId: val.id,
      remarks: val.examineRemarks,
      approved: val.approved
    })
      .then((res: any) => {
        if (res.code == 0) {
          success('审核成功')
          dialogRemAndReviref.value.falseLoading()
          dialogRemAndReviref.value.close()
          getTableData()
        }
      })
      .catch((err: any) => {
        dialogRemAndReviref.value.falseLoading()
      })
  }
}
/**
 * 创建工单弹窗部分
 */
let executorList: any = ref([])
const changeExecutor = (e: String) => {
  if (e) {
    executorList.value.forEach((item: any) => {
      if (item.userId == e) {
        formData.value.executorName = item.name
      }
    })
  } else {
    formData.value.executorName = ''
  }
}
const dialogCreatOrderRef = ref<any>(null)
const createOrder = () => {
  // 请求获取可选执行人
  $_getMemberList({ current: 1, size: 999 }).then((res: any) => {
    if (res.code == 0) {
      executorList.value = res.data.records
      formData.value = {}
      dialogCreatOrderRef.value.open()
    }
  })
}
let formData: any = ref({})

const formRef = ref<any>(null)
// 验证规则
const rules = ref<any>({
  title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  type: [{ required: true, message: '请输入工单类型', trigger: 'blur' }],
  content: [{ required: true, message: '请输入任务内容', trigger: 'blur' }],
  filenames: [{ required: true, message: '请上传问题图片', trigger: 'blur' }],
  executorId: [{ required: true, message: '请选择执行人', trigger: 'blur' }],
  finishTime: [
    { required: true, message: '请选择要求完成时间', trigger: 'blur' }
  ]
})
const confirmEve = () => {
  // 验证表单
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      dialogCreatOrderRef.value.loading = true
      formData.value.source = 1
      $createOrder(formData.value).then((res: any) => {
        if (res.code == 0) {
          success('新增成功！')
          dialogCreatOrderRef.value.loading = false
          dialogCreatOrderRef.value.close()
          getTableData()
        }
      })

      console.log('要上传的数据', formData.value)
    }
  })
}

/**
 *表单部分
 */
let queryParams: any = ref({
  current: 1,
  size: 10
})

const getTableData = (parmasValue: any = queryParams.value) => {
  $getOrderList(parmasValue).then((res: any) => {
    console.log('我创建的工单', res)
    if (res.code == 0) {
      tableData.value = res.data.records
      tableData.value.forEach((item: any) => {})
      pageConfig.total = res.data.total
    }
  })
}
const tableData: any = ref([])
const tableTitle: Array<Object | any> = [
  {
    label: '工单编号',
    prop: 'workNo',
    type: 'text',
    width: '200px'
  },
  {
    label: '标题',
    prop: 'title',
    type: 'text'
  },
  {
    label: '工单来源',
    prop: 'sourceMark',
    type: 'text'
  },
  {
    label: '工单类型',
    prop: 'typeMark',
    type: 'text'
  },
  {
    label: '问题图片',
    prop: 'filenames',
    type: 'image',
    width: '180px'
  },
  {
    label: '任务内容',
    prop: 'content',
    type: 'text'
  },
  {
    label: '派单人',
    prop: 'creatorName',
    type: 'text'
  },
  {
    label: '创建时间',
    prop: 'finishTime',
    type: 'text'
  },
  {
    label: '要求完成时间',
    prop: 'finishTime',
    type: 'text'
  },
  {
    label: '执行人',
    prop: 'executorName',
    type: 'text'
  },
  {
    label: '逾期状态',
    name: 'overdue',
    type: 'custom'
  },
  {
    label: '处理状态',
    prop: 'statusMark',
    type: 'text'
  },
  {
    label: '操作',
    name: 'linkBtn',
    type: 'custom',
    width: '280px'
  }
]

const pageConfig: PageConfigType = {
  currentPage: 1,
  pageSize: 20,
  pageSizes: [10, 20, 30, 40],
  total: 10,
  background: true,
  layout: 'total,prev, pager,  next, sizes, jumper',
  handleSizeChange: (e: any) => sizeChange(e),
  handleCurrentChange: (e: any) => cuttentChange(e)
}
const cuttentChange = (e: any) => {
  pageConfig.currentPage = e
  queryParams.value.current = e
  getTableData()
}

// 每页显示条数
const sizeChange = (e: any) => {
  pageConfig.pageSize = e
  queryParams.value.size = e

  getTableData()
}
const overdueList = [
  {
    value: false,
    label: '未逾期'
  },
  {
    value: true,
    label: '已逾期'
  }
]
const statusList = [
  {
    value: 1,
    label: '待接单'
  },
  {
    value: 2,
    label: '已接单'
  },
  {
    value: 3,
    label: '转单待接单'
  },
  {
    value: 4,
    label: '已完成'
  },
  {
    value: 5,
    label: '已处理待审核'
  }
]
</script>

<style scoped></style>

<template>
  <dialog-view
    ref="dialogCreatOrderRef"
    width="800px"
    class="flex"
    :title="props.typeBoolean"
    @confirm="confirmEve"
  >
    <el-form
      :model="dataValue"
      label-width="120px"
      ref="rmandreformRef"
      :rules="rules"
      inline
      style="max-width: 800px"
    >
      <div class="df">
        <el-form-item label="工单编号:" class="f1">
          <span>{{ dataValue.workNo }}</span>
        </el-form-item>
        <el-form-item label="执行人:" class="f1">
          <span>{{ dataValue.executorName }}</span>
        </el-form-item>
      </div>
      <div class="df">
        <el-form-item label="标题:" class="f1">
          <span>{{ dataValue.title }}</span>
        </el-form-item>
        <el-form-item label="要求完成时间:" class="f1">
          <span>{{ dataValue.finishTime }}</span>
        </el-form-item>
      </div>
      <el-form-item label="问题照片:" style="width: 100%">
        <div style="display: flex; align-items: center; width: 100%">
          <el-image
            style="width: 100px; margin: 5px"
            v-for="it in dataValue.filenames"
            :src="baseUrl + '/admin/sys-file/oss/file?fileName=' + it"
          />
        </div>
      </el-form-item>
      <el-form-item label="任务内容:" style="width: 100%">
        <span>{{ dataValue.content }}</span>
      </el-form-item>
      <el-form-item
        label="完成情况"
        style="width: 100%"
        v-if="props.typeBoolean == '审核'"
      >
        <span>{{ dataValue.process?.remarks }}</span>
      </el-form-item>
      <el-form-item
        v-if="props.typeBoolean == '审核'"
        label="处理照片:"
        style="width: 100%"
      >
        <div style="display: flex; align-items: center; width: 100%">
          <el-image
            style="width: 100px; margin: 5px"
            v-for="it in dataValue.process?.filenames"
            :src="baseUrl + '/admin/sys-file/oss/file?fileName=' + it"
          />
        </div>
      </el-form-item>
      <el-form-item
        v-if="props.typeBoolean == '催单'"
        label="捎个话:"
        prop="remarks"
        style="width: 100%"
      >
        <el-input
          type="textarea"
          v-model="dataValue.remarks"
          :rows="5"
          placeholder="请输入捎话内容"
        />
      </el-form-item>
      <el-form-item
        v-if="props.typeBoolean == '审核'"
        label="审核意见:"
        prop="examineRemarks"
        style="width: 100%"
      >
        <el-input
          type="textarea"
          v-model="dataValue.examineRemarks"
          :rows="5"
          placeholder="请输入审核内容"
        />
      </el-form-item>
      <el-form-item
        v-if="props.typeBoolean == '审核'"
        label="审核结果:"
        prop="approved"
        style="width: 100%"
      >
        <el-select
          v-model="dataValue.approved"
          clearable
          placeholder="请选择"
          style="width: 150px"
          @change="searchEvd"
        >
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <p v-if="props.typeBoolean == '处置上报'">完成情况</p>
      <el-form-item
        v-if="props.typeBoolean == '处置上报'"
        label="处理情况描述:"
        prop="remarks"
        style="width: 100%"
      >
        <el-input
          type="textarea"
          v-model="dataValue.remarks"
          :rows="5"
          placeholder="请输入处理情况描述"
        />
      </el-form-item>
      <el-form-item
        v-if="props.typeBoolean == '处置上报'"
        label="处理照片:"
        prop="filenames"
        style="width: 100%"
      >
        <uploadImg
          v-model:fileList="dataValue.upLodafilenames"
          :baseUrl="baseUrl"
        ></uploadImg>
      </el-form-item>
      <p v-if="props.typeBoolean == '转单'">原因说明</p>
      <el-form-item
        v-if="props.typeBoolean == '转单'"
        label="转单原因:"
        prop="remarks"
        style="width: 100%"
      >
        <el-input
          type="textarea"
          v-model="dataValue.remarks"
          :rows="5"
          placeholder="请输入处理情况描述"
        />
      </el-form-item>
      <el-form-item
        v-if="props.typeBoolean == '转单'"
        label="转给谁:"
        prop="filenames"
        style="width: 100%"
      >
        <el-select
          v-model="dataValue.changeExecutorId"
          placeholder="请选择转给"
          size="large"
          style="width: 200px"
        >
          <el-option
            v-for="item in executorList"
            :key="item.userId"
            :label="item.name"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </dialog-view>
</template>

<script setup lang="ts">
import DialogView from '@/components/DialogView/index.vue'
import uploadImg from './upLoadImg.vue'
import { ref } from 'vue'
const props = defineProps({
  typeBoolean: {
    type: String,
    default: '催单'
  },
  executorList: {
    type: Array,
    default: () => []
  }
})
const emits = defineEmits()
const baseUrl = import.meta.env.VITE_RESOURCE_URL
const dialogCreatOrderRef = ref<any>(null)
const dataValue = ref<any>(null)
const rmandreformRef = ref<any>(null)
const confirmEve = () => {
  // 验证表单
  rmandreformRef.value.validate((valid: boolean) => {
    if (valid) {
      emits('remindConfigBtn', dataValue.value, props.typeBoolean)
    }
  })
}
// 验证规则
const rules = ref<any>({
  remarks: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  examineRemarks: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
  approved: [{ required: true, message: '请选择审核结果', trigger: 'blur' }],
  upLodafilenames: [
    { required: true, message: '请上传处理照片', trigger: 'blur' }
  ]
})
const statusList = [
  {
    value: true,
    label: '通过'
  },
  {
    value: false,
    label: '再次处理'
  }
]
// 打开弹窗s事件
const open = (data = null) => {
  dataValue.value = data
  dialogCreatOrderRef.value.open()
}
const close = (data = null) => {
  dialogCreatOrderRef.value.close()
}
const trueLoading = () => {
  dialogCreatOrderRef.value.loading = true
}
const falseLoading = () => {
  dialogCreatOrderRef.value.loading = false
}
defineExpose({ open, trueLoading, falseLoading, close })
</script>

<style scoped></style>

<template>
  <el-drawer
    v-model="showDrawer"
    title="申请平台授权"
    direction="rtl"
    size="26%"
    :close-on-click-modal="false"
    @open="openDrawer"
  >
    <el-form
      :model="formDataTwo"
      label-width="120px"
      ref="formRefTwo"
      :rules="rules"
      style="max-width: 800px"
    >
      <el-form-item label="申请组织">
        <span>{{ orgName }}</span>
      </el-form-item>
      <el-form-item label="标题名称" prop="title">
        <el-input
          v-model="formDataTwo.title"
          placeholder="请输入标题名称"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="userName">
        <el-input
          v-model="formDataTwo.userName"
          placeholder="请输入申请人"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="contacts">
        <el-input
          v-model="formDataTwo.contacts"
          placeholder="请输入联系人"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="申请内容" prop="content">
        <el-input
          type="textarea"
          v-model="formDataTwo.content"
          :rows="5"
          placeholder="请输入申请内容"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input
          type="textarea"
          v-model="formDataTwo.remarks"
          :rows="5"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer pb-[36px]">
        <el-button color="#F2F3F5" @click="close">取消</el-button>
        <el-button type="primary" @click="dialogConfirmEve" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { $applyAuth } from '@/api/order/index.ts'
import { success } from '@/utils/toast.ts'

const showDrawer = defineModel('showDrawer', {
  type: Boolean,
  default: false
})

const formDataTwo = ref<any>({})

const orgName = ref<String>('')

const formRefTwo = ref<any>(null)

const rules = ref<any>({
  title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  userName: [{ required: true, message: '请输入申请人', trigger: 'blur' }],
  contacts: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  remarks: [{ required: true, message: '请输入备注', trigger: 'blur' }],
  content: [{ required: true, message: '请输入任务内容', trigger: 'blur' }]
})

const loading = ref<boolean>(false)

const close = () => {
  showDrawer.value = false
}

const dialogConfirmEve = () => {
  formRefTwo.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      let res: any = await $applyAuth({
        orgName: orgName.value,
        ...formDataTwo.value
      })
      loading.value = false
      if (res.code == 0) {
        success('申请成功')
        showDrawer.value = false
      }
    }
  })
}

const openDrawer = () => {
  orgName.value = localStorage.getItem('userBaseInfo')
    ? JSON.parse(localStorage.getItem('userBaseInfo')!)?.userBaseInfo?.orgName
    : ''
  if (!formRefTwo.value) return
  formRefTwo.value.resetFields()
}
</script>

<style lang="scss" scoped></style>

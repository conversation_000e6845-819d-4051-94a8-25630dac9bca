<template>
  <el-upload
    v-model:file-list="imageLIst"
    :action="props.baseUrl + '/admin/sys-file/upload'"
    list-type="picture-card"
    multiple
    :on-preview="handlePictureCardPreview"
    :on-remove="handleRemove"
    :http-request="customRequest"
  >
    <el-icon><Plus /></el-icon>
  </el-upload>
  <el-dialog v-model="dialogVisible">
    <img w-full :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script setup lang="ts">
import type { UploadProps, UploadUserFile } from 'element-plus'
import { ref, defineModel, onBeforeMount } from 'vue'
import { $upLoadFile } from '@/api/order/index.ts'

const props = defineProps({
  baseUrl: {
    type: String,
    default: ''
  }
})
onBeforeMount(() => {
  if (fileList.value?.length > 0) {
    imageLIst.value = fileList.value.map((v) => {
      return {
        url: props.baseUrl + '/admin/sys-file/oss/file?fileName=' + v,
        fileName: v
      }
    })
  } else {
    imageLIst.value = []
  }

  console.log('接收父组件传的值', fileList.value, props.baseUrl)
})
const fileList = defineModel('fileList', {
  type: Array,
  default: () => []
})
const imageLIst = ref<Array<any>>([])
const customRequest = ({
  file,
  onSuccess,
  onError
}: {
  file: UploadUserFile
  onSuccess: Function
  onError: Function
}) => {
  const formData = new FormData()
  formData.append('file', file as any)
  $upLoadFile(formData).then((res: any) => {
    if (res.code == 0) {
      res.data.url =
        props.baseUrl + '/admin/sys-file/oss/file?fileName=' + res.data.fileName
      imageLIst.value.push(res.data)
      imageLIst.value = imageLIst.value.filter((item: any) => item.fileName)
      fileList.value = imageLIst.value.map((item) => item.fileName)
    }
    console.log('上传图片成功', res, fileList.value)
  })
  console.log('打印数据', file, onSuccess, onError)
}
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handleRemove = (uploadFile: any, uploadFiles: any) => {
  console.log(uploadFile)
  imageLIst.value = imageLIst.value.filter(
    (item: any) => item.fileName !== uploadFile.fileName
  )
  fileList.value = imageLIst.value.map((item) => item.fileName)
}
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisible.value = true
}
</script>

<style scoped></style>

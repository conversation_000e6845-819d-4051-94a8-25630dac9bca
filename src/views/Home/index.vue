<template>
  <div
    class="w-full h-full grid gap-[16px] p-[16px] box-border rounded"
    style="grid-template-rows: 2fr 1fr 2fr"
  >
    <div class="content">
      <div class="content grid gap-x-[16px] grid-cols-3">
        <div class="contentBox">
          <div class="flex flex-col h-full gap-y-[24px]">
            <div class="header">设备概况</div>
            <div class="grid grid-cols-2 gap-x-[22px] h-full">
              <div class="device-content" style="background-color: #f6f8ff">
                <div class="device-content-header">
                  <div class="header-content">
                    <div class="header-content-title">视频数量</div>
                    <div class="header-content-value">
                      {{ videoInfo?.total || 0 }}
                    </div>
                  </div>
                  <div class="header-echart">
                    <v-chart
                      class="w-full h-full"
                      :option="videoOption"
                      autoresize
                    />
                  </div>
                </div>
                <div class="w-full device-line"></div>
                <div class="w-full h-full grid grid-rows-3 gap-y-[16px]">
                  <div class="device-content-value">
                    <div class="device-content-value-title">
                      <img
                        class="w-[20px] h-20px"
                        src="@/assets/home/<USER>"
                      />
                      <div>在线</div>
                    </div>
                    <div class="device-content-value-value">
                      {{ videoInfo?.online || 0 }}台
                    </div>
                  </div>
                  <div class="device-content-value">
                    <div class="device-content-value-title">
                      <img
                        class="w-[20px] h-20px"
                        src="@/assets/home/<USER>"
                      />
                      <div>离线</div>
                    </div>
                    <div class="device-content-value-value">
                      {{ videoInfo?.offline || 0 }}台
                    </div>
                  </div>
                  <div class="device-content-value">
                    <div class="device-content-value-title">
                      <img
                        class="w-[20px] h-20px"
                        src="@/assets/home/<USER>"
                      />
                      <div>授权</div>
                    </div>
                    <div class="device-content-value-value">
                      {{ videoInfo?.grant || 0 }}台
                    </div>
                  </div>
                </div>
              </div>
              <div class="device-content" style="background-color: #f6f8ff">
                <div class="device-content-header">
                  <div class="header-content">
                    <div class="header-content-title">传感器数量</div>
                    <div class="header-content-value">100</div>
                  </div>
                  <div class="header-echart">
                    <v-chart
                      class="w-full h-full"
                      :option="sensorOption"
                      autoresize
                    />
                  </div>
                </div>
                <div class="w-full device-line"></div>
                <div class="w-full h-full grid grid-rows-3 gap-y-[16px]">
                  <div class="device-content-value">
                    <div class="device-content-value-title">
                      <img
                        class="w-[20px] h-20px"
                        src="@/assets/home/<USER>"
                      />
                      <div>在线</div>
                    </div>
                    <div class="device-content-value-value">60台</div>
                  </div>
                  <div class="device-content-value">
                    <div class="device-content-value-title">
                      <img
                        class="w-[20px] h-20px"
                        src="@/assets/home/<USER>"
                      />
                      <div>离线</div>
                    </div>
                    <div class="device-content-value-value">60台</div>
                  </div>
                  <div class="device-content-value">
                    <div class="device-content-value-title">
                      <img
                        class="w-[20px] h-20px"
                        src="@/assets/home/<USER>"
                      />
                      <div>授权</div>
                    </div>
                    <div class="device-content-value-value">60台</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentBox">
          <div class="flex flex-col h-full gap-y-[24px]">
            <div class="header">服务概况</div>
            <div
              class="grid grid-cols-2 grid-rows-2 gap-x-[26px] gap-y-[23px] h-full"
            >
              <div class="service-content" style="background-color: #e9eeff">
                <img src="@/assets/home/<USER>" />
                <div class="service-content-text">
                  <div class="service-content-text-value">
                    {{ serviceInfo?.total || 0 }}
                  </div>
                  <div class="service-content-text-title">接口数量 / 条</div>
                </div>
              </div>
              <div class="service-content" style="background-color: #e9eeff">
                <img src="@/assets/home/<USER>" />
                <div class="service-content-text">
                  <div class="service-content-text-value">
                    {{ serviceInfo?.allNum || 0 }}
                  </div>
                  <div class="service-content-text-title">接口使用 / 条</div>
                </div>
              </div>
              <div class="service-content" style="background-color: #f1feff">
                <img src="@/assets/home/<USER>" />
                <div class="service-content-text">
                  <div class="service-content-text-value">
                    {{ serviceInfo?.todayUseNum || 0 }}
                  </div>
                  <div class="service-content-text-title">今日调用 / 次</div>
                </div>
              </div>
              <div class="service-content" style="background-color: #f1feff">
                <img src="@/assets/home/<USER>" />
                <div class="service-content-text">
                  <div class="service-content-text-value">
                    {{ serviceSuccessRate
                    }}<span
                      style="
                        font-family: 'Source Han Sans CN, Source Han Sans CN';
                        font-weight: 500;
                        font-size: 16px;
                        color: #80909a;
                        line-height: 19px;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                        margin-left: 4px;
                      "
                      >%</span
                    >
                  </div>
                  <div class="service-content-text-title">调用成功率</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentBox appContent">
          <div class="flex flex-col h-full gap-y-[24px]">
            <div class="header">应用概况</div>
            <div class="app-content">
              <div class="app-content-item">
                <div class="app-content-title">
                  <div class="app-content-title-line"></div>
                  <div class="app-content-title-text">应用数</div>
                </div>
                <div class="app-content-value">
                  <div class="app-content-value-value">20</div>
                  <div class="app-content-value-unit">个</div>
                </div>
              </div>

              <div class="app-content-item">
                <div class="app-content-title">
                  <div class="app-content-title-line"></div>
                  <div class="app-content-title-text">在线率</div>
                </div>
                <div class="app-content-value">
                  <div class="app-content-value-value">80</div>
                  <div class="app-content-value-unit">%</div>
                </div>
              </div>
              <div class="app-content-item">
                <div class="app-content-title">
                  <div class="app-content-title-line"></div>
                  <div class="app-content-title-text">正在执行应用</div>
                </div>
                <div class="app-content-value">
                  <div class="app-content-value-value">18</div>
                  <div class="app-content-value-unit">个</div>
                </div>
              </div>
              <div class="app-content-item">
                <div class="app-content-title">
                  <div class="app-content-title-line"></div>
                  <div class="app-content-title-text">产生事件结果</div>
                </div>
                <div class="app-content-value">
                  <div class="app-content-value-value">18000</div>
                  <div class="app-content-value-unit">条</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content w-full">
        <div class="contentBox">
          <div class="flex flex-col h-full gap-y-[24px]">
            <div class="header">待办事项</div>
            <div class="todo-content">
              <div class="todo-one h-full">
                <div class="todo-title">待您处理</div>
                <div class="flex flex-row gap-x-[36px] items-center">
                  <div class="todo-value">99条</div>
                  <el-icon color="white" size="22px"
                    ><ArrowRightBold
                  /></el-icon>
                </div>
              </div>
              <div class="todo-two h-full">
                <div class="todo-title">超时未处理</div>
                <div class="flex flex-row gap-x-[36px] items-center">
                  <div class="todo-value">99条</div>
                  <el-icon color="white" size="22px"
                    ><ArrowRightBold
                  /></el-icon>
                </div>
              </div>
              <div class="todo-three h-full">
                <div class="todo-title">我发起的</div>
                <div class="flex flex-row gap-x-[36px] items-center">
                  <div class="todo-three-value">
                    <div
                      class="todo-three-value-text text-white underline underline-offset-6 underline-white"
                    >
                      总 | 10条
                    </div>
                    <div
                      class="todo-three-value-text text-white underline underline-offset-6 underline-white"
                    >
                      未完成 | 10条
                    </div>
                    <div
                      class="todo-three-value-text text-red-500 underline underline-offset-6 underline-red-500"
                    >
                      已逾期 | 10条
                    </div>
                  </div>
                  <el-icon color="white" size="22px"
                    ><ArrowRightBold
                  /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content grid gap-x-[16px] grid-cols-2">
        <div class="contentBox">
          <div class="flex flex-col h-full gap-y-[24px]">
            <div class="flex justify-between items-center">
              <div class="header">
                消息通知<span style="color: #adb2ba; margin-left: 6px"
                  >(10条未读)</span
                >
              </div>
              <div
                class="header flex items-center cursor-pointer"
                @click="handMore"
              >
                <div style="color: #adb2ba; margin-left: 6px">查看更多</div>
                <el-icon color="#adb2ba" size="18px"
                  ><ArrowRightBold
                /></el-icon>
              </div>
            </div>
            <div class="message-box">
              <div class="message-box-item" v-for="item in 5" :key="item">
                <div class="message-box-item-title">
                  <div
                    class="dto"
                    :style="{
                      backgroundColor: item <= 2 ? '#3665ff' : 'transparent'
                    }"
                  ></div>
                  <div>版本更新通知</div>
                </div>
                <div class="message-box-item-time">2024-08-12 12:22:12</div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentBox">
          <div class="flex flex-col h-full gap-y-[24px]">
            <div class="header">日历管理</div>
            <div class="w-full h-full">
              <img
                class="w-full h-full"
                src="@/assets/images/index-example.jpg"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { $_getHomeData, PromiseAllType } from '@/api/home'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import { GraphicComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { useRouter } from 'vue-router'
import videoNum from '@/assets/home/<USER>'
import sensorNum from '@/assets/home/<USER>'

use([CanvasRenderer, GraphicComponent, PieChart])
// 设备饼状图参数
const videoOption = computed(() => {
  return {
    color: ['rgba(232, 232, 232, 1)'],
    graphic: {
      elements: [
        {
          type: 'image',
          style: {
            image: videoNum,
            width: 28,
            height: 28
          },
          left: 'center',
          top: 'center'
        }
      ]
    },
    series: [
      {
        type: 'pie',
        clockwise: true,
        radius: ['50%', '66%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: videoInfo.value?.online,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            itemStyle: {
              color: '#3665FF'
            }
          },
          {
            value: videoInfo.value?.offline
          }
        ]
      }
    ]
  }
})
// 传感器饼状图参数
const sensorOption = computed(() => {
  return {
    color: ['rgba(232, 232, 232, 1)'],
    graphic: {
      elements: [
        {
          type: 'image',
          style: {
            image: sensorNum,
            width: 28,
            height: 28
          },
          left: 'center',
          top: 'center'
        }
      ]
    },
    series: [
      {
        type: 'pie',
        clockwise: true,
        radius: ['50%', '66%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: 80,
            itemStyle: {
              color: '#3665FF'
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          },
          {
            value: 20
          }
        ]
      }
    ]
  }
})

// video相关数据
const videoInfo = computed(() => {
  let data = homeData.value?.find((item: object) => {
    return Object.hasOwn(item, 'videoAccount')
  })
  if (data) {
    return data['videoAccount']
  }
})

// 服务相关数据
const serviceInfo = computed(() => {
  let data = homeData.value?.find((item: object) => {
    return Object.hasOwn(item, 'serviceInfo')
  })
  if (data) {
    return data['serviceInfo']
  }
})

// 服务接口成功率
const serviceSuccessRate = computed(() => {
  if (serviceInfo.value) {
    const successNum = Number(serviceInfo.value.successNum)
    const allNum = Number(serviceInfo.value.allNum)
    if (
      typeof successNum === 'number' &&
      typeof allNum === 'number' &&
      allNum !== 0 &&
      successNum !== 0
    ) {
      const rate = successNum / allNum
      return rate?.toFixed(4) ? Number(rate?.toFixed(4)) * 100 : 0
    } else {
      return 0
    }
  } else {
    return 0
  }
})
// 首页数据集合
const homeData = ref<Array<Partial<PromiseAllType>>>()

onMounted(async () => {
  let res = await $_getHomeData()
  console.log('🚀 ~ onMounted ~ res:', res)
  homeData.value = res
})

const $router = useRouter()
// 跳转更多消息
const handMore = () => {
  $router.push({ path: '/serverMessageCenter/logs' })
}
</script>

<style lang="scss" scoped>
.content {
  @apply w-full h-full;
}

.contentBox {
  @apply h-full px-[16px] py-[24px] bg-white box-border rounded;
}

.appContent {
  padding-right: 221px;
  box-sizing: border-box;
  background: url('@/assets/home/<USER>') no-repeat center / 100% 100%;
  background-color: white;
}

.header {
  font-family: 'Source Han Sans CN, Source Han Sans CN';
  font-weight: bold;
  font-size: 18px;
  color: #232830;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.device-content {
  @apply rounded flex flex-col gap-y-[21px] p-y-[24px] p-x-[25px] box-border;
  .device-line {
    @apply w-full;
    border: 1px solid;

    border-image: linear-gradient(
        90deg,
        rgba(54, 101, 255, 0),
        rgba(54, 101, 255, 0.15),
        rgba(54, 101, 255, 0.15),
        rgba(54, 101, 255, 0)
      )
      1 1;
  }

  .device-content-header {
    @apply w-full h-[150px] flex justify-between flex-row;
    .header-content {
      @apply flex flex-col justify-center items-center gap-y-[6px];
      &-title {
        font-family: 'Source Han Sans CN, Source Han Sans CN';
        font-weight: 500;
        font-size: 16px;
        color: #4a4a4a;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      &-value {
        font-family: 'Source Han Sans CN, Source Han Sans CN';
        font-weight: bold;
        font-size: 36px;
        color: #000000;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .header-echart {
      @apply h-[120px] w-[120px];
    }
  }

  .device-content-value {
    @apply flex flex-row items-center justify-between;
    &-title {
      @apply flex gap-x-[6px] items-center;
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: 500;
      font-size: 14px;
      color: #8d92a1;
      line-height: 16px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    &-value {
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: 500;
      font-size: 14px;
      color: #4a4a4a;
      line-height: 16px;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }
  }
}

.service-content {
  @apply rounded py-[32px] px-[38px] box-border flex flex-row items-center justify-between;
  img {
    @apply w-[70px] h-[70px];
  }
  .service-content-text {
    @apply flex flex-col gap-y-[4px];
    &-value {
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: bold;
      font-size: 24px;
      color: #000000;
      line-height: 28px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    &-title {
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: 500;
      font-size: 14px;
      color: #747474;
      line-height: 16px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}

.app-content {
  @apply grid grid-cols-2 grid-rows-2 gap-y-[58px] w-full h-full  py-[38px] px-[25px];
  .app-content-item {
    @apply flex flex-col justify-between;
    .app-content-title {
      @apply flex flex-col gap-y-[12px];
      &-line {
        width: 24px;
        height: 0px;
        border: 2px solid #3665ff;
        border-radius: 2px;
      }
      &-text {
        font-family: 'Source Han Sans CN, Source Han Sans CN';
        font-weight: bold;
        font-size: 16px;
        color: #80909a;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .app-content-value {
    @apply flex flex-row gap-x-[8px] items-end;
    &-value {
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: bold;
      font-size: 32px;
      color: #000000;
      line-height: 38px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    &-unit {
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: 500;
      font-size: 16px;
      color: #80909a;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}

.todo-content {
  @apply grid gap-x-[16px] grid-cols-3 h-full;
  .todo-one {
    @apply py-[14px] px-[24px] box-border flex justify-between items-center;
    background: url('@/assets/home/<USER>') no-repeat center / 100% 100%;
  }
  .todo-two {
    @apply py-[14px] px-[24px] box-border flex justify-between items-center;
    background: url('@/assets/home/<USER>') no-repeat center / 100% 100%;
  }
  .todo-three {
    @apply py-[14px] px-[24px] box-border flex justify-between items-center;
    background: url('@/assets/home/<USER>') no-repeat center / 100% 100%;
  }
  .todo-title {
    font-family: 'Source Han Sans CN, Source Han Sans CN';
    font-weight: bold;
    font-size: 24px;
    color: #ffffff;
    line-height: 28px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .todo-value {
    @apply underline underline-offset-6 cursor-pointer;
    font-family: 'Source Han Sans CN, Source Han Sans CN';
    font-weight: bold;
    font-size: 24px;
    color: #ffffff;
    line-height: 28px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .todo-three-value {
    @apply flex flex-row gap-x-[14px] items-center;
    .todo-three-value-text {
      @apply cursor-pointer;
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: 500;
      font-size: 18px;
      line-height: 21px;
      text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
      text-align: center;
      font-style: normal;
    }
    .todo-three-value-text::before {
      content: ' ';
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 6px;
      background: #ffffff;
      border-radius: 50%;
    }
  }
}

.message-box {
  @apply grid grid-rows-5 gap-y-[12px] w-full h-full;
  .message-box-item {
    @apply py-[8px] px-[20px] box-border flex justify-between items-center;
    &-title {
      @apply flex items-center flex-row;
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: 400;
      font-size: 16px;
      color: #2d3243;
      line-height: 19px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    &-time {
      font-family: 'Source Han Sans CN, Source Han Sans CN';
      font-weight: 400;
      font-size: 16px;
      color: #8d92a1;
      line-height: 19px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .dto {
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 12px;
      border-radius: 50%;
    }
  }
  .message-box-item:nth-child(odd) {
    @apply w-full;
    background-color: #f6f8ff;
  }
  .message-box-item:nth-child(even) {
    @apply w-full;
    background-color: #f1feff;
  }
}
</style>

<template>
  <div
    class="flex flex-row w-full h-full overflow-hidden"
    style="background-color: #f2f4f5"
  >
    <div class="content-tree bg-white">
      <div
        class="left flex flex-col w-[250px] h-[full] flex-shrink-0 overflow-hidden"
        style="border-right: 1px solid #e2e2e2"
      >
        <div
          style="font-weight: bold"
          class="bg-tit flex-shrink-0 h-[66px] bg-[#3665FF] bg-opacity-10 w-full text-[20px] flex items-center justify-center text-[#3665FF]"
        >
          服务目录
        </div>
        <div class="p-[8px] box-border">
          <el-input placeholder="服务名称" clearable>
            <template #suffix>
              <el-icon><Search /></el-icon> </template
          ></el-input>
        </div>
        <div class="h-[calc(100%-66px)] w-full p-[8px]">
          <el-tree
            :data="listData"
            :props="defaultProps"
            :expand-on-click-node="false"
            :highlight-current="true"
            :default-expand-all="true"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </div>
    </div>
    <div class="content overflow-hidden">
      <div class="p-[16px] box-border w-full h-full flex flex-col gap-y-[16px]">
        <div class="content-header" v-if="serviceInfo == null">
          <div class="header-item">
            <span>算法服务总数:</span>
            <span>50</span>
          </div>
          <div
            class="header-item"
            style="flex-direction: column; justify-content: center"
          >
            <div class="flex justify-between w-full">
              <span>使用路数:</span>
              <span>45</span>
            </div>
            <div class="flex justify-between w-full">
              <span>算法服务总路数:</span>
              <span>50</span>
            </div>
          </div>
          <div class="header-item">
            <span>产生事件数:</span>
            <span>300</span>
          </div>
        </div>
        <div
          v-if="serviceInfo == null"
          class="flex-1 flex flex-col justify-between overflow-hidden h-full"
        >
          <div class="flex-1 overflow-auto">
            <div class="content-box">
              <div
                class="content-box-item flex"
                v-for="item in list"
                :key="item.title"
              >
                <div class="w-400px">
                  <div
                    class="item-type"
                    :style="{ backgroundColor: typeBgColor(item.type) }"
                  >
                    {{ item.type }}
                  </div>
                  <div class="w-full flex flex-row items-center">
                    <svg-icon
                      name="link"
                      style="width: 24px; height: 24px; margin-right: 5px"
                    ></svg-icon>
                    <div class="text-[18px] font-bold">{{ item.title }}</div>
                  </div>
                </div>

                <!-- <div class="item-text">资源分类：1/2/3</div> -->
                <div class="w-full flex flex-col justify-between">
                  <div class="flex align-center mb-[32px]">
                    <div class="item-text">
                      资源提供方: {{ item.dataSource }}
                    </div>
                    <!-- <div class="item-text">
                    算法授权总路数: {{ item.dataSource }}
                  </div>
                  <div class="item-text">使用路数: {{ item.dataSource }}</div> -->
                  </div>
                  <div class="flex align-center mb-[32px]">
                    <div class="item-text">创建时间: {{ item.createTime }}</div>
                    <!-- <div class="item-text">到期时间: {{ item.updateTime }}</div> -->
                  </div>
                  <!-- 按钮部分 -->
                  <div class="flex align-center justify-end">
                    <el-button type="primary" @click="clickRunDetail(item)"
                      >事件记录</el-button
                    >
                    <el-button
                      type="primary"
                      @click="clickServiceItem(item, 'describe')"
                      >算法说明</el-button
                    >
                    <el-button
                      type="primary"
                      @click="clickServiceItem(item, 'second')"
                      >错误参照码</el-button
                    >
                    <el-button
                      type="primary"
                      @click="clickServiceItem(item, 'first')"
                      >API文档</el-button
                    >
                    <el-button
                      type="primary"
                      @click="clickServiceItem(item, 'third')"
                      >示例代码</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="serviceInfo == null"
          class="flex flex-shrink-0 flex-row justify-end mt-[16px] bg-white p-[16px] box-border"
        >
          <el-pagination
            class="fn"
            :current-page="pageConfig.currentPage"
            :page-size="pageConfig.pageSize"
            layout="total,prev, pager,  next, sizes, jumper"
            background
            :total="pageConfig.total"
            @size-change="pageConfig.handleSizeChange"
            @current-change="pageConfig.handleCurrentChange"
          >
            <template #default>
              <div>
                共
                <span class="text-[#3665FF] font-bold">{{
                  pageConfig.total
                }}</span>
                条
              </div>
            </template>
          </el-pagination>
        </div>
        <div v-else class="flex-1 overflow-hidden w-full">
          <service-details
            @infoCallback="infoCallback"
            :info="serviceInfo"
            v-model:typeTab="serviceType"
          ></service-details>
        </div>
        <runDia
          v-model:showRun="showRun"
          :runDetailValue="runDetailValue"
        ></runDia>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import serviceDetails from './components/serViceDetails.vue'
import runDia from './components/runDia.vue'
import { $getServiceTypeList, $getAuthorizedApiList } from '@/api/servicePage'

const listData = ref(
  [] as { label: string; value: string; sourceCode: string; children?: any[] }[]
)
//获取服务平台的服务类型列表
function getServiceTypeList() {
  $getServiceTypeList().then((res: any) => {
    listData.value = res.data.map((item: any) => {
      return {
        label: item.sourceName,
        value: item.sourceCode,
        children: item.apiTypeId.map((api: any) => {
          return {
            label: Object.values(api)[0],
            value: Object.keys(api)[0],
            sourceCode: item.sourceCode
          }
        })
      }
    })
    listData.value = [
      { label: '全部', value: 'all', sourceCode: '' },
      ...listData.value
    ]
    console.log(listData.value)
  })
}
getServiceTypeList()
let orgObj = JSON.parse(localStorage.getItem('userBaseInfo')!).userBaseInfo

// 请求筛选项
const tablePagination = ref<any>({
  current: 1,
  size: 20,
  apiTypeId: '',
  sourceCode: '',
  grantDataIds: [orgObj.orgId]
})
// 分页数据
const pageConfig = reactive<any>({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  handleSizeChange: (e: any) => {
    sizeChange(e)
  },
  handleCurrentChange: (e: any) => {
    pageChange(e)
  }
})
function getAuthorizedApiList() {
  $getAuthorizedApiList(tablePagination.value).then((res: any) => {
    list.value = res.data.records.map((item: any) => {
      return {
        type: item.sourceName,
        title: item.apiServerName,
        createTime: item.createTime,
        updateTime: item.updateTime,
        sourceName: item.sourceName,
        dataSource: item.dataSource,
        data: item
      }
    })
    pageConfig.total = +res.data.total
  })
}
getAuthorizedApiList({})

const defaultProps = {
  children: 'children',
  label: 'label',
  value: 'id'
}

function handleNodeClick(data: any) {
  console.log('打印选择项', data)
  if (data.sourceCode) {
    tablePagination.value.sourceCode = data.sourceCode
    tablePagination.value.apiTypeId = data.value
  } else if (data.value !== 'all') {
    tablePagination.value.sourceCode = data.value
  } else {
    tablePagination.value.sourceCode = data.sourceCode
    tablePagination.value.apiTypeId = ''
  }
  getAuthorizedApiList()
}

const list = ref([])

const typeBgColor = computed(() => {
  return (type: string) => {
    switch (type) {
      case '数据服务':
        return '#FFB400'
      case '算法服务':
        return '#28a745'
      case '业务服务':
        return '#3665FF'
    }
  }
})
// 运行记录
const runDetailValue = ref<any>({})
const showRun = ref<boolean>(false)
const clickRunDetail = (item: any) => {
  console.log('item', item)
  runDetailValue.value.apiServerName = item.data.apiServerName
  runDetailValue.value.apiServerCode = item.data.apiServerCode
  showRun.value = true
}
// 选中的服务信息
const serviceInfo = ref<any>(null)
const serviceType = ref<any>('describe')

const clickServiceItem = (item: any, type: string) => {
  serviceInfo.value = item
  serviceType.value = type
}

const infoCallback = () => {
  serviceInfo.value = null
}
// 分页事件
const sizeChange = (e: any) => {
  pageConfig.pageSize = e
  tablePagination.value.size = e
  getAuthorizedApiList()
}
const pageChange = (e: any) => {
  pageConfig.currentPage = e
  tablePagination.value.current = e
  getAuthorizedApiList()
}
</script>

<style scoped lang="scss">
.content {
  @apply w-full h-full;
  .content-header {
    @apply h-120px  p-[16px] pl-0 box-border rounded flex;
    .header-item {
      @apply flex items-center justify-between bg-white w-300px mr-32px border box-border pl-42px pr-42px w-100%;
      span {
        @apply font-weight-bold;
        font-size: 18px;
      }
    }
  }
  .content-box {
    @apply flex flex-col gap-y-[16px] gap-x-[16px] w-full h-full;
    &-item {
      @apply relative w-full  border rounded flex   p-[16px] box-border justify-between bg-white  cursor-pointer;
      .item-type {
        @apply absolute right-0 top-0 py-[8px] px-[16px] box-border  text-white;
      }
      .item-text {
        @apply text-gray-400 mr-50px;

        font-size: 16px;
      }
    }
  }
}
</style>

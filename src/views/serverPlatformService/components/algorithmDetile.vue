<template>
  <div>
    <!-- {{ detileData }} -->
    <div class="df mb-[24px] aic">
      <div class="w-[320px] h-[195px] bg-[#D9D9D9] fn mr-[38px]">
        <img
          :src="baseUrl + '/oss/file/preview?fileName=' + infoData?.picUrl"
          alt=""
          class="w-full h-full"
        />
      </div>
      <div class="flex-1">
        <div class="df">
          <div class="w-[220px] fn">
            <div class="title">算法名称</div>
            <div class="content">{{ infoData?.apiServerName }}</div>
          </div>
          <div class="flex-1">
            <div class="title">算法描述</div>
            <div class="content">
              {{ infoData?.description }}
            </div>
          </div>
        </div>
        <div class="df jcsb mt-[24px] w-full" style="flex-wrap: wrap">
          <div class="mr-8 flex-1" v-if="infoData?.sourceType">
            <div class="title">分析源类型</div>
            <div class="content">
              {{
                dictArr
                  .find((item) => item.name == 'sourceType')
                  .data.find((it) => it.value === infoData?.sourceType).label
              }}
            </div>
          </div>
          <div class="mr-8 flex-1" v-if="infoData?.analysisType">
            <div class="title">分析类型</div>
            <div class="content">
              {{
                dictArr
                  .find((item) => item.name == 'analysisType')
                  .data.find((it) => it.value === infoData?.analysisType).label
              }}
            </div>
          </div>
          <div class="mr-8 flex-1" v-if="infoData?.algorithmTypeName">
            <div class="title">算法类型</div>
            <div class="content">{{ infoData?.algorithmTypeName }}</div>
          </div>
          <!-- <div class="mr-8" v-if="infoData?.vendor">
              <div class="title">厂商</div>
              <div class="content">{{ infoData?.vendor }}</div>
            </div> -->
        </div>
      </div>
    </div>
    <div class="df">
      <div
        class="w-[334px]"
        v-if="infoData?.analysisTargetName || infoData?.analysisTargetName"
      >
        <div class="title">分析目标</div>
        <div class="content" v-if="infoData?.analysisTargetName">
          <div v-for="it in infoData?.analysisTargetName?.split(',')">
            {{ it }}
          </div>
        </div>
      </div>
      <div class="w-[334px]" v-if="infoData?.appIndust">
        <div class="title">适用行业</div>
        <div class="content">
          <div v-for="it in infoData?.appIndustName?.split(',')">{{ it }}</div>
        </div>
      </div>
      <div class="w-[334px]" v-if="infoData?.appPlace">
        <div class="title">适用场所</div>
        <div class="content">
          <div v-for="it in infoData?.appPlaceName?.split(',')">{{ it }}</div>
        </div>
      </div>
    </div>
    <div
      class="title mt-[24px]"
      v-if="infoData?.algorithmConditionsDtos?.length > 0"
    >
      应用条件
    </div>
    <div class="df tiaojianlist" style="flex-wrap: wrap">
      <div
        class="w-[334px] h-[164px] mr-[24px] mb-[16px] item"
        v-for="item in infoData?.algorithmConditionsDtos"
      >
        <div class="title">{{ item.title }}</div>
        <div>
          {{ item.describe }}
        </div>
      </div>
      <!-- <div class="w-[334px] h-[164px] bg-red-500 mr-[24px] mb-[16px] item"></div>
        <div class="w-[334px] h-[164px] bg-red-500 mr-[24px] mb-[16px] item"></div>
        <div class="w-[334px] h-[164px] bg-red-500 mr-[24px] mb-[16px] item"></div>
        <div class="w-[334px] h-[164px] bg-red-500 mr-[24px] mb-[16px] item"></div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { $getDictType } from '@/api/dict/index.ts'
import { $getAlgorithmDetail } from '@/api/servicePage'

const baseUrl = import.meta.env.VITE_RESOURCE_URL
const detileEl = ref()

const props = defineProps({
  data: {
    type: Object,
    default: {}
  }
})
const infoData = ref()
const detileData = computed(() => {
  let data = JSON.parse(JSON.stringify(props.data))
  return data
})
// 字典数组
const dictArr = ref([
  // 源类型
  {
    data: [],
    type: 'alg_source_type',
    name: 'sourceType'
  },
  // 分析类型
  {
    data: [],
    type: 'alg_analy_type',
    name: 'analysisType'
  },
  // 分析目标
  {
    data: [],
    type: 'analy_target',
    name: 'analysisTarget'
  },
  // 适用行业
  {
    data: [],
    type: 'applicable_industry',
    name: 'appIndust'
  },
  // 适用场所
  {
    data: [],
    type: 'applicable_place',
    name: 'appPlace'
  }
])

// 获取字典数据
const getDictData = () => {
  dictArr.value.forEach((item) => {
    $getDictType(item.type).then((res) => {
      item.data = res.data
    })
  })
  console.log(dictArr.value)
}

onMounted(() => {
  console.log('打印获取的数据', detileData)
  getDictData()
})
// 获取算法详情
const getInfo = (id) => {
  $getAlgorithmDetail({ code: id }).then((res) => {
    if (res.code === 0) {
      infoData.value = res.data
    }
  })
}
watch(
  () => props.data,
  (newVal, oldVal) => {
    getInfo(newVal.apiServerCode)
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.title {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #333333;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8px;
}
.content {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #52585f;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.tiaojianlist {
  // :nth-child(3n) {
  //   margin-right: 0;
  // }
}
.item {
  padding: 24px;
  padding-top: 28px;
  background: linear-gradient(180deg, #f2f5ff 0%, #ffffff 34%);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
  .title {
    position: relative;
    margin-left: 10px;
    line-height: 16px;
    margin-bottom: 9px;
    &:before {
      content: '';
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -10px;
      width: 3px;
      height: 16px;
      background-color: #3665ff;
    }
  }
}
</style>

<template>
  <div style="height: calc(100% - 50px)" class="overflow-hidden">
    <!-- <el-scrollbar> -->
      <!-- 示例代码{{data}} -->
      <div>
        <div class="df aic">
          <div class="mr-[10px]">代码类型</div>
          <el-select
            size="small"
            v-model="SetActiveType"
            placeholder="请选择代码类型"
            style="width: 120px"
          >
            <el-option
              v-for="item in data.sampleCodeVos || []"
              :key="item.id"
              :label="item.type"
              readonly
              :value="item.id"
            />
          </el-select>
        </div>
        <div class="title">请求示例</div>
        <div
          v-if="SetActiveType"
          style="width: calc(100vw + 500px); overflow: hidden"
        >
          <CodeEditor
            :needMenu="false"
            :readOnly="true"
            :htmlValue="
              formatCode(
                data.sampleCodeVos.find((item) => item.id == SetActiveType).code
              )
            "
          />
        </div>
        <div class="title">成功响应</div>
        <div style="width: calc(100vw + 500px); overflow: hidden">
          <CodeEditor
            :needMenu="false"
            :readOnly="true"
            :htmlValue="formatCode(data.returnType, 'json')"
          />
        </div>
        <div class="title">失败响应</div>
        <div style="width: calc(100vw + 500px); overflow: hidden">
          <CodeEditor
            :needMenu="false"
            :readOnly="true"
            :htmlValue="formatCode(data.errorReturnType, 'json')"
          />
        </div>
      </div>
    <!-- </el-scrollbar> -->
  </div>
</template>

<script setup>
import CodeEditor from '@/components/Editor/codeEditor.vue'
const props = defineProps({
  data: {
    type: Object,
    default: {}
  }
})

const SetActiveType = ref('')

watch(
  () => props.data,
  () => {
    SetActiveType.value = props.data.sampleCodeVos[0].id
  },
  { deep: true, immediate: true }
)

function formatCode(code, type = 'object') {
  return '<pre><code class="language-javascript">' + code + '</code></pre>'
}
</script>

<style lang="scss" scoped>
.title {
  font-weight: bold;
  margin-top: 10px;
}
</style>

<template>
  <div class="flex flex-col gap-y-[16px]">
    <div>
      <el-button
        type="primary"
        @click="
          () => {
            emit('infoCallback')
          }
        "
        >返回列表</el-button
      >
    </div>
    <div class="overflow-hidden">
      <div class="content-box-item">
        <div
          class="item-type"
          :style="{ backgroundColor: typeBgColor(info?.type) }"
        >
          {{ info?.type }}
        </div>
        <div class="w-full flex flex-row items-start w-400px">
          <svg-icon
            name="link"
            style="width: 24px; height: 24px; margin-right: 5px"
          ></svg-icon>
          <div class="text-[18px] font-bold">{{ info?.title }}</div>
        </div>
        <!-- <div class="item-text">资源分类：1/2/3</div> -->
        <div class="w-full flex flex flex-col justify-between">
          <div class="flex align-center mb-[32px]">
            <div class="item-text">资源提供方: {{ info.dataSource }}</div>
            <!-- <div class="item-text">算法授权总路数: {{ info.dataSource }}</div>
            <div class="item-text">使用路数: {{ info.dataSource }}</div> -->
          </div>
          <div class="flex align-center mb-[32px]">
            <div class="item-text">创建时间: {{ info.createTime }}</div>
            <!-- <div class="item-text">到期时间: {{ info.updateTime }}</div> -->
          </div>
        </div>
      </div>
    </div>
    <div
      class="px-[24px] py-[16px] box-border bg-white w-full overflow-hidden"
      style="height: calc(100vh - 160px)"
    >
      <el-tabs
        v-model="typeTab"
        class="demo-tabs w-full h-full"
        @tab-click="handleClick"
      >
        <el-tab-pane label="算法说明" name="describe" class="h-full">
          <el-scrollbar>
            <algorithmDetile :data="info.data" />
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="API详情" name="first" class="h-full">
          <el-scrollbar>
            <ApiDetails :data="info.data" />
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="错误参照码" name="second" class="h-full">
          <el-scrollbar>
            <ErrorCode :data="info.data" />
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="示例代码" name="third" class="h-full">
          <el-scrollbar>
            <TestCode :data="info.data" />
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TabsPaneContext } from 'element-plus'
import ApiDetails from './apiDetails.vue'
import ErrorCode from './errorCode.vue'
import TestCode from './testCode.vue'
import algorithmDetile from './algorithmDetile.vue'
defineProps<{
  info: any
}>()

const emit = defineEmits(['infoCallback'])
const typeTab = defineModel('typeTab', {
  type: String,
  default: 'describe'
})
const typeBgColor = computed(() => {
  return (type: string) => {
    switch (type) {
      case '数据服务':
        return '#FFB400'
      case '算法服务':
        return '#28a745'
      case '业务服务':
        return '#3665FF'
    }
  }
})

const activeName = ref('first')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
</script>

<style lang="scss" scoped>
.content-box-item {
  @apply relative w-full  border rounded flex p-[16px] box-border justify-between bg-white pr-[20%] cursor-pointer;
  .item-type {
    @apply absolute  right-0 top-0 py-[8px] px-[16px] box-border  text-white;
  }
  .item-text {
    @apply text-gray-400 mr-50px;
    font-size: 16px;
  }
}

.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

:deep(.el-tabs__item.is-active) {
  color: #3665ff;
}
:deep(.el-tabs__item:hover) {
  color: #3665ff;
}
:deep(.el-tabs__active-bar) {
  background: #3665ff;
}
:deep(.el-tabs__content) {
  height: 100% !important;
  overflow: hidden !important;
}
</style>

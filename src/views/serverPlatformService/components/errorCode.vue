<template>
  <div style="height: calc(100% - 50px)" class="overflow-hidden">
    <!-- <el-scrollbar> -->
      <el-table :data="data.errorCodeVos" style="width: 100%">
        <el-table-column label="错误码" prop="code" />
        <el-table-column label="错误说明" prop="desc" />
      </el-table>
    <!-- </el-scrollbar> -->
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: {}
  }
})
</script>

<style lang="scss" scoped></style>

<template>
  <div style="height: calc(100% - 50px)" class="overflow-hidden">
    <!-- <el-scrollbar> -->
      <div class="border-1 df aic p-[10px] border-radius-[5%]">
        <div class="f1">
          <div>调试地址：{{ data.url }}</div>
          <div>调用方式：{{ data.method }}</div>
          <div>返回格式：json,csv,xml,dbt</div>
          <div>请求示例：{{ data.headers }}</div>
        </div>
        <div class="mr-[50px]">
          <el-button @click="handleDebug" type="primary">调试</el-button>
        </div>
      </div>
      <div class="border-1 mt-[10px] p-[10px] border-radius-[5%]">
        <div class="title">输入参数</div>
        <el-table :data="paramsData" :border="true" style="width: 100%">
          <el-table-column label="英文名" prop="fieldName"></el-table-column>
          <el-table-column label="中文名" prop="fieldDesc"></el-table-column>
          <el-table-column label="数据类型" prop="fieldClazz"></el-table-column>
          <el-table-column label="是否必填" prop="requisite">
            <template #default="scope">{{
              scope.row.requisite ? '是' : '否'
            }}</template>
          </el-table-column>
          <el-table-column label="参数位置" prop="name"></el-table-column>
          <el-table-column label="示例值" prop=""></el-table-column>
          <el-table-column label="说明" prop="fieldDesc"></el-table-column>
        </el-table>
      </div>
      <div class="border-1 mt-[10px] p-[10px] border-radius-[5%]">
        <div class="title">输出参数</div>
        <el-table :data="returnParamsData" :border="true" style="width: 100%">
          <el-table-column label="英文名" prop="fieldName"></el-table-column>
          <el-table-column label="中文名" prop="fieldDesc"></el-table-column>
          <el-table-column label="数据类型" prop="fieldClazz"></el-table-column>
          <el-table-column label="是否必填" prop="requisite">
            <template #default="scope">{{
              scope.row.requisite ? '是' : '否'
            }}</template>
          </el-table-column>
          <el-table-column label="参数位置" prop=""></el-table-column>
          <el-table-column label="示例值" prop=""></el-table-column>
          <el-table-column label="说明" prop="fieldDesc"></el-table-column>
        </el-table>
      </div>
    <!-- </el-scrollbar> -->
  </div>
  <dialog-view
    :needOkBtn="false"
    ref="dialogRef"
    title="服务调试"
    cancelBtnText="关闭"
  >
    <div class="df fdc aic">
      <el-form :data="formData" ref="formDataRef">
        <el-form-item label="调用方式">
          <el-radio-group v-model="data.method">
            <el-radio label="POST">POST</el-radio>
            <el-radio label="GET">GET</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="输入参数">
          <div>
            <div v-for="item in paramsData" class="df aic h-[40px]">
              <span class="text-[red]">{{
                item.requisite ? '*' : '&nbsp;'
              }}</span>
              <div class="w-[200px]">{{ item.fieldName }}</div>
              <el-input
                v-model="formData[item.name][item.fieldName]"
                :placeholder="item.fieldDesc"
              ></el-input>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="df w-full mb-[10px]">
        <div class="f1"></div>
        <el-button class="mr-[40px]" type="primary" @click="testClick(data.url)"
          >执行</el-button
        >
      </div>
      <div class="w-full h-[360px]">
        <code-editor
          height="359.56px"
          width="100%"
          placeholder=""
          style="margin-top: 0; width: 100%; border: 1px solid #e2e2e2"
          v-model:htmlValue="response.response"
          :bord="false"
          :readOnly="true"
          :needMenu="false"
        ></code-editor>
      </div>
    </div>
  </dialog-view>
</template>

<script setup>
import dialogView from '@/components/DialogView/index.vue'
import CodeEditor from '@/components/Editor/codeEditor.vue'
import { $debugApi } from '@/api/videoCenter/index.ts'
import { ref, computed } from 'vue'
const props = defineProps({
  data: {
    type: Object,
    default: {}
  }
})

const dialogRef = ref(null)
const formData = ref({
  body: {},
  header: {}
})
console.log('data', props.data)

const paramsData = computed(() => {
  let header = JSON.parse(props.data.headerParamDoc || '[]')
  let body = JSON.parse(props.data.bodyParamDoc || '[]')
  if (header.childFieldDescDtos) {
    header = header.childFieldDescDtos.map((item) => {
      return { ...item, name: 'header' }
    })
  }
  if (body.childFieldDescDtos) {
    body = body.childFieldDescDtos.map((item) => {
      return { ...item, name: 'body' }
    })
  }
  // console.log('请求头', body);
  // let tempBody = flattenObject(body.childFieldDescDtos)
  // console.log('对象拍扁',tempBody);
  return [...header, ...body]
})

const returnParamsData = computed(() => {
  let returnData = JSON.parse(props.data.returnTypeDoc || '[]')
  if (returnData.childFieldDescDtos) {
    returnData = returnData.childFieldDescDtos.map((item) => {
      return { ...item, name: '返回值' }
    })
  }
  console.log('返回值', returnData)
  let temp = flattenObject(returnData)
  console.log('对象拍扁', temp)
  return temp
})

//调试
function handleDebug() {
  dialogRef.value.open()
}

//对象拍扁
const flattenObject = (obj, data = [], prefix = '') => {
  // return obj.map((item)=>{
  //     if(item.childFieldDescDtos){
  //         let pre = prefix ? prefix + '.':''
  //         flattenObject(item.childFieldDescDtos, pre+item.fieldName)
  //     }
  //     if(prefix){
  //         item.fieldName = prefix + '.' + item.fieldName
  //     }
  //     return item
  // });
  for (let index = 0; index < obj.length; index++) {
    let item = obj[index]
    if (item.childFieldDescDtos) {
      let pre = prefix ? prefix + '.' : ''
      if (item.fieldName) {
        pre = pre + item.fieldName
      }
      let temp = flattenObject(item.childFieldDescDtos, data, pre)
      data = [...data, ...temp]
    }
    if (item.fieldName !== undefined) {
      if (prefix) {
        item.fieldName = prefix + item.fieldName
      }
      data.push(item)
    }
  }
  return data
}

const response = ref({})

//调试
function testClick(url) {
  let zhongjian = '/datacenter'
  if (props.data.sourceCode == 'citybrain-aicenter-biz') {
    zhongjian = '/aicenter'
  }
  let testUrl = `${zhongjian}${url}`
  // let header = {}
  // // testForm.value.inputParam.forEach((item) => {
  // //     if (item.value && item.value !== undefined) {
  // //         header[item.columnName] = item.value;
  // //     }
  // // });
  // console.log("发起请求的内容", header);
  $debugApi(
    testUrl,
    props.data.method,
    formData.value.header || null,
    formData.value.body || null
  )
    .then((res) => {
      response.value = {
        response: `<pre><code class="language-javascript">${JSON.stringify(
          res.data,
          null,
          2
        )}</code></pre>`
        // time:
        //   res.config.transformRequest.length +
        //   res.config.transformResponse.length,
      }
      console.log('调试结果', response.value)
    })
    .catch((err) => {
      response.value = {
        response: `<pre><code class="language-javascript">${JSON.stringify(
          err,
          null,
          2
        )}</code></pre>`
      }
    })
}
</script>

<style lang="scss" scoped>
.title {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: bold;
}
</style>

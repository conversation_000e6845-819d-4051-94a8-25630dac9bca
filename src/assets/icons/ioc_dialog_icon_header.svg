<svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#188;&#150;&#231;&#187;&#132; 24&#229;&#164;&#135;&#228;&#187;&#189; 4">
<path id="&#229;&#189;&#162;&#231;&#138;&#182;&#229;&#164;&#135;&#228;&#187;&#189; 9" fill-rule="evenodd" clip-rule="evenodd" d="M1.98597 13.451C3.86766 7.56361 8.3664 2.9426 14.098 1.00977L15.6769 5.98624C11.4817 7.38512 8.19265 10.7635 6.83076 15.0728L1.98597 13.451ZM19.9818 29.1794C25.1943 29.1794 29.4119 24.8471 29.4119 19.493C29.4119 14.1388 25.1943 9.80664 19.9818 9.80664C14.7693 9.80664 10.5518 14.1388 10.5518 19.493C10.5518 24.8471 14.7693 29.1794 19.9818 29.1794ZM25.8636 1.00977L24.2847 5.98624C28.459 7.40809 31.7465 10.7628 33.1308 15.0728L37.9756 13.451C36.0939 7.56361 31.5951 2.9426 25.8636 1.00977ZM1.96484 25.5573L6.80964 23.9355C8.19387 28.2455 11.4814 31.6002 15.6557 33.0221L14.0769 37.9985C8.34528 36.0657 3.84653 31.4447 1.96484 25.5573ZM33.1538 23.9355C31.7696 28.2233 28.5037 31.6002 24.3077 33.0221L25.8866 37.9985C31.6182 36.0435 36.0953 31.4447 37.9986 25.5573L33.1538 23.9355Z" fill="#1C61B7"/>
<g id="&#229;&#189;&#162;&#231;&#138;&#182;">
<path id="&#229;&#189;&#162;&#231;&#138;&#182;_2" fill-rule="evenodd" clip-rule="evenodd" d="M0.0211216 12.4412C1.90281 6.55384 6.40156 1.93283 12.1331 0L13.712 4.97647C9.51682 6.37536 6.22781 9.75374 4.86592 14.063L0.0211216 12.4412ZM18.017 28.1696C23.2295 28.1696 27.447 23.8374 27.447 18.4832C27.447 13.1291 23.2295 8.79688 18.017 8.79688C12.8045 8.79688 8.58691 13.1291 8.58691 18.4832C8.58691 23.8374 12.8045 28.1696 18.017 28.1696ZM23.8987 0L22.3198 4.97647C26.4941 6.39832 29.7817 9.75301 31.1659 14.063L36.0107 12.4412C34.129 6.55384 29.6303 1.93283 23.8987 0ZM0 24.5476L4.8448 22.9258C6.22903 27.2358 9.51659 30.5904 13.6909 32.0123L12.112 36.9888C6.38044 35.0559 1.88169 30.4349 0 24.5476ZM31.189 22.9258C29.8047 27.2135 26.5388 30.5904 22.3429 32.0123L23.9218 36.9888C29.6533 35.0337 34.1304 30.4349 36.0338 24.5476L31.189 22.9258Z" fill="white"/>
<path id="&#229;&#189;&#162;&#231;&#138;&#182;_3" fill-rule="evenodd" clip-rule="evenodd" d="M0.0211216 12.4412C1.90281 6.55384 6.40156 1.93283 12.1331 0L13.712 4.97647C9.51682 6.37536 6.22781 9.75374 4.86592 14.063L0.0211216 12.4412ZM18.017 28.1696C23.2295 28.1696 27.447 23.8374 27.447 18.4832C27.447 13.1291 23.2295 8.79688 18.017 8.79688C12.8045 8.79688 8.58691 13.1291 8.58691 18.4832C8.58691 23.8374 12.8045 28.1696 18.017 28.1696ZM23.8987 0L22.3198 4.97647C26.4941 6.39832 29.7817 9.75301 31.1659 14.063L36.0107 12.4412C34.129 6.55384 29.6303 1.93283 23.8987 0ZM0 24.5476L4.8448 22.9258C6.22903 27.2358 9.51659 30.5904 13.6909 32.0123L12.112 36.9888C6.38044 35.0559 1.88169 30.4349 0 24.5476ZM31.189 22.9258C29.8047 27.2135 26.5388 30.5904 22.3429 32.0123L23.9218 36.9888C29.6533 35.0337 34.1304 30.4349 36.0338 24.5476L31.189 22.9258Z" fill="url(#paint0_radial_3528_19828)"/>
<g id="&#229;&#189;&#162;&#231;&#138;&#182;_4" filter="url(#filter0_i_3528_19828)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.0211216 12.4412C1.90281 6.55384 6.40156 1.93283 12.1331 0L13.712 4.97647C9.51682 6.37536 6.22781 9.75374 4.86592 14.063L0.0211216 12.4412ZM18.017 28.1696C23.2295 28.1696 27.447 23.8374 27.447 18.4832C27.447 13.1291 23.2295 8.79688 18.017 8.79688C12.8045 8.79688 8.58691 13.1291 8.58691 18.4832C8.58691 23.8374 12.8045 28.1696 18.017 28.1696ZM23.8987 0L22.3198 4.97647C26.4941 6.39832 29.7817 9.75301 31.1659 14.063L36.0107 12.4412C34.129 6.55384 29.6303 1.93283 23.8987 0ZM0 24.5476L4.8448 22.9258C6.22903 27.2358 9.51659 30.5904 13.6909 32.0123L12.112 36.9888C6.38044 35.0559 1.88169 30.4349 0 24.5476ZM31.189 22.9258C29.8047 27.2135 26.5388 30.5904 22.3429 32.0123L23.9218 36.9888C29.6533 35.0337 34.1304 30.4349 36.0338 24.5476L31.189 22.9258Z" fill="url(#paint1_linear_3528_19828)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_i_3528_19828" x="0" y="0" width="37.0332" height="37.9883" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.215686 0 0 0 0 0.211765 0 0 0 0.843137 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3528_19828"/>
</filter>
<radialGradient id="paint0_radial_3528_19828" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(22.8017 14.2609) scale(20.8628 21.4028)">
<stop stop-color="#0085FF"/>
<stop offset="1" stop-color="#00A3FF" stop-opacity="0.01"/>
</radialGradient>
<linearGradient id="paint1_linear_3528_19828" x1="26.2321" y1="0.851722" x2="18.9863" y2="8.67352" gradientUnits="userSpaceOnUse">
<stop stop-color="#004D6E" stop-opacity="0.545098"/>
<stop offset="1" stop-color="#001F44" stop-opacity="0.01"/>
</linearGradient>
</defs>
</svg>

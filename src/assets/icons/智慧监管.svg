<svg width="89" height="84" viewBox="0 0 89 84" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1142819472">
<g id="Group 1142818466">
<g id="&#230;&#153;&#186;&#230;&#133;&#167;&#231;&#155;&#145;&#231;&#174;&#161;" filter="url(#filter0_d_3259_23149)">
<path d="M7.80098 64.7H9.70098C9.72098 64.54 9.72098 64.4 9.72098 64.26V63.4H8.72098C8.44098 63.88 8.12098 64.32 7.80098 64.7ZM15.101 68.48L13.421 70C12.861 69.5 11.741 68.68 10.841 68.06C10.121 69.08 8.96098 70.1 7.16098 70.94C6.84098 70.46 6.18098 69.72 5.66098 69.34C7.70098 68.54 8.72098 67.56 9.22098 66.56H5.90098V64.7H7.24098C6.76098 64.4 6.12098 64.04 5.72098 63.84C6.74098 62.88 7.52098 61.42 7.92098 59.94L10.021 60.38C9.90098 60.76 9.78098 61.16 9.62098 61.56H14.581V63.4H11.981V64.3C11.981 64.44 11.981 64.56 11.961 64.7H15.041V66.56H11.741C12.581 67 14.581 68.14 15.101 68.48ZM19.161 72.16H10.901V73.28H19.161V72.16ZM10.901 76.18H19.161V75H10.901V76.18ZM8.56098 78.74V70.3H21.601V78.72H19.161V78.08H10.901V78.74H8.56098ZM20.961 67V63.62H17.921V67H20.961ZM15.721 61.52H23.321V69.1H15.721V61.52ZM37.981 67.12V66.34H35.021V64.94H37.981V64.28H35.581V62.9H37.981V62.28H35.181V60.9H37.981V60.06H40.201V60.9H43.241V62.28H40.201V62.9H42.921V64.28H40.201V64.94H43.621V66.34H40.201V67.12H37.981ZM42.121 67.4V73.2H35.661C36.361 73.66 37.101 74.24 37.481 74.7L35.981 76.04C35.541 75.44 34.501 74.6 33.641 74.08L34.641 73.2H27.661V71.7H39.781V70.96H28.461V69.58H39.781V68.88H27.781V67.4H42.121ZM29.441 67.12V66.34H26.241V64.94H29.441V64.28H26.981V62.9H29.441V62.28H26.501V60.9H29.441V60.06H31.621V60.9H34.261V62.28H31.621V62.9H34.041V64.28H31.621V64.94H34.461V66.34H31.621V67.12H29.441ZM28.141 78.3L26.061 77.14C26.781 76.28 27.421 74.98 27.761 73.82L29.821 74.7C29.401 75.88 28.861 77.34 28.141 78.3ZM34.081 76.5H37.421C38.281 76.5 38.441 76.28 38.541 74.8C39.041 75.14 39.961 75.42 40.601 75.54C40.321 77.84 39.681 78.44 37.621 78.44H33.841C31.101 78.44 30.401 77.84 30.401 75.92V73.78H32.761V75.88C32.761 76.42 32.961 76.5 34.081 76.5ZM40.321 74.22L42.521 73.64C43.301 74.86 44.121 76.48 44.421 77.58L42.061 78.24C41.801 77.18 41.061 75.48 40.321 74.22ZM63.941 64.88H57.721C57.061 66.36 56.321 67.7 55.461 68.72C55.061 68.32 54.081 67.62 53.541 67.3C55.101 65.64 56.261 62.9 56.881 60.08L59.161 60.54C58.981 61.28 58.781 62 58.561 62.72H63.941V64.88ZM57.661 66.62L59.481 65.4C60.721 66.32 62.241 67.7 62.981 68.64L61.041 70.02C60.381 69.08 58.881 67.64 57.661 66.62ZM53.481 60.08V69.78H51.121V60.08H53.481ZM49.481 60.74V69.24H47.181V60.74H49.481ZM50.181 72.64V76.16H51.941V72.64H50.181ZM55.921 72.64H54.121V76.16H55.921V72.64ZM59.881 72.64H58.081V76.16H59.881V72.64ZM62.221 76.16H64.121V78.28H45.921V76.16H47.961V70.64H62.221V76.16ZM70.101 63.14H69.301C68.801 64.04 68.241 64.82 67.681 65.44C67.201 65.14 66.181 64.62 65.621 64.38C66.741 63.3 67.721 61.6 68.241 59.92L70.581 60.36C70.441 60.72 70.301 61.1 70.121 61.48H74.901V63.14H72.441C72.801 63.66 73.101 64.16 73.261 64.54L71.241 65.28C71.061 64.72 70.561 63.88 70.101 63.14ZM79.781 75.34H71.341V76.46H79.781V75.34ZM78.781 69.94H71.341V70.98H78.781V69.94ZM71.341 72.68V73.6H82.161V78.76H79.781V78.22H71.341V78.76H68.921V68.22H81.101V72.68H71.341ZM73.441 64.5L75.561 64.08C75.861 64.52 76.161 65.12 76.361 65.62H83.601V69.1H81.161V67.4H68.821V69.1H66.521V65.62H73.961C73.821 65.24 73.641 64.82 73.441 64.5ZM78.681 61.5H84.001V63.14H81.241C81.601 63.62 81.941 64.08 82.141 64.44L80.141 65.34C79.921 64.74 79.301 63.88 78.701 63.14H77.881C77.541 63.74 77.161 64.28 76.761 64.7C76.321 64.4 75.341 63.92 74.801 63.68C75.721 62.76 76.421 61.32 76.801 59.92L79.101 60.34C78.981 60.72 78.841 61.12 78.681 61.5Z" fill="white"/>
</g>
</g>
<g id="Frame">
<path id="Vector" d="M58.6286 8.23695C58.9297 8.23695 59.2186 8.3566 59.4316 8.56957C59.6445 8.78254 59.7642 9.07139 59.7642 9.37258V36.6276C59.7642 36.9288 59.6445 37.2176 59.4316 37.4306C59.2186 37.6436 58.9297 37.7632 58.6286 37.7632H31.3736C31.0724 37.7632 30.7835 37.6436 30.5705 37.4306C30.3576 37.2176 30.2379 36.9288 30.2379 36.6276V9.37258C30.2379 9.07139 30.3576 8.78254 30.5705 8.56957C30.7835 8.3566 31.0724 8.23695 31.3736 8.23695H58.6286ZM58.6286 4.83008H31.3736C30.1688 4.83008 29.0134 5.30866 28.1615 6.16055C27.3096 7.01243 26.8311 8.16783 26.8311 9.37258V36.6276C26.8311 37.8323 27.3096 38.9877 28.1615 39.8396C29.0134 40.6915 30.1688 41.1701 31.3736 41.1701H58.6286C59.8333 41.1701 60.9887 40.6915 61.8406 39.8396C62.6925 38.9877 63.1711 37.8323 63.1711 36.6276V9.37258C63.1711 8.16783 62.6925 7.01243 61.8406 6.16055C60.9887 5.30866 59.8333 4.83008 58.6286 4.83008Z" fill="white"/>
<path id="Vector_2" d="M36.6059 33.1198C36.1484 33.1198 35.7096 32.9405 35.3861 32.6215C35.0626 32.3025 34.8809 31.8698 34.8809 31.4186V15.9609C34.8809 15.5097 35.0626 15.077 35.3861 14.758C35.7096 14.439 36.1484 14.2598 36.6059 14.2598C37.0634 14.2598 37.5021 14.439 37.8256 14.758C38.1491 15.077 38.3309 15.5097 38.3309 15.9609V31.4209C38.3302 31.8717 38.1482 32.3038 37.8248 32.6223C37.5014 32.9409 37.063 33.1198 36.6059 33.1198Z" fill="white"/>
<path id="Vector_3" d="M45.0006 33.1208C44.5736 33.1208 44.1641 32.9421 43.8622 32.624C43.5602 32.3059 43.3906 31.8744 43.3906 31.4245V21.247C43.3906 20.7972 43.5602 20.3657 43.8622 20.0476C44.1641 19.7295 44.5736 19.5508 45.0006 19.5508C45.4276 19.5508 45.8371 19.7295 46.1391 20.0476C46.441 20.3657 46.6106 20.7972 46.6106 21.247V31.4245C46.6106 31.8744 46.441 32.3059 46.1391 32.624C45.8371 32.9421 45.4276 33.1208 45.0006 33.1208Z" fill="white"/>
<path id="Vector_4" d="M53.3959 33.1191C52.9384 33.1191 52.4996 32.9415 52.1761 32.6254C51.8526 32.3093 51.6709 31.8806 51.6709 31.4336V24.2245C51.6709 23.7775 51.8526 23.3488 52.1761 23.0327C52.4996 22.7166 52.9384 22.5391 53.3959 22.5391C53.8534 22.5391 54.2922 22.7166 54.6157 23.0327C54.9392 23.3488 55.1209 23.7775 55.1209 24.2245V31.4359C55.1203 31.8825 54.9383 32.3106 54.6148 32.6262C54.2914 32.9418 53.853 33.1191 53.3959 33.1191Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_3259_23149" x="0.861133" y="55.1199" width="87.9398" height="28.4398" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3259_23149"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3259_23149" result="shape"/>
</filter>
</defs>
</svg>

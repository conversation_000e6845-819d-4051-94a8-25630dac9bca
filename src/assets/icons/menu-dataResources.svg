<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_304_16418)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_304_16418)"/>
<g id="Vector" filter="url(#filter0_d_304_16418)">
<path d="M15.1886 13.1204V18.7334C15.1886 18.9164 15.2544 19.0919 15.3716 19.2213C15.4887 19.3507 15.6476 19.4234 15.8133 19.4234C15.979 19.4234 16.1379 19.3507 16.2551 19.2213C16.3722 19.0919 16.438 18.9164 16.438 18.7334V13.1463C16.9934 12.9907 17.4787 12.6178 17.804 12.0969C18.1292 11.5759 18.2723 10.9424 18.2066 10.3138C18.141 9.68514 17.871 9.1042 17.4469 8.67877C17.0228 8.25334 16.4733 8.01232 15.9004 8.00046C15.3276 7.9886 14.7703 8.2067 14.3321 8.61427C13.8938 9.02184 13.6043 9.5912 13.5174 10.2166C13.4304 10.8421 13.5519 11.4812 13.8592 12.0152C14.1666 12.5492 14.6389 12.9419 15.1886 13.1204ZM15.8586 9.37558C16.0801 9.37558 16.2966 9.44812 16.4807 9.58402C16.6649 9.71992 16.8084 9.91308 16.8932 10.1391C16.9779 10.3651 17.0001 10.6137 16.9569 10.8537C16.9137 11.0936 16.807 11.3139 16.6504 11.4869C16.4938 11.6599 16.2943 11.7777 16.0771 11.8254C15.8598 11.8731 15.6347 11.8486 15.4301 11.755C15.2255 11.6614 15.0506 11.5029 14.9275 11.2995C14.8045 11.0961 14.7388 10.857 14.7388 10.6124C14.7388 10.4496 14.7678 10.2884 14.8243 10.138C14.8808 9.98764 14.9635 9.85103 15.0678 9.736C15.1721 9.62097 15.2959 9.52978 15.4321 9.46764C15.5683 9.4055 15.7143 9.37363 15.8617 9.37386L15.8586 9.37558ZM10.4048 13.9036C10.6401 13.9033 10.8738 13.8608 11.0967 13.7776C11.9416 14.4021 12.4773 17.0102 12.5944 18.7886C12.6059 18.9628 12.6768 19.1257 12.7927 19.2445C12.9086 19.3632 13.061 19.429 13.2192 19.4286H13.2645C13.3463 19.422 13.4262 19.3977 13.4995 19.3571C13.5728 19.3164 13.6382 19.2602 13.6919 19.1916C13.7455 19.1231 13.7864 19.0435 13.8122 18.9575C13.8381 18.8715 13.8483 18.7807 13.8423 18.6903C13.783 17.9089 13.4909 14.4469 12.1368 12.9514C12.4338 12.5189 12.5897 11.9882 12.5791 11.4456C12.5686 10.9029 12.3922 10.3801 12.0786 9.96209C11.7651 9.54411 11.3328 9.25548 10.852 9.14311C10.3712 9.03073 9.87008 9.1012 9.4301 9.34307C8.99013 9.58495 8.63712 9.98401 8.42842 10.4754C8.21972 10.9669 8.16759 11.5218 8.2805 12.0501C8.39341 12.5784 8.66472 13.049 9.05037 13.3855C9.43601 13.7219 9.91334 13.9045 10.4048 13.9036ZM10.4048 10.4658C10.5901 10.4658 10.7713 10.5264 10.9254 10.6402C11.0795 10.7539 11.1996 10.9155 11.2705 11.1047C11.3415 11.2938 11.36 11.5019 11.3239 11.7026C11.2877 11.9034 11.1985 12.0878 11.0674 12.2326C10.9364 12.3773 10.7694 12.4759 10.5876 12.5158C10.4058 12.5557 10.2174 12.5352 10.0462 12.4569C9.87497 12.3786 9.72862 12.2459 9.62565 12.0757C9.52268 11.9055 9.46772 11.7054 9.46772 11.5007C9.46752 11.3647 9.4916 11.2299 9.5386 11.1041C9.5856 10.9784 9.6546 10.8641 9.74163 10.7678C9.82867 10.6715 9.93204 10.5951 10.0458 10.543C10.1596 10.4909 10.2816 10.464 10.4048 10.464V10.4658Z" fill="white"/>
<path d="M25.9217 22.9476L22.6662 16.3787C22.5813 16.2145 22.4397 16.0927 22.2719 16.0395C22.1042 15.9862 21.9236 16.0059 21.7692 16.0941C21.6148 16.1824 21.4988 16.3322 21.4464 16.5113C21.3939 16.6904 21.4091 16.8845 21.4887 17.0517L24.2222 22.5681H7.76426L10.4328 17.0463C10.5144 16.8778 10.5303 16.6814 10.4769 16.5003C10.4236 16.3192 10.3054 16.1683 10.1484 16.0807C9.99137 15.9931 9.80836 15.9761 9.63962 16.0333C9.47087 16.0905 9.33023 16.2173 9.24862 16.3859L6.07477 22.9547C6.0221 23.0638 5.99648 23.1856 6.00039 23.3085C6.00429 23.4313 6.03758 23.5509 6.09706 23.6559C6.15654 23.7609 6.2402 23.8478 6.33999 23.908C6.43978 23.9683 6.55234 23.9999 6.66685 24H25.3347C25.4497 23.9997 25.5626 23.9674 25.6626 23.9064C25.7626 23.8454 25.8462 23.7577 25.9053 23.6518C25.9643 23.5459 25.9969 23.4254 25.9998 23.302C26.0027 23.1786 25.9758 23.0565 25.9217 22.9476Z" fill="white"/>
<path d="M18.2233 18.7052C18.2135 18.8863 18.2722 19.064 18.3867 19.1996C18.5011 19.3352 18.662 19.4175 18.834 19.4286H18.8747C19.0406 19.4289 19.2004 19.3626 19.3214 19.2431C19.4425 19.1237 19.5157 18.9601 19.5261 18.7857C19.6466 16.6858 20.2296 14.2551 20.8809 13.962C20.9258 13.9419 20.9684 13.9166 21.008 13.8866C21.5379 14.0093 22.0921 13.9291 22.5709 13.6602C23.0497 13.3914 23.4217 12.9516 23.62 12.4199C23.8184 11.8882 23.83 11.2995 23.6529 10.7595C23.4758 10.2196 23.1216 9.76382 22.6539 9.4742C22.1861 9.18458 21.6356 9.08008 21.1012 9.17947C20.5668 9.27887 20.0837 9.57564 19.7386 10.0165C19.3934 10.4573 19.209 11.0132 19.2184 11.5843C19.2278 12.1555 19.4304 12.7043 19.7899 13.1323C18.5343 14.4934 18.2672 17.9441 18.2233 18.7052ZM21.4965 10.525C21.6897 10.525 21.8787 10.5853 22.0393 10.6983C22.2 10.8114 22.3253 10.972 22.3992 11.1599C22.4732 11.3479 22.4925 11.5547 22.4548 11.7542C22.4171 11.9537 22.324 12.137 22.1874 12.2808C22.0508 12.4246 21.8767 12.5226 21.6871 12.5623C21.4976 12.602 21.3011 12.5816 21.1226 12.5038C20.944 12.4259 20.7914 12.2941 20.6841 12.1249C20.5767 11.9558 20.5194 11.7569 20.5194 11.5535C20.5194 11.2807 20.6224 11.0191 20.8056 10.8262C20.9888 10.6334 21.2374 10.525 21.4965 10.525Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_304_16418" x="-1.4" y="0.6" width="34.8" height="30.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_304_16418"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_304_16418" result="shape"/>
</filter>
<linearGradient id="paint0_linear_304_16418" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_304_16418">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

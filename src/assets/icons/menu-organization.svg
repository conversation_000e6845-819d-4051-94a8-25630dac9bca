<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_304_16494)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_304_16494)"/>
<g id="Vector" filter="url(#filter0_d_304_16494)">
<path d="M18.932 8.31242C18.8356 8.21336 18.7212 8.13479 18.5952 8.08118C18.4693 8.02758 18.3343 7.99999 18.198 8H13.494C13.2187 8 12.9547 8.11239 12.76 8.31244C12.5654 8.51249 12.456 8.78382 12.456 9.06674V12.8352C12.4557 12.9755 12.4824 13.1144 12.5344 13.2441C12.5865 13.3738 12.6629 13.4916 12.7593 13.5909C12.8557 13.6901 12.9702 13.7689 13.0963 13.8226C13.2224 13.8764 13.3575 13.904 13.494 13.904H15.35V16.0406H9.938L9.878 16.0437C9.75142 16.0588 9.63464 16.1211 9.54985 16.2189C9.46505 16.3166 9.41813 16.443 9.418 16.574V18.6704H9.038C8.76322 18.6704 8.49966 18.7824 8.30508 18.9818C8.1105 19.1812 8.00079 19.4517 8 19.7341V22.9333C8 23.0733 8.02685 23.2121 8.07901 23.3415C8.13118 23.4709 8.20764 23.5885 8.30402 23.6876C8.40041 23.7866 8.51484 23.8652 8.64077 23.9188C8.76671 23.9724 8.90169 24 9.038 24H11.358C11.4944 24.0001 11.6295 23.9726 11.7555 23.9191C11.8816 23.8655 11.9961 23.787 12.0926 23.6879C12.1891 23.5889 12.2657 23.4712 12.3179 23.3417C12.3701 23.2122 12.397 23.0734 12.397 22.9333V19.7341C12.397 19.4512 12.2876 19.1798 12.093 18.9798C11.8983 18.7797 11.6343 18.6674 11.359 18.6674H10.456V17.1053H15.351V18.6674H14.84C14.5648 18.6676 14.3009 18.7801 14.1063 18.9801C13.9117 19.1801 13.8023 19.4513 13.802 19.7341V22.9333C13.802 23.0733 13.8288 23.2121 13.881 23.3415C13.9332 23.4709 14.0096 23.5885 14.106 23.6876C14.2024 23.7866 14.3168 23.8652 14.4428 23.9188C14.5687 23.9724 14.7037 24 14.84 24H17.16C17.2963 24 17.4313 23.9724 17.5572 23.9188C17.6832 23.8652 17.7976 23.7866 17.894 23.6876C17.9904 23.5885 18.0668 23.4709 18.119 23.3415C18.1712 23.2121 18.198 23.0733 18.198 22.9333V19.7341C18.1981 19.594 18.1714 19.4552 18.1193 19.3257C18.0671 19.1962 17.9907 19.0786 17.8943 18.9795C17.7979 18.8804 17.6834 18.8018 17.5574 18.7483C17.4314 18.6947 17.2963 18.6672 17.16 18.6674H16.389V17.1053H21.282V18.6674H20.642C20.5056 18.6672 20.3705 18.6947 20.2445 18.7483C20.1184 18.8018 20.0039 18.8804 19.9074 18.9794C19.8109 19.0785 19.7343 19.1961 19.6821 19.3256C19.6299 19.4551 19.603 19.5939 19.603 19.7341V22.9333C19.6029 23.0734 19.6296 23.2122 19.6817 23.3416C19.7339 23.4711 19.8103 23.5888 19.9067 23.6879C20.0031 23.7869 20.1176 23.8655 20.2436 23.9191C20.3696 23.9726 20.5047 24.0001 20.641 24H22.962C23.2371 24 23.501 23.8878 23.6956 23.6879C23.8903 23.4881 23.9997 23.217 24 22.9343V19.7351C24.0001 19.595 23.9734 19.4562 23.9213 19.3267C23.8691 19.1972 23.7927 19.0796 23.6963 18.9805C23.5999 18.8814 23.4854 18.8029 23.3594 18.7493C23.2334 18.6957 23.0983 18.6682 22.962 18.6684H22.32V16.5719L22.317 16.5102C22.3021 16.3806 22.2414 16.2611 22.1465 16.1744C22.0516 16.0876 21.929 16.0397 21.802 16.0396H16.389V13.903H18.199C18.4741 13.9027 18.7379 13.7902 18.9323 13.5902C19.1268 13.3902 19.236 13.119 19.236 12.8363V9.06674C19.236 8.78309 19.126 8.51179 18.932 8.31242ZM13.494 9.06674H18.198V12.8352H13.494V9.06674ZM9.038 19.7341H11.358V22.9343H9.038V19.7351V19.7341ZM14.84 19.7341H17.16V22.9343H14.84V19.7351V19.7341ZM20.641 19.7341H22.962V22.9343H20.642V19.7351L20.641 19.7341ZM17.21 10.5086C17.113 10.4153 16.9849 10.3638 16.852 10.3647H14.707L14.647 10.3678C14.5145 10.3824 14.3926 10.4489 14.3065 10.5533C14.2204 10.6578 14.1767 10.7923 14.1844 10.929C14.1921 11.0656 14.2507 11.194 14.348 11.2876C14.4452 11.3811 14.5738 11.4326 14.707 11.4314H16.85L16.911 11.4273C17.0095 11.4161 17.1028 11.376 17.1799 11.312C17.257 11.2479 17.3146 11.1625 17.3459 11.0659C17.3773 10.9693 17.381 10.8654 17.3568 10.7667C17.3325 10.6679 17.2812 10.5783 17.209 10.5086H17.21Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_304_16494" x="0.6" y="0.6" width="30.8" height="30.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_304_16494"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_304_16494" result="shape"/>
</filter>
<linearGradient id="paint0_linear_304_16494" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_304_16494">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

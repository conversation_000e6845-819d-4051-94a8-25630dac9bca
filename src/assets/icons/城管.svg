<svg width="49" height="84" viewBox="0 0 49 84" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1142819471">
<g id="Group 1142818466">
<g id="&#229;&#159;&#142;&#231;&#174;&#161;" filter="url(#filter0_d_3268_20803)">
<path d="M15.801 69.06H14.221V69.46C14.221 70.7 14.161 72.14 13.921 73.56C14.421 73.62 14.801 73.62 15.021 73.62C15.241 73.62 15.381 73.56 15.521 73.38C15.701 73.08 15.761 72.02 15.801 69.06ZM21.941 66.96L24.081 67.36C23.441 69.98 22.561 72.3 21.341 74.22C21.661 75.54 22.021 76.3 22.401 76.3C22.701 76.3 22.861 75.36 22.921 73.32C23.361 73.82 24.041 74.34 24.581 74.56C24.281 77.64 23.661 78.5 22.121 78.5C21.141 78.5 20.381 77.66 19.801 76.28C18.981 77.24 18.041 78.06 17.001 78.76C16.681 78.36 15.841 77.52 15.341 77.18C16.821 76.3 18.041 75.18 19.061 73.8C18.541 71.38 18.261 68.26 18.121 64.98H14.221V67.14H17.781C17.781 67.14 17.781 67.68 17.761 67.94C17.701 72.4 17.621 74.18 17.221 74.72C16.901 75.16 16.601 75.34 16.121 75.42C15.721 75.5 15.081 75.52 14.401 75.5C14.341 74.9 14.181 74.12 13.901 73.62C13.581 75.54 12.941 77.42 11.661 78.8C11.341 78.4 10.441 77.64 9.94098 77.34C10.741 76.44 11.261 75.34 11.541 74.22C9.78098 74.94 7.90098 75.72 6.38098 76.34L5.62098 73.96C6.22098 73.8 6.92098 73.56 7.70098 73.3V67H5.96098V64.8H7.70098V60.32H9.90098V64.8H11.461V67H9.90098V72.52L11.441 71.98L11.721 73.44C12.001 72.1 12.041 70.68 12.041 69.46V62.82H18.061C18.041 61.88 18.021 60.96 18.021 60.04H20.261C20.241 60.98 20.241 61.9 20.261 62.82H21.821C21.421 62.2 20.841 61.48 20.301 60.92L21.921 59.96C22.661 60.66 23.501 61.64 23.861 62.3L23.021 62.82H24.121V64.98H20.301C20.401 67.08 20.541 69.06 20.761 70.76C21.261 69.6 21.641 68.32 21.941 66.96ZM30.101 63.14H29.301C28.801 64.04 28.241 64.82 27.681 65.44C27.201 65.14 26.181 64.62 25.621 64.38C26.741 63.3 27.721 61.6 28.241 59.92L30.581 60.36C30.441 60.72 30.301 61.1 30.121 61.48H34.901V63.14H32.441C32.801 63.66 33.101 64.16 33.261 64.54L31.241 65.28C31.061 64.72 30.561 63.88 30.101 63.14ZM39.781 75.34H31.341V76.46H39.781V75.34ZM38.781 69.94H31.341V70.98H38.781V69.94ZM31.341 72.68V73.6H42.161V78.76H39.781V78.22H31.341V78.76H28.921V68.22H41.101V72.68H31.341ZM33.441 64.5L35.561 64.08C35.861 64.52 36.161 65.12 36.361 65.62H43.601V69.1H41.161V67.4H28.821V69.1H26.521V65.62H33.961C33.821 65.24 33.641 64.82 33.441 64.5ZM38.681 61.5H44.001V63.14H41.241C41.601 63.62 41.941 64.08 42.141 64.44L40.141 65.34C39.921 64.74 39.301 63.88 38.701 63.14H37.881C37.541 63.74 37.161 64.28 36.761 64.7C36.321 64.4 35.341 63.92 34.801 63.68C35.721 62.76 36.421 61.32 36.801 59.92L39.101 60.34C38.981 60.72 38.841 61.12 38.681 61.5Z" fill="white"/>
</g>
</g>
<g id="Frame">
<path id="Vector" d="M25.4617 2.83671C28.0863 3.74247 30.975 5.02677 34.1317 6.68766C37.2738 8.34073 40.455 10.251 43.6763 12.4147C43.8521 12.5327 43.999 12.6887 44.1063 12.8712C44.2136 13.0536 44.2786 13.2579 44.2963 13.4688C44.3141 13.6797 44.2842 13.8919 44.2089 14.0897C44.1336 14.2875 44.0148 14.4659 43.8612 14.6116C42.8503 15.5729 41.7952 16.4869 40.6996 17.3504C41.084 18.4753 41.3276 19.6432 41.4264 20.8287C42.5626 21.1396 43.5589 21.8283 44.251 22.7814C44.9432 23.7344 45.2899 24.8948 45.2339 26.0713C45.178 27.2478 44.7227 28.3701 43.9433 29.2532C43.1638 30.1363 42.1066 30.7274 40.9461 30.929C40.0236 34.4554 37.958 37.5765 35.0726 39.804C32.1872 42.0315 28.6448 43.2398 24.9995 43.2398C21.3542 43.2398 17.8117 42.0315 14.9263 39.804C12.0409 37.5765 9.97535 34.4554 9.05283 30.929C7.89234 30.7272 6.83527 30.136 6.0559 29.2529C5.27654 28.3698 4.82142 27.2474 4.76559 26.0709C4.70976 24.8944 5.05654 23.7341 5.74877 22.7811C6.441 21.8281 7.43733 21.1395 8.5735 20.8287C8.67523 19.6207 8.92273 18.4548 9.29935 17.3485C8.20427 16.4856 7.1499 15.5724 6.13966 14.6116C5.98538 14.4654 5.8661 14.2863 5.79068 14.0876C5.71527 13.8889 5.68567 13.6757 5.7041 13.464C5.72253 13.2522 5.78851 13.0474 5.89712 12.8647C6.00573 12.682 6.15418 12.5262 6.33139 12.4088C9.77868 10.1268 12.9579 8.21944 15.8691 6.68864C18.7911 5.15002 21.674 3.86767 24.5206 2.84355C24.8239 2.734 25.1545 2.73204 25.4597 2.83671H25.4617ZM38.2775 19.1013L37.8921 19.3527C33.6309 22.0895 29.3316 23.4717 25 23.4717C20.5392 23.4717 16.1117 22.0064 11.7244 19.1023C11.4732 20.0682 11.3466 21.0622 11.3478 22.0602V23.4717H9.9352C9.62042 23.4737 9.30925 23.5389 9.02011 23.6633C8.73097 23.7878 8.46973 23.969 8.25186 24.1961C8.03398 24.4233 7.8639 24.6919 7.75167 24.986C7.63944 25.28 7.58734 25.5936 7.59847 25.9082C7.60959 26.2228 7.68371 26.5319 7.81644 26.8173C7.94916 27.1027 8.1378 27.3586 8.37118 27.5698C8.60456 27.7811 8.87794 27.9433 9.17515 28.047C9.47236 28.1507 9.78736 28.1938 10.1015 28.1736L11.3536 28.0866L11.5855 29.32C12.7946 35.7171 18.4106 40.418 25 40.418C31.5893 40.418 37.2053 35.7171 38.4144 29.32L38.6473 28.0875L39.8994 28.1736C40.2136 28.1938 40.5286 28.1508 40.8259 28.047C41.1231 27.9433 41.3966 27.781 41.63 27.5697C41.8634 27.3584 42.052 27.1025 42.1847 26.817C42.3174 26.5315 42.3915 26.2223 42.4025 25.9077C42.4136 25.5931 42.3614 25.2795 42.2491 24.9854C42.1368 24.6913 41.9666 24.4227 41.7486 24.1956C41.5306 23.9684 41.2693 23.7873 40.98 23.663C40.6908 23.5386 40.3795 23.4736 40.0647 23.4717H38.6512V22.0602C38.6512 21.038 38.522 20.0472 38.2765 19.1013H38.2775ZM20.2927 23.9431V28.65H16.5265V23.9431H20.2927ZM33.4744 23.9431V28.65H29.7082V23.9431H33.4744ZM34.9613 17.8229L15.0386 17.8239C18.3852 19.7117 21.7043 20.6478 25 20.6478C28.2956 20.6478 31.6157 19.7117 34.9613 17.8229ZM25.0097 5.67235C22.3276 6.67319 19.7135 7.84749 17.1839 9.1878C14.7911 10.4477 12.1998 11.9755 9.40989 13.7733C9.90096 14.2028 10.392 14.6116 10.8821 15.0019L39.1188 15.0009C39.6206 14.6009 40.1136 14.1891 40.5959 13.7665C38.0735 12.1232 35.4775 10.5957 32.816 9.18878C29.9694 7.69124 27.3663 6.51845 25.0088 5.67235H25.0097Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_3268_20803" x="0.821094" y="55.1199" width="47.9799" height="28.4809" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3268_20803"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3268_20803" result="shape"/>
</filter>
</defs>
</svg>

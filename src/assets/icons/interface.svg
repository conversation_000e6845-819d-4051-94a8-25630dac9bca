<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_440_6000)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_440_6000)"/>
<g id="Vector" filter="url(#filter0_d_440_6000)">
<path d="M16.0007 25.1673C13.5695 25.1673 11.2379 24.2016 9.51884 22.4825C7.79976 20.7634 6.83398 18.4318 6.83398 16.0007C6.83398 13.5695 7.79976 11.2379 9.51884 9.51884C11.2379 7.79976 13.5695 6.83398 16.0007 6.83398C18.4318 6.83398 20.7634 7.79976 22.4825 9.51884C24.2016 11.2379 25.1673 13.5695 25.1673 16.0007C25.1673 18.4318 24.2016 20.7634 22.4825 22.4825C20.7634 24.2016 18.4318 25.1673 16.0007 25.1673ZM16.0007 23.9451C17.045 23.9468 18.0795 23.7426 19.0449 23.3441C20.0102 22.9456 20.8876 22.3607 21.6267 21.6228C22.3658 20.885 22.9521 20.0086 23.3522 19.0439C23.7522 18.0792 23.9582 17.045 23.9582 16.0006C23.9582 14.9563 23.7522 13.9221 23.3522 12.9574C22.9521 11.9927 22.3658 11.1163 21.6267 10.3785C20.8876 9.64058 20.0102 9.05567 19.0449 8.65719C18.0795 8.25871 17.045 8.05449 16.0007 8.05621C13.8937 8.05621 11.873 8.89321 10.3831 10.3831C8.89321 11.873 8.05621 13.8937 8.05621 16.0007C8.05621 18.1077 8.89321 20.1284 10.3831 21.6182C11.873 23.1081 13.8937 23.9451 16.0007 23.9451V23.9451Z" fill="white"/>
<path d="M16.0007 23.8951L15.9648 23.895C13.884 23.8856 11.8905 23.0549 10.4184 21.5829C8.93794 20.1024 8.10621 18.0944 8.10621 16.0007C8.10621 13.9069 8.93794 11.8989 10.4184 10.4184C11.8989 8.93794 13.9069 8.10621 16.0007 8.10621L16.0007 8.10621C17.0385 8.1045 18.0665 8.30744 19.0258 8.70341C19.9851 9.09938 20.8569 9.6806 21.5913 10.4138C22.3258 11.1471 22.9085 12.0179 23.306 12.9766C23.7035 13.9352 23.9082 14.9628 23.9082 16.0006C23.9082 17.0385 23.7035 18.0661 23.306 19.0247C22.9085 19.9834 22.3258 20.8542 21.5913 21.5875C20.8569 22.3207 19.9851 22.9019 19.0258 23.2979C18.0665 23.6939 17.0385 23.8968 16.0007 23.8951ZM9.48349 22.5178C11.2119 24.2463 13.5562 25.2173 16.0007 25.2173C18.4451 25.2173 20.7894 24.2463 22.5178 22.5178C24.2463 20.7894 25.2173 18.4451 25.2173 16.0007C25.2173 13.5562 24.2463 11.2119 22.5178 9.48349C20.7894 7.75502 18.4451 6.78398 16.0007 6.78398C13.5562 6.78398 11.2119 7.75502 9.48349 9.48349C7.75502 11.2119 6.78398 13.5562 6.78398 16.0007C6.78398 18.4451 7.75502 20.7894 9.48349 22.5178Z" stroke="white" stroke-width="0.1"/>
</g>
<g id="Vector_2" filter="url(#filter1_d_440_6000)">
<path d="M20.0832 11.8596C19.1188 10.8965 17.6583 10.7914 16.8247 11.6249L14.6125 13.8372C14.1786 14.271 14.005 14.8748 14.0686 15.4896C14.0324 15.4031 13.9754 15.3269 13.9026 15.2678C13.8298 15.2087 13.7435 15.1685 13.6514 15.1508C13.5593 15.1332 13.4643 15.1386 13.3748 15.1665C13.2853 15.1945 13.2041 15.2442 13.1385 15.3112L11.662 16.7864C10.8285 17.6187 10.9336 19.0805 11.8967 20.0436C12.8598 21.0067 14.3216 21.1118 15.1552 20.2783L17.3674 18.066C17.8 17.6322 17.9736 17.0284 17.9113 16.4136C17.9475 16.4999 18.0045 16.5758 18.0772 16.6348C18.1499 16.6937 18.236 16.7338 18.3279 16.7514C18.4198 16.7691 18.5147 16.7638 18.604 16.736C18.6934 16.7082 18.7745 16.6587 18.8402 16.592L20.3154 15.118C21.1489 14.2845 21.0438 12.8227 20.0807 11.8596H20.0832ZM19.505 14.3065L18.031 15.7805C17.9326 15.8804 17.8747 16.0132 17.8685 16.1533C17.7582 15.6435 17.5026 15.1765 17.1327 14.8088C17.0235 14.7091 16.8801 14.6553 16.7323 14.6587C16.5845 14.662 16.4437 14.7222 16.3391 14.8268C16.2346 14.9313 16.1744 15.0722 16.171 15.22C16.1677 15.3678 16.2214 15.5112 16.3212 15.6204C16.8284 16.1264 16.9359 16.8756 16.5558 17.2545L14.3436 19.4667C13.9647 19.8456 13.2167 19.7381 12.7095 19.232C12.2035 18.7248 12.0959 17.9768 12.4748 17.5967L13.95 16.1227C14.0481 16.0227 14.1056 15.8899 14.1114 15.7499C14.214 16.2303 14.456 16.7033 14.8472 17.0944C14.9563 17.1941 15.0997 17.2479 15.2476 17.2445C15.3954 17.2412 15.5362 17.181 15.6408 17.0764C15.7453 16.9719 15.8055 16.831 15.8089 16.6832C15.8122 16.5354 15.7584 16.392 15.6587 16.2828C15.1515 15.7768 15.0452 15.0276 15.424 14.6487L17.6363 12.4365C18.0152 12.0576 18.7644 12.1651 19.2704 12.6712C19.7776 13.1784 19.8839 13.9264 19.505 14.3065Z" fill="white"/>
<path d="M17.8656 15.9407C17.8957 15.8685 17.9396 15.8021 17.9954 15.7454L17.9957 15.7451L19.4696 14.2712L17.8656 15.9407ZM17.8656 15.9407C17.7347 15.5004 17.4953 15.0987 17.168 14.7733L17.168 14.7733L17.1664 14.7719C17.0478 14.6635 16.8919 14.605 16.7312 14.6087C16.5705 14.6123 16.4174 14.6778 16.3038 14.7914C16.1901 14.9051 16.1247 15.0582 16.121 15.2189C16.1174 15.3795 16.1758 15.5354 16.2842 15.6541L16.2842 15.6541L16.2858 15.6558C16.5322 15.9016 16.6806 16.2056 16.7217 16.4922C16.7629 16.7791 16.6965 17.0437 16.5205 17.2191L16.5205 17.2191L14.3082 19.4314C14.1328 19.6067 13.8685 19.6729 13.5818 19.6317C13.2953 19.5906 12.9912 19.4425 12.7448 19.1966C12.499 18.9502 12.351 18.6462 12.3098 18.3596C12.2686 18.0727 12.3348 17.8081 12.5102 17.6321L17.8656 15.9407ZM17.4028 18.1013C17.8014 17.7016 17.9848 17.1629 17.973 16.6041C17.9953 16.6291 18.0196 16.6524 18.0457 16.6736C18.1248 16.7377 18.2185 16.7813 18.3185 16.8005C18.4185 16.8197 18.5217 16.814 18.6189 16.7837C18.716 16.7535 18.8042 16.6997 18.8756 16.6273C18.8757 16.6272 18.8757 16.6272 18.8758 16.6271L20.3507 15.1534L20.3507 15.1534C21.1838 14.3203 21.0995 12.8871 20.198 11.9096H20.204L20.1185 11.8242C19.1396 10.8466 17.6471 10.7319 16.7894 11.5896L14.5771 13.8018C14.1776 14.2013 13.994 14.7397 14.0065 15.2982C13.9843 15.2733 13.9601 15.2501 13.9341 15.229C13.8549 15.1647 13.761 15.121 13.6608 15.1017C13.5606 15.0825 13.4573 15.0884 13.3599 15.1188C13.2626 15.1492 13.1743 15.2032 13.103 15.276C13.1029 15.276 13.1028 15.2761 13.1028 15.2762L11.6267 16.751C11.6267 16.751 11.6267 16.751 11.6267 16.751C10.769 17.6075 10.8837 19.1013 11.8614 20.0789C12.839 21.0566 14.3328 21.1713 15.1905 20.3136L17.4027 18.1014L17.4028 18.1013ZM12.5102 17.632L13.9854 16.1581L13.9857 16.1577C14.041 16.1013 14.0844 16.0354 14.1143 15.9638C14.2385 16.3821 14.469 16.7869 14.8118 17.1297L14.8118 17.1298L14.8134 17.1313C14.9321 17.2397 15.088 17.2981 15.2487 17.2945C15.4094 17.2909 15.5625 17.2254 15.6761 17.1118C15.7898 16.9981 15.8552 16.845 15.8588 16.6843C15.8625 16.5237 15.804 16.3678 15.6956 16.2491L15.6957 16.2491L15.694 16.2474C15.4476 16.0016 15.2996 15.6976 15.2586 15.411C15.2175 15.1241 15.284 14.8595 15.4594 14.6841L17.6716 12.4718C17.847 12.2965 18.1116 12.2303 18.3986 12.2715C18.6852 12.3126 18.9892 12.4607 19.235 12.7065C19.4814 12.9529 19.6295 13.257 19.6705 13.5436C19.7115 13.8304 19.6451 14.0951 19.4697 14.2711L12.5102 17.632Z" stroke="white" stroke-width="0.1"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_440_6000" x="-0.665625" y="-0.665625" width="33.3332" height="33.3332" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_440_6000"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_440_6000" result="shape"/>
</filter>
<filter id="filter1_d_440_6000" x="3.6" y="3.56289" width="24.7785" height="24.7785" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_440_6000"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_440_6000" result="shape"/>
</filter>
<linearGradient id="paint0_linear_440_6000" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_440_6000">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

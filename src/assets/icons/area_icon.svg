<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_552_6121)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_552_6121)"/>
<g id="Vector" filter="url(#filter0_d_552_6121)">
<path d="M16 8.19922C18.4258 8.19922 20.4004 10.3516 20.4004 13C20.4004 14.3789 18.6563 16.9766 16 19.6016C13.3438 16.9766 11.5996 14.3789 11.5996 13C11.5996 10.3535 13.5742 8.19922 16 8.19922ZM16 6.59961C12.6855 6.59961 10 9.46484 10 13C10 15.9102 14.0645 19.9902 15.5039 21.3437C15.6465 21.4785 15.8242 21.5449 16 21.5449C16.1777 21.5449 16.3555 21.4785 16.4961 21.3437C17.9336 19.9902 22 15.9082 22 13C22 9.46484 19.3145 6.59961 16 6.59961Z" fill="white"/>
</g>
<g id="Vector_2" filter="url(#filter1_d_552_6121)">
<path d="M16 15.1992C14.6758 15.1992 13.5996 14.123 13.5996 12.7988C13.5996 11.4746 14.6758 10.4004 16 10.4004C17.3242 10.4004 18.4004 11.4766 18.4004 12.8008C18.4004 14.125 17.3242 15.1992 16 15.1992ZM16 12C15.5586 12 15.1992 12.3594 15.1992 12.8008C15.1992 13.2422 15.5586 13.6016 16 13.6016C16.4414 13.6016 16.8008 13.2422 16.8008 12.8008C16.8008 12.3594 16.4414 12 16 12ZM23.5996 19.8008C23.1602 19.8008 22.7988 20.1602 22.7988 20.6016V23.8008H9.19922V20.5996C9.19922 20.1602 8.83984 19.7988 8.39844 19.7988C7.95703 19.7988 7.59766 20.1582 7.59766 20.5996V24.5996C7.59766 25.0391 7.95703 25.4004 8.39844 25.4004H23.5977C24.0371 25.4004 24.3984 25.041 24.3984 24.5996V20.5996C24.4004 20.1602 24.0391 19.8008 23.5996 19.8008Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_552_6121" x="2.6" y="-0.800391" width="26.8" height="29.7453" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_552_6121"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_552_6121" result="shape"/>
</filter>
<filter id="filter1_d_552_6121" x="0.197656" y="3.00039" width="31.6008" height="29.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_552_6121"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_552_6121" result="shape"/>
</filter>
<linearGradient id="paint0_linear_552_6121" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_552_6121">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

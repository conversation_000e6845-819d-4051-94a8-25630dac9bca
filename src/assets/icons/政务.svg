<svg width="51" height="72" viewBox="0 0 51 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1142817487">
<g id="Group 1142817486">
<g id="&#230;&#148;&#191;&#229;&#138;&#161;" filter="url(#filter0_d_2951_18862)">
<path d="M16.8461 54.0009L16.7261 54.3209C17.1461 56.4209 17.7261 58.3609 18.5661 60.0609C19.3861 58.3609 19.8861 56.3609 20.2461 54.0009H16.8461ZM23.4261 54.0009H22.1061C21.6061 57.1809 20.8461 59.8009 19.6261 61.9209C20.6461 63.4209 21.9661 64.6009 23.5861 65.3609C23.1661 65.7409 22.5861 66.4409 22.3261 66.9409C20.7661 66.1209 19.5261 64.9809 18.5061 63.5809C17.3861 64.9609 15.9661 66.0809 14.2061 67.0009C14.0261 66.5809 13.4861 65.7409 13.1261 65.3609C14.9861 64.5009 16.4061 63.3609 17.4661 61.8809C16.6661 60.4009 16.0661 58.7009 15.6061 56.8409C15.2661 57.4209 14.9261 57.9409 14.5661 58.4409C14.3461 58.1609 13.8461 57.7409 13.4461 57.4209H11.2261V62.0209L14.3661 61.3609L14.5461 63.0409C11.2661 63.8209 7.76609 64.6409 5.26609 65.1809L4.92609 63.3209L6.04609 63.1009V54.4209H7.72609V62.7809L9.40609 62.4209V51.7009H5.32609V49.9009H14.4061V51.7009H11.2261V55.6609H13.7261V56.2809C15.0261 54.3009 15.8661 51.5009 16.3861 48.5809L18.2261 48.9009C18.0061 50.0609 17.7261 51.1809 17.4261 52.2609H23.4261V54.0009ZM36.4861 59.6409H43.7261C43.7261 59.6409 43.6661 60.1609 43.6261 60.4009C43.1661 63.9409 42.7261 65.4809 42.0061 66.1209C41.5061 66.5609 41.0061 66.6809 40.1861 66.7209C39.5261 66.7609 38.3061 66.7609 37.0861 66.6809C37.0661 66.2009 36.8061 65.5209 36.4861 65.0609C37.7661 65.1809 39.1061 65.2009 39.6261 65.2009C40.0661 65.2009 40.3061 65.1809 40.5261 65.0009C40.9861 64.6409 41.3461 63.5209 41.6861 61.2409H35.9461C34.6461 64.3409 32.2461 65.9609 28.4861 66.9409C28.3061 66.5209 27.7661 65.7409 27.4261 65.3809C30.6661 64.7409 32.7861 63.5009 33.9461 61.2409H28.7661V59.6409H34.5661C34.7261 59.0809 34.8461 58.4609 34.9261 57.7809L36.8661 57.9409C36.7661 58.5409 36.6461 59.1209 36.4861 59.6409ZM33.0661 52.1609L32.8461 52.3809C33.6661 53.3409 34.8661 54.1209 36.3461 54.7409C38.0861 54.0409 39.6061 53.2009 40.7461 52.1609H33.0661ZM42.5861 50.5009L43.7261 51.2609C42.5461 53.0209 40.7461 54.4209 38.6061 55.4809C40.6061 56.0009 42.9461 56.3009 45.4461 56.4409C45.0461 56.8409 44.6261 57.6009 44.4061 58.0809C41.3661 57.8409 38.6261 57.3409 36.3461 56.4409C33.7461 57.4009 30.8261 57.9809 27.9661 58.3209C27.8261 57.8609 27.5261 57.1209 27.2461 56.7009C29.6261 56.4809 32.0261 56.0609 34.2461 55.4409C33.2461 54.8609 32.3861 54.1809 31.6261 53.3809C30.8861 53.9609 30.0461 54.5209 29.1061 55.0009C28.8861 54.5809 28.3661 53.8809 27.9861 53.6009C30.7861 52.2209 32.7061 50.3009 33.7261 48.6009L35.6261 48.9809C35.3061 49.5209 34.9261 50.0609 34.5061 50.5809H42.2661L42.5861 50.5009Z" fill="white"/>
</g>
</g>
<g id="Frame" clip-path="url(#clip0_2951_18862)">
<g id="Vector" filter="url(#filter1_d_2951_18862)">
<path d="M18.2165 19.5641C18.5648 19.5641 18.8498 19.855 18.8498 20.2141L18.8482 30.9391H22.0465V20.2141C22.0465 19.855 22.3315 19.5641 22.6799 19.5641H27.8115C28.1614 19.5641 28.4448 19.855 28.4448 20.2141V30.9391H31.6431V20.2141C31.6431 19.855 31.9281 19.5641 32.2765 19.5641H37.408C37.758 19.5641 38.0414 19.855 38.0414 20.2141V30.9391H40.4465C40.7948 30.9391 41.0798 31.23 41.0798 31.5891V32.5641C41.0798 32.9951 40.913 33.4084 40.616 33.7131C40.3191 34.0179 39.9164 34.1891 39.4965 34.1891H10.9965C10.5765 34.1891 10.1738 34.0179 9.87687 33.7131C9.57993 33.4084 9.41312 32.9951 9.41312 32.5641V31.5891C9.41312 31.23 9.69812 30.9391 10.0465 30.9391H12.4515V20.2141C12.4515 19.855 12.735 19.5641 13.0849 19.5641H18.2149H18.2165ZM26.426 4.57995L26.616 4.71808L40.95 16.2832C41.1137 16.4151 41.2362 16.5934 41.3024 16.7959C41.3685 16.9985 41.3754 17.2165 41.3222 17.4231C41.269 17.6296 41.158 17.8156 41.0029 17.9582C40.8478 18.1007 40.6555 18.1935 40.4496 18.2251L40.2913 18.2381H10.2016C9.99431 18.2387 9.79132 18.1773 9.61752 18.0613C9.44372 17.9453 9.30665 17.7798 9.22313 17.5851C9.1396 17.3903 9.11324 17.1748 9.14726 16.965C9.18129 16.7551 9.27423 16.5599 9.4147 16.4035L9.54137 16.2832L23.8769 4.71808C24.2361 4.4282 24.6738 4.25934 25.1301 4.23461C25.5865 4.20988 26.0391 4.3305 26.426 4.57995Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_2951_18862" x="0.125781" y="43.7811" width="50.1195" height="28.0199" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2951_18862"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2951_18862" result="shape"/>
</filter>
<filter id="filter1_d_2951_18862" x="4.33281" y="-0.568555" width="41.8266" height="39.558" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2951_18862"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2951_18862" result="shape"/>
</filter>
<clipPath id="clip0_2951_18862">
<rect width="38" height="39" fill="white" transform="translate(6.24609 0.605469)"/>
</clipPath>
</defs>
</svg>

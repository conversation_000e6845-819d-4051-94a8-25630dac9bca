<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_518_6208)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_518_6208)"/>
<g id="Vector" filter="url(#filter0_d_518_6208)">
<path d="M23.5784 20.8618H8.42155C8.04453 20.8618 7.68296 20.713 7.41636 20.4482C7.14977 20.1834 7 19.8242 7 19.4496V9.41218C7 9.03765 7.14977 8.67845 7.41636 8.41362C7.68296 8.14878 8.04453 8 8.42155 8H9.68515C9.89459 8.00003 10.0954 8.0827 10.2435 8.22983C10.3916 8.37696 10.4748 8.57649 10.4748 8.78454C10.4748 8.9926 10.3916 9.19213 10.2435 9.33926C10.0954 9.48639 9.89459 9.56906 9.68515 9.56909H8.5795V19.2927H23.4205V9.56909H22.3148C22.1054 9.56906 21.9046 9.48639 21.7565 9.33926C21.6084 9.19213 21.5252 8.9926 21.5252 8.78454C21.5252 8.57649 21.6084 8.37696 21.7565 8.22983C21.9046 8.0827 22.1054 8.00003 22.3148 8H23.5784C23.9555 8 24.317 8.14878 24.5836 8.41362C24.8502 8.67845 25 9.03765 25 9.41218V19.4496C25 19.8242 24.8502 20.1834 24.5836 20.4482C24.317 20.713 23.9555 20.8618 23.5784 20.8618Z" fill="white"/>
<path d="M13.4736 24C13.3732 23.9998 13.2738 23.9807 13.1806 23.9435C13.0843 23.9052 12.9965 23.8485 12.9223 23.7765C12.8481 23.7046 12.7888 23.6188 12.748 23.5241C12.7071 23.4294 12.6855 23.3276 12.6842 23.2246C12.683 23.1215 12.7022 23.0193 12.7407 22.9236L14.0043 19.7854C14.0428 19.6898 14.0999 19.6026 14.1724 19.5288C14.2448 19.4551 14.3312 19.3963 14.4265 19.3557C14.5219 19.3151 14.6243 19.2936 14.728 19.2923C14.8317 19.2911 14.9347 19.3102 15.031 19.3484C15.1273 19.3867 15.215 19.4434 15.2893 19.5154C15.3635 19.5874 15.4227 19.6732 15.4636 19.7679C15.5044 19.8626 15.5261 19.9644 15.5273 20.0674C15.5286 20.1704 15.5094 20.2727 15.4709 20.3683L14.2073 23.5065C14.1487 23.6522 14.0474 23.7771 13.9167 23.8651C13.7859 23.9531 13.6315 24.0001 13.4736 24Z" fill="white"/>
<path d="M18.5264 24C18.3684 24 18.214 23.9529 18.0832 23.8648C17.9524 23.7767 17.8512 23.6516 17.7927 23.5057L16.5291 20.3676C16.4906 20.2719 16.4714 20.1696 16.4727 20.0666C16.4739 19.9636 16.4956 19.8618 16.5364 19.7671C16.5773 19.6724 16.6365 19.5866 16.7107 19.5146C16.785 19.4427 16.8727 19.3859 16.969 19.3477C17.0653 19.3094 17.1683 19.2903 17.272 19.2915C17.3757 19.2928 17.4781 19.3143 17.5735 19.3549C17.6688 19.3955 17.7552 19.4543 17.8276 19.5281C17.9001 19.6018 17.9572 19.689 17.9957 19.7846L19.2593 22.9228C19.3075 23.0419 19.3255 23.1709 19.3119 23.2985C19.2982 23.4261 19.2532 23.5485 19.1809 23.6548C19.1085 23.7611 19.011 23.8482 18.8969 23.9084C18.7828 23.9686 18.6556 24 18.5264 24Z" fill="white"/>
<path d="M19.7924 23.9992H12.2108C12.0013 23.9992 11.8004 23.9166 11.6523 23.7694C11.5042 23.6223 11.421 23.4227 11.421 23.2147C11.421 23.0066 11.5042 22.807 11.6523 22.6599C11.8004 22.5128 12.0013 22.4301 12.2108 22.4301H19.7924C20.0018 22.4301 20.2027 22.5128 20.3508 22.6599C20.4989 22.807 20.5821 23.0066 20.5821 23.2147C20.5821 23.4227 20.4989 23.6223 20.3508 23.7694C20.2027 23.9166 20.0018 23.9992 19.7924 23.9992Z" fill="white"/>
<path d="M16.0379 11.452C15.8285 11.452 15.6276 11.3693 15.4795 11.2222C15.3314 11.0751 15.2482 10.8755 15.2482 10.6675V8.78454C15.2482 8.57647 15.3314 8.37692 15.4795 8.22979C15.6276 8.08266 15.8285 8 16.0379 8C16.2474 8 16.4482 8.08266 16.5963 8.22979C16.7445 8.37692 16.8277 8.57647 16.8277 8.78454V10.6675C16.8277 10.8755 16.7445 11.0751 16.5963 11.2222C16.4482 11.3693 16.2474 11.452 16.0379 11.452Z" fill="white"/>
<path d="M13.8519 12.7065C13.7132 12.7065 13.5771 12.6702 13.457 12.6014L11.8159 11.6599C11.6345 11.5559 11.5022 11.3845 11.448 11.1835C11.3937 10.9825 11.4221 10.7684 11.5269 10.5882C11.6316 10.408 11.8041 10.2765 12.0064 10.2227C12.2087 10.1688 12.4243 10.197 12.6057 10.3011L14.2475 11.2417C14.3981 11.3281 14.5158 11.4614 14.5823 11.6209C14.6488 11.7805 14.6605 11.9574 14.6155 12.1242C14.5705 12.291 14.4713 12.4384 14.3334 12.5436C14.1955 12.6487 14.0265 12.7057 13.8527 12.7057L13.8519 12.7065Z" fill="white"/>
<path d="M12.2108 16.1569C12.072 16.157 11.9357 16.1209 11.8155 16.052C11.6953 15.9831 11.5954 15.884 11.5261 15.7646C11.4741 15.6753 11.4404 15.5767 11.4269 15.4745C11.4133 15.3722 11.4202 15.2683 11.4471 15.1687C11.474 15.0691 11.5205 14.9757 11.5838 14.894C11.647 14.8122 11.7259 14.7436 11.8159 14.6922L13.4578 13.7507C13.5477 13.6992 13.6468 13.6658 13.7497 13.6525C13.8526 13.6391 13.9571 13.6459 14.0572 13.6727C14.1574 13.6994 14.2513 13.7455 14.3336 13.8083C14.4158 13.8711 14.4848 13.9494 14.5366 14.0386C14.6413 14.2188 14.6697 14.433 14.6155 14.634C14.5613 14.8349 14.4289 15.0063 14.2475 15.1103L12.6057 16.0518C12.4856 16.1206 12.3494 16.1569 12.2108 16.1569Z" fill="white"/>
<path d="M16.0379 18.3528C15.8285 18.3528 15.6276 18.2702 15.4795 18.1231C15.3314 17.9759 15.2482 17.7764 15.2482 17.5683V15.6854C15.2482 15.4773 15.3314 15.2778 15.4795 15.1306C15.6276 14.9835 15.8285 14.9009 16.0379 14.9009C16.2474 14.9009 16.4482 14.9835 16.5963 15.1306C16.7445 15.2778 16.8277 15.4773 16.8277 15.6854V17.5683C16.8277 17.7764 16.7445 17.9759 16.5963 18.1231C16.4482 18.2702 16.2474 18.3528 16.0379 18.3528Z" fill="white"/>
<path d="M19.8643 16.1569C19.7256 16.1569 19.5894 16.1206 19.4694 16.0518L17.8283 15.1103C17.6469 15.0063 17.5145 14.8349 17.4603 14.634C17.4061 14.433 17.4345 14.2188 17.5392 14.0386C17.644 13.8585 17.8164 13.727 18.0188 13.6731C18.2211 13.6193 18.4366 13.6475 18.618 13.7515L20.2599 14.6922C20.4105 14.7785 20.5281 14.9118 20.5946 15.0714C20.6612 15.2309 20.6728 15.4078 20.6278 15.5746C20.5829 15.7415 20.4837 15.8889 20.3458 15.994C20.2079 16.0991 20.0389 16.1561 19.865 16.1561L19.8643 16.1569Z" fill="white"/>
<path d="M18.2231 12.7065C18.0844 12.7066 17.948 12.6704 17.8278 12.6016C17.7076 12.5327 17.6078 12.4336 17.5384 12.3142C17.4865 12.2249 17.4528 12.1263 17.4392 12.024C17.4257 11.9218 17.4326 11.8179 17.4595 11.7183C17.4864 11.6187 17.5328 11.5253 17.5961 11.4435C17.6594 11.3618 17.7383 11.2932 17.8283 11.2417L19.4702 10.3003C19.56 10.2488 19.6592 10.2154 19.7621 10.202C19.8649 10.1886 19.9694 10.1955 20.0696 10.2222C20.1698 10.249 20.2637 10.2951 20.3459 10.3579C20.4282 10.4207 20.4972 10.4989 20.549 10.5882C20.6537 10.7684 20.6821 10.9825 20.6279 11.1835C20.5737 11.3845 20.4413 11.5559 20.2599 11.6599L18.618 12.6014C18.498 12.6702 18.3618 12.7065 18.2231 12.7065Z" fill="white"/>
<path d="M16.0387 9.88212C16.6947 9.88212 17.336 10.0754 17.8815 10.4374C18.427 10.7995 18.8521 11.3141 19.1032 11.9162C19.3542 12.5183 19.4199 13.1809 19.2919 13.82C19.1639 14.4592 18.848 15.0464 18.3841 15.5072C17.9203 15.968 17.3292 16.2818 16.6858 16.409C16.0424 16.5361 15.3754 16.4709 14.7694 16.2215C14.1633 15.9721 13.6452 15.5497 13.2808 15.0079C12.9163 14.466 12.7217 13.8289 12.7217 13.1772C12.7217 12.3033 13.0712 11.4652 13.6933 10.8472C14.3153 10.2293 15.159 9.88212 16.0387 9.88212ZM16.0387 14.9032C16.3823 14.9032 16.7183 14.802 17.004 14.6123C17.2897 14.4227 17.5124 14.1531 17.6439 13.8377C17.7754 13.5223 17.8098 13.1753 17.7428 12.8405C17.6757 12.5057 17.5102 12.1981 17.2673 11.9567C17.0243 11.7154 16.7147 11.551 16.3777 11.4844C16.0406 11.4178 15.6913 11.452 15.3738 11.5826C15.0563 11.7132 14.785 11.9345 14.5941 12.2183C14.4031 12.5021 14.3012 12.8358 14.3012 13.1772C14.3012 13.4039 14.3462 13.6283 14.4335 13.8377C14.5208 14.0471 14.6488 14.2374 14.8101 14.3977C15.136 14.7214 15.5779 14.9032 16.0387 14.9032Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_518_6208" x="-0.4" y="0.6" width="32.8" height="30.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_518_6208"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_518_6208" result="shape"/>
</filter>
<linearGradient id="paint0_linear_518_6208" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_518_6208">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

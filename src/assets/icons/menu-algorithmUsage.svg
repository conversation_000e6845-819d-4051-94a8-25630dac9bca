<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_304_16330)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_304_16330)"/>
<g id="Vector" filter="url(#filter0_d_304_16330)">
<path d="M22.5742 24C22.3598 23.9872 22.1487 23.9434 21.9484 23.8702L21.9346 23.8616L16.5671 21.5438C16.462 21.4905 16.3447 21.4627 16.2256 21.4627C16.1064 21.4627 15.9891 21.4905 15.8841 21.5438L10.0525 23.8659L10.0457 23.8702C9.84613 23.9455 9.63487 23.9893 9.42033 24C9.15265 24.0017 8.88994 23.9315 8.66278 23.7976C8.43562 23.6636 8.25335 23.4713 8.13717 23.2432V23.2346C8.05786 23.0439 8.01156 22.8424 8.00004 22.6378V10.6162C7.99627 9.9265 8.28204 9.26357 8.79459 8.77303C9.30714 8.28249 10.0046 8.00446 10.7337 8H21.2709C21.9995 8.00469 22.6963 8.28293 23.2081 8.77354C23.7199 9.26416 24.0047 9.92697 23.9999 10.6162V22.6378C24.001 22.8158 23.965 22.9923 23.8939 23.1572C23.8228 23.3221 23.718 23.472 23.5856 23.5985C23.4532 23.7251 23.2957 23.8256 23.1221 23.8945C22.9486 23.9634 22.7624 23.9992 22.5742 24ZM22.7895 10.6162C22.793 10.4254 22.7562 10.2359 22.6812 10.0588C22.6063 9.88168 22.4947 9.72051 22.353 9.58475C22.2112 9.449 22.0423 9.3414 21.856 9.26827C21.6697 9.19515 21.4698 9.15797 21.2681 9.15892H10.7309C10.5293 9.15808 10.3295 9.19534 10.1432 9.26851C9.957 9.34168 9.78809 9.44928 9.64639 9.58502C9.5047 9.72075 9.39308 9.88187 9.31807 10.0589C9.24306 10.236 9.20619 10.4255 9.2096 10.6162V22.6508H9.23154L9.26354 22.7113C9.27445 22.731 9.28942 22.7484 9.30756 22.7625C9.32569 22.7766 9.34663 22.7871 9.36913 22.7935C9.41238 22.8075 9.45957 22.806 9.5017 22.7892L14.4144 20.4713C15.0089 20.3373 15.6181 20.2705 16.2292 20.2724C16.6908 20.2703 17.1498 20.3375 17.5892 20.4713L22.5042 22.7892L22.5115 22.7935C22.536 22.8075 22.5641 22.8149 22.5927 22.8149C22.6213 22.8149 22.6493 22.8075 22.6738 22.7935C22.7058 22.7792 22.7334 22.7574 22.7539 22.7301C22.7744 22.7029 22.7871 22.6711 22.7908 22.6378V10.6162H22.7895ZM21.5255 15.0876H18.2109C18.1274 15.0876 18.0448 15.072 17.9677 15.0418C17.8906 15.0116 17.8206 14.9673 17.7616 14.9115C17.7026 14.8557 17.6558 14.7894 17.6238 14.7165C17.5919 14.6436 17.5755 14.5654 17.5755 14.4865C17.5755 14.4075 17.5919 14.3294 17.6238 14.2565C17.6558 14.1835 17.7026 14.1173 17.7616 14.0614C17.8206 14.0056 17.8906 13.9614 17.9677 13.9311C18.0448 13.9009 18.1274 13.8854 18.2109 13.8854H21.5255C21.609 13.8854 21.6916 13.9009 21.7687 13.9311C21.8458 13.9614 21.9158 14.0056 21.9748 14.0614C22.0338 14.1173 22.0806 14.1835 22.1126 14.2565C22.1445 14.3294 22.1609 14.4075 22.1609 14.4865C22.1609 14.5654 22.1445 14.6436 22.1126 14.7165C22.0806 14.7894 22.0338 14.8557 21.9748 14.9115C21.9158 14.9673 21.8458 15.0116 21.7687 15.0418C21.6916 15.072 21.609 15.0876 21.5255 15.0876ZM14.2339 15.0876H13.2136V16.0476C13.2091 16.2046 13.1401 16.3538 13.0211 16.4633C12.9021 16.5729 12.7426 16.6342 12.5766 16.6342C12.4105 16.6342 12.251 16.5729 12.132 16.4633C12.0131 16.3538 11.944 16.2046 11.9396 16.0476V15.0876H10.9229C10.8395 15.0876 10.7568 15.072 10.6797 15.0418C10.6027 15.0116 10.5326 14.9673 10.4736 14.9115C10.4146 14.8557 10.3678 14.7894 10.3359 14.7165C10.3039 14.6436 10.2875 14.5654 10.2875 14.4865C10.2875 14.4075 10.3039 14.3294 10.3359 14.2565C10.3678 14.1835 10.4146 14.1173 10.4736 14.0614C10.5326 14.0056 10.6027 13.9614 10.6797 13.9311C10.7568 13.9009 10.8395 13.8854 10.9229 13.8854H11.9418V12.9254C11.9554 12.7742 12.0284 12.6334 12.1465 12.5307C12.2646 12.4281 12.4192 12.3711 12.5795 12.3711C12.7399 12.3711 12.8944 12.4281 13.0125 12.5307C13.1306 12.6334 13.2037 12.7742 13.2172 12.9254V13.8854H14.2362C14.4047 13.8857 14.5662 13.9493 14.6851 14.0622C14.8041 14.1751 14.8707 14.3281 14.8704 14.4876C14.8701 14.647 14.8029 14.7997 14.6835 14.9123C14.5641 15.0248 14.4024 15.0878 14.2339 15.0876Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_304_16330" x="0.6" y="0.6" width="30.8" height="30.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_304_16330"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_304_16330" result="shape"/>
</filter>
<linearGradient id="paint0_linear_304_16330" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_304_16330">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

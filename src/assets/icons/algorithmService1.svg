<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_304_16322)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_304_16322)"/>
<g id="Vector" filter="url(#filter0_d_304_16322)">
<path d="M24.6291 20.2434C24.7694 20.5266 24.6535 20.8714 24.3728 21.0118L16.6704 24.8783C16.5154 24.9569 16.3445 24.9985 16.1711 25C15.9976 25.0015 15.8261 24.9628 15.6698 24.887L7.6928 21.0167C7.62557 20.9841 7.56537 20.9385 7.51562 20.8824C7.46586 20.8264 7.42754 20.7609 7.40284 20.6899C7.37814 20.6189 7.36754 20.5437 7.37165 20.4686C7.37575 20.3934 7.39449 20.3198 7.42678 20.252C7.49199 20.115 7.60846 20.0097 7.75058 19.9594C7.82095 19.9345 7.89549 19.9238 7.96995 19.9279C8.04442 19.9321 8.11735 19.951 8.18457 19.9836L16.1615 23.8551L23.8676 19.9872C24.1483 19.8456 24.49 19.9614 24.6291 20.2458V20.2434ZM24.6291 16.3152C24.7694 16.5984 24.6535 16.9432 24.3728 17.0836L16.6704 20.9502C16.5154 21.0287 16.3445 21.0703 16.1711 21.0718C15.9976 21.0733 15.8261 21.0346 15.6698 20.9588L7.6928 17.0861C7.62557 17.0535 7.56537 17.0079 7.51562 16.9518C7.46586 16.8957 7.42754 16.8303 7.40284 16.7593C7.37814 16.6883 7.36754 16.6131 7.37165 16.538C7.37575 16.4628 7.39449 16.3892 7.42678 16.3214C7.49199 16.1844 7.60846 16.0791 7.75058 16.0288C7.82095 16.0039 7.89549 15.9932 7.96995 15.9973C8.04442 16.0015 8.11735 16.0204 8.18457 16.0529L16.1615 19.9257L23.8676 16.0566C24.1483 15.9163 24.49 16.032 24.6291 16.3152ZM15.6258 7.11424C15.782 7.03803 15.9534 6.99895 16.1269 7.00002C16.3003 7.00109 16.4712 7.04227 16.6265 7.1204L24.3692 11.0141C24.5585 11.1088 24.7178 11.2549 24.8293 11.4361C24.9408 11.6172 24.9999 11.8262 25 12.0394C25.0001 12.2527 24.9413 12.4617 24.83 12.643C24.7188 12.8243 24.5596 12.9706 24.3704 13.0656L16.6704 16.9568C16.5153 17.0351 16.3444 17.0765 16.1709 17.0778C15.9975 17.0791 15.826 17.0402 15.6698 16.9642L7.64521 13.0705C7.45223 12.9774 7.28927 12.8311 7.17512 12.6485C7.06096 12.466 7.00026 12.2546 7 12.0387C6.99975 11.8229 7.05995 11.6114 7.17368 11.4285C7.2874 11.2457 7.45001 11.099 7.64277 11.0054L15.6258 7.11424ZM16.1201 8.14738L8.13698 12.0386L16.1615 15.931L23.8615 12.0398L16.1213 8.14861L16.1201 8.14738ZM16.5947 9.65338L16.5935 13.3279L18.8901 12.1913L18.8852 12.1309C18.8852 11.6741 19.3184 11.2961 19.8785 11.2406L20.0213 11.2333C20.6485 11.2333 21.1562 11.6359 21.1562 12.1322C21.1562 12.6272 20.6485 13.0286 20.0213 13.0286C19.8261 13.0286 19.6418 12.9905 19.4807 12.9215L15.6856 14.7981V11.1692L13.5331 12.312C13.4843 12.7725 12.9961 13.1358 12.4006 13.1358C11.7734 13.1358 11.2658 12.7331 11.2658 12.2368C11.2658 11.7418 11.7734 11.3404 12.4006 11.3404C12.6532 11.3404 12.8875 11.4057 13.0755 11.5152L16.5947 9.65338Z" fill="white"/>
<path d="M24.6728 20.2214L24.6739 20.2212C24.8264 20.529 24.7006 20.9037 24.3953 21.0565L24.6728 20.2214ZM24.6728 20.2214C24.5208 19.914 24.1501 19.7887 23.8451 19.9426L16.1612 23.7993L8.2064 19.9386L8.20638 19.9386C8.13321 19.9031 8.05382 19.8825 7.97273 19.878C7.89165 19.8735 7.81049 19.8851 7.73388 19.9123C7.57917 19.9671 7.45251 20.0816 7.38163 20.2305C7.34653 20.3042 7.32618 20.3842 7.32172 20.4658C7.31726 20.5475 7.32877 20.6292 7.35562 20.7064C7.38246 20.7835 7.42411 20.8546 7.47822 20.9156C7.53232 20.9766 7.59781 21.0262 7.67097 21.0617C7.67098 21.0617 7.67098 21.0617 7.67099 21.0617L15.6479 24.932L15.6698 24.887L15.648 24.932C15.8112 25.0111 15.9903 25.0515 16.1715 25.05C16.3527 25.0484 16.5311 25.0049 16.693 24.923L24.3952 21.0565L24.6728 20.2214ZM15.648 21.0038L15.6479 21.0038L7.67099 17.1311L15.648 21.0038ZM15.648 21.0038C15.8112 21.083 15.9903 21.1234 16.1715 21.1218C16.3527 21.1202 16.5311 21.0768 16.693 20.9948L24.3952 17.1283L15.648 21.0038ZM8.20638 16.0079L8.20641 16.008L16.1612 19.8699L23.8452 16.0119L23.8453 16.0119C24.1509 15.859 24.5226 15.9852 24.6739 16.2931C24.8264 16.6009 24.7005 16.9756 24.3953 17.1283L8.20638 16.0079ZM8.20638 16.0079C8.13321 15.9725 8.05382 15.9519 7.97273 15.9474C7.89165 15.9429 7.81049 15.9545 7.73388 15.9817C7.57917 16.0365 7.45251 16.151 7.38163 16.2999C7.34653 16.3736 7.32618 16.4536 7.32172 16.5352C7.31726 16.6169 7.32877 16.6986 7.35562 16.7758C7.38246 16.8529 7.42411 16.924 7.47822 16.985C7.53231 17.046 7.5978 17.0956 7.67096 17.1311L8.20638 16.0079ZM16.693 17.0014L16.6929 17.0014C16.5309 17.0832 16.3525 17.1264 16.1713 17.1278C15.9901 17.1291 15.8111 17.0885 15.6479 17.0091L7.62347 13.1155L16.693 17.0014ZM16.693 17.0014L24.3928 13.1103C24.3928 13.1103 24.3928 13.1102 24.3929 13.1102C24.5904 13.0111 24.7566 12.8583 24.8726 12.6691C24.9887 12.48 25.0501 12.2619 25.05 12.0394C25.0499 11.817 24.9882 11.5989 24.8719 11.4099C24.7556 11.2208 24.5892 11.0682 24.3915 10.9694L16.649 7.07574C16.6489 7.07573 16.6489 7.07573 16.6489 7.07573C16.4868 6.99415 16.3084 6.95114 16.1272 6.95002C15.946 6.94891 15.767 6.98972 15.6039 7.06931M16.693 17.0014L15.6039 7.06931M15.6039 7.06931L7.62092 10.9605C7.41964 11.0582 7.2499 11.2113 7.13122 11.4021C7.01254 11.5929 6.94973 11.8136 6.95 12.0388C6.95027 12.264 7.01359 12.4846 7.13272 12.6751C7.25184 12.8655 7.42191 13.0182 7.62338 13.1155L15.6039 7.06931ZM16.1191 8.20348L23.7504 12.0399L16.1611 15.8752L8.25132 12.0385L16.1191 8.20348ZM16.6447 9.65339L16.6448 9.57034L16.5714 9.60918L13.0763 11.4582C12.8848 11.3524 12.651 11.2904 12.4006 11.2904C11.7574 11.2904 11.2158 11.7039 11.2158 12.2368C11.2158 12.7709 11.7573 13.1858 12.4006 13.1858C13.0012 13.1858 13.5116 12.825 13.5795 12.3439L15.6356 11.2524V14.7981V14.8787L15.7078 14.843L19.4825 12.9764C19.6448 13.0425 19.8281 13.0786 20.0213 13.0786C20.6645 13.0786 21.2062 12.6651 21.2062 12.1322C21.2062 11.5981 20.6647 11.1833 20.0213 11.1833V11.1832L20.0187 11.1833L19.8759 11.1907L19.8759 11.1907L19.8736 11.1909C19.2981 11.2478 18.8352 11.6393 18.8352 12.1309H18.8351L18.8354 12.135L18.8375 12.1615L16.6436 13.2473L16.6447 9.65339Z" stroke="white" stroke-width="0.1"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_304_16322" x="-0.499609" y="-0.499609" width="32.9992" height="32.9992" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_304_16322"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_304_16322" result="shape"/>
</filter>
<linearGradient id="paint0_linear_304_16322" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_304_16322">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

<svg width="85" height="118" viewBox="0 0 85 118" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1142819321">
<g id="Vector" filter="url(#filter0_d_3248_17059)">
<path d="M42 49C29.9 49 20 39.1 20 27C20 14.9 29.9 5 42 5C54.1 5 64 14.9 64 27C64 39.1 54.1 49 42 49ZM42 8.66667C31.9167 8.66667 23.6667 16.9167 23.6667 27C23.6667 37.0833 31.9167 45.3333 42 45.3333C52.0833 45.3333 60.3333 37.0833 60.3333 27C60.3333 16.9167 52.0833 8.66667 42 8.66667Z" fill="white"/>
<path d="M34.1167 35.25C33.5667 35.25 33.2 35.0667 32.8333 34.7C32.1 33.9667 32.1 32.8667 32.8333 32.1333L37.9667 27L32.8333 21.8667C32.1 21.1333 32.1 20.0333 32.8333 19.3C33.5667 18.5667 34.6667 18.5667 35.4 19.3L41.8167 25.7167C42.1833 26.0833 42.3667 26.45 42.3667 27C42.3667 27.55 42.1833 27.9167 41.8167 28.2833L35.4 34.7C35.0333 35.0667 34.4833 35.25 34.1167 35.25ZM45.1167 35.25C44.5667 35.25 44.2 35.0667 43.8333 34.7C43.1 33.9667 43.1 32.8667 43.8333 32.1333L48.9667 27L43.8333 21.8667C43.1 21.1333 43.1 20.0333 43.8333 19.3C44.5667 18.5667 45.6667 18.5667 46.4 19.3L52.8167 25.7167C53.55 26.45 53.55 27.55 52.8167 28.2833L46.4 34.7C46.0333 35.0667 45.4833 35.25 45.1167 35.25Z" fill="white"/>
</g>
<g id="&#232;&#174;&#173;&#231;&#187;&#131;&#228;&#184;&#173;&#231;&#174;&#151;&#230;&#179;&#149;" filter="url(#filter1_d_3248_17059)">
<path d="M17.02 69.62L15.8 71.02C15.08 70.14 13.52 68.76 12.34 67.8L13.48 66.56C14.66 67.46 16.26 68.78 17.02 69.62ZM15.72 80.3L17.38 78.88C17.56 79.4 17.9 80.12 18.14 80.46C14.8 83.32 14.26 83.82 13.96 84.3C13.78 83.92 13.28 83.26 12.98 82.98C13.36 82.64 13.94 81.96 13.94 80.96V74.18H11.36V72.4H15.72V80.3ZM18.96 73.7V66.82H20.82V73.72C20.82 77.5 20.6 81.4 18.66 84.54C18.24 84.2 17.4 83.7 16.86 83.48C18.76 80.6 18.96 77.18 18.96 73.7ZM23.08 81.98V67.82H24.8V81.98H23.08ZM27.14 66.72H29V84.32H27.14V66.72ZM33.86 74.64V74.56C33.84 74.58 33.84 74.62 33.84 74.64C33.76 74.26 33.48 73.4 33.26 72.94C33.62 72.84 33.92 72.36 34.3 71.7C34.68 71.06 35.86 68.48 36.46 66.12L38.18 66.88C37.52 68.88 36.52 71.04 35.48 72.74L37.24 72.58C37.6 71.9 37.94 71.18 38.24 70.5L39.78 71.38C38.8 73.34 37.62 75.34 36.36 77.02L39.54 76.42C39.5 76.9 39.5 77.62 39.54 77.98C35.1 78.94 34.42 79.18 34 79.44V79.42C33.9 79.04 33.64 78.24 33.44 77.78C33.86 77.7 34.28 77.22 34.8 76.52C35.1 76.14 35.7 75.26 36.36 74.16C34.58 74.38 34.14 74.5 33.86 74.64ZM39.24 79.58L39.56 80.98C37.56 81.86 35.5 82.74 33.86 83.46L33.42 81.64C34.86 81.18 37.04 80.38 39.24 79.58ZM41.78 78.28L43.5 78.78C42.7 80.46 41.52 82.38 40.3 83.62C39.98 83.32 39.38 82.86 39 82.64C40.14 81.5 41.24 79.7 41.78 78.28ZM42.52 75.58H45V73.52H43.34C43.06 74.24 42.78 74.94 42.52 75.58ZM50.72 77.26H46.8V82.54C46.8 83.48 46.6 83.96 46 84.22C45.38 84.5 44.42 84.54 43.06 84.54C42.98 84.06 42.74 83.32 42.52 82.86C43.44 82.88 44.38 82.88 44.64 82.86C44.92 82.86 45 82.78 45 82.52V77.26H42.58C41.68 77.26 40.96 77.38 40.78 77.58C40.68 77.22 40.42 76.4 40.2 75.96C40.6 75.84 40.92 75.16 41.34 74.16C41.4 74.02 41.5 73.8 41.58 73.52H40.14V71.82H42.16C42.34 71.28 42.52 70.72 42.66 70.1H39.8V68.4H43.12C43.32 67.64 43.48 66.88 43.6 66.1L45.44 66.4C45.3 67.06 45.12 67.74 44.94 68.4H51.08V70.1H44.48C44.3 70.68 44.12 71.26 43.92 71.82H46.8V75.58H50.72V77.26ZM47.88 78.98L49.34 78.32C50.18 79.7 51.22 81.58 51.74 82.68L50.18 83.48C49.7 82.36 48.7 80.42 47.88 78.98ZM65.42 76.38H70.62V71.54H65.42V76.38ZM58.3 76.38H63.48V71.54H58.3V76.38ZM65.42 69.72H72.56V79.28H70.62V78.2H65.42V84.56H63.48V78.2H58.3V79.38H56.44V69.72H63.48V66.24H65.42V69.72ZM29.44 107.78H34.1V106.28H29.44V107.68V107.78ZM26.88 104.1V105.1H36.44V104.1H26.88ZM26.88 102V102.98H36.44V102H26.88ZM26.88 99.96V100.9H36.44V99.96H26.88ZM31.18 97.12H28.12C28.38 97.56 28.64 97.98 28.78 98.32L27.52 98.78H31.68C31.26 98.5 30.62 98.16 30.24 98C30.58 97.76 30.9 97.46 31.18 97.12ZM34.82 97.12H33.42C33 97.76 32.5 98.32 32.02 98.78H36.02C35.76 98.3 35.28 97.66 34.82 97.12ZM40.42 107.78V109.28H36.02V112.56H34.1V109.28H29.14C28.62 110.54 27.26 111.74 24.12 112.6C23.86 112.18 23.36 111.58 22.94 111.22C25.22 110.72 26.4 110 26.98 109.28H22.66V107.78H27.56V107.64V106.28H25V98.78H27.1C26.92 98.3 26.56 97.68 26.22 97.12H25.44C24.88 97.92 24.28 98.62 23.68 99.18C23.36 98.88 22.6 98.38 22.18 98.14C23.42 97.14 24.52 95.64 25.18 94.12L26.9 94.58C26.74 94.94 26.56 95.26 26.38 95.62H31.22V97.08C32 96.22 32.66 95.18 33.04 94.12L34.84 94.52C34.7 94.9 34.52 95.26 34.34 95.62H40.3V97.12H36.9C37.22 97.52 37.52 97.92 37.7 98.22L36.48 98.78H38.4V106.28H36.02V107.78H40.42ZM49.08 101.76L48.06 103.3C47.28 102.64 45.66 101.72 44.38 101.14L45.36 99.74C46.62 100.24 48.26 101.1 49.08 101.76ZM46.64 112.34L45.04 111.08C46.12 109.58 47.66 107.02 48.82 104.74L50.18 105.98C49.14 108.06 47.82 110.46 46.64 112.34ZM45.48 95.8L46.52 94.44C47.78 94.96 49.44 95.84 50.28 96.48L49.2 98.02C48.42 97.32 46.78 96.38 45.48 95.8ZM62.4 111.72L60.72 112.56C60.56 112.02 60.28 111.38 59.9 110.68C52.84 111.54 51.94 111.7 51.34 111.98C51.24 111.58 50.92 110.64 50.7 110.12C51.12 110.02 51.5 109.56 51.98 108.82C52.4 108.26 53.54 106.32 54.44 104.2H50.32V102.44H55.26V99.2H51.16V97.44H55.26V94.22H57.16V97.44H61.42V99.2H57.16V102.44H62.18V104.2H56.66C55.74 106.18 54.6 108.14 53.48 109.74L59.04 109.14C58.56 108.34 58 107.54 57.48 106.8L58.98 106.08C60.36 107.82 61.82 110.14 62.4 111.72Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_3248_17059" x="15.2" y="0.2" width="53.6" height="53.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3248_17059"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3248_17059" result="shape"/>
</filter>
<filter id="filter1_d_3248_17059" x="6.55937" y="61.2996" width="70.8012" height="56.1" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3248_17059"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3248_17059" result="shape"/>
</filter>
</defs>
</svg>

<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g clip-path="url(#clip0_304_15672)">
<rect width="32" height="32" rx="4" fill="url(#paint0_linear_304_15672)"/>
<g id="Vector" filter="url(#filter0_d_304_15672)">
<path d="M25.9999 12.6386C25.9999 12.6135 25.9978 12.5862 25.9957 12.5611C25.9637 12.3034 25.9126 11.9576 25.853 11.79C25.689 11.3332 25.2566 11.0335 24.7475 11.0251H24.7241C24.1575 11.0251 23.6505 11.3856 23.4247 11.9513C23.3523 12.1336 23.2991 12.3935 23.2266 12.7518C23.0882 13.4328 22.8901 14.4176 22.4705 15.2768C22.0913 15.0442 21.6121 15.0085 21.1882 15.2076L17.2732 17.0348C16.9814 17.171 16.7258 17.3575 16.5128 17.5838C16.2955 17.347 16.0293 17.1522 15.7289 17.0097L11.8118 15.1825C11.3879 14.9855 10.9065 15.019 10.5295 15.2516C10.1099 14.3925 9.91181 13.4076 9.77336 12.7266C9.70094 12.3683 9.64769 12.1085 9.57526 11.9262C9.35161 11.3625 8.84253 11 8.27595 11H8.25251C7.74344 11.0084 7.30891 11.3101 7.14703 11.7648C7.08739 11.9346 7.03627 12.2782 7.00432 12.5359C7.00219 12.5611 7.00006 12.5883 7.00006 12.6135C7.00006 12.6554 6.9958 13.6528 7.06183 14.8158C7.15555 16.4565 7.33447 17.5356 7.60924 18.114C8.06081 19.0611 9.22168 20.0669 10.3421 21.0371C10.9001 21.519 11.4263 21.9758 11.7394 22.3258C11.963 22.5751 12.3805 23.2603 12.5914 23.6333C12.7171 23.8533 12.9492 23.979 13.1878 23.979C13.3007 23.979 13.4136 23.9518 13.518 23.8952C13.846 23.715 13.9653 23.3064 13.7821 22.9837C13.7182 22.8727 13.1559 21.8815 12.7618 21.4415C12.3912 21.0266 11.8331 20.5426 11.241 20.0313C10.3186 19.2329 9.17269 18.2397 8.8404 17.5461C8.48043 16.7897 8.35476 14.1662 8.36115 12.6617C8.36115 12.6554 8.36328 12.6512 8.36328 12.6449C8.38671 12.7518 8.41227 12.8775 8.4357 12.9907C8.60184 13.8142 8.883 15.1929 9.56887 16.3287C10.3825 17.676 11.7607 18.6106 12.1207 18.8411C12.3976 19.0611 12.7235 19.0276 12.9386 18.8893C13.2198 18.707 13.3241 18.3528 13.1835 18.0469C13.1367 17.9442 13.0621 17.8541 12.9727 17.7871C12.8257 17.632 12.4891 17.236 12.1569 16.8295L15.1432 18.2229C15.5521 18.4136 15.8141 18.8243 15.812 19.2706V21.6196C15.812 21.7369 15.8418 21.8459 15.8951 21.9402C16.0058 22.1623 16.238 22.3132 16.5064 22.3132C16.8834 22.3132 17.188 22.0135 17.188 21.6426V19.2937C17.1859 18.8495 17.4479 18.4367 17.8568 18.246L20.8431 16.8504C20.513 17.2548 20.1743 17.653 20.0273 17.808C19.9357 17.8751 19.8633 17.9652 19.8165 18.0679C19.6759 18.3738 19.7803 18.7279 20.0614 18.9102C20.2744 19.0485 20.6024 19.082 20.8793 18.862C21.2393 18.6315 22.6175 17.697 23.4311 16.3496C24.117 15.2139 24.396 13.8372 24.5643 13.0116C24.5877 12.8985 24.6133 12.7727 24.6367 12.6659C24.6367 12.6721 24.6389 12.6763 24.6389 12.6826C24.6431 14.1871 24.5196 16.8106 24.1596 17.5671C23.8294 18.2606 22.6814 19.256 21.7591 20.0522C21.169 20.5635 20.611 21.0475 20.2382 21.4624C19.8442 21.9025 19.2818 22.8915 19.2179 23.0047C19.0347 23.3274 19.154 23.736 19.482 23.9162C19.5864 23.9728 19.7014 24 19.8122 24C20.0508 24 20.2829 23.8764 20.4086 23.6564C20.6195 23.2834 21.037 22.5982 21.2606 22.3488C21.5759 21.9989 22.102 21.5421 22.6579 21.0601C23.7805 20.0878 24.9392 19.0841 25.3908 18.137C25.6655 17.5587 25.8445 16.4795 25.9382 14.8388C26.0042 13.68 25.9999 12.6805 25.9999 12.6386Z" fill="white"/>
<path d="M20.3259 9.64518C19.4271 8.85499 17.9665 8.85499 17.0677 9.64518L16.5299 10.118L15.9323 9.59264C15.0335 8.80245 13.5729 8.80245 12.6741 9.59264C11.7753 10.3828 11.7753 11.6669 12.6741 12.4571L14.8327 14.3548C14.9283 14.4389 15.0335 14.5166 15.1434 14.5839C15.5546 14.8613 16.0494 15 16.5442 15C17.1347 15 17.7251 14.8025 18.1745 14.4074L20.3259 12.5096C21.2247 11.7194 21.2247 10.4354 20.3259 9.64518ZM19.2454 11.5597L18.5857 12.1398C18.5857 12.1377 18.5833 12.1377 18.5833 12.1356L16.9673 13.4343C16.7044 13.6361 16.3243 13.655 16.0398 13.4932C16.0231 13.4806 16.0088 13.468 15.992 13.4553L14.4526 12.1166C14.4526 12.1166 14.4526 12.1187 14.4502 12.1187L13.7522 11.5051C13.6064 11.3769 13.5275 11.2067 13.5275 11.0238C13.5275 10.841 13.6064 10.6708 13.7522 10.5426C13.9028 10.4102 14.1012 10.3429 14.2996 10.3429C14.498 10.3429 14.6964 10.4102 14.847 10.5426L15.3777 11.0091L16.5275 12.0179L17.6773 11.0091L18.1482 10.5951C18.2988 10.4627 18.4972 10.3954 18.6956 10.3954C18.894 10.3954 19.0924 10.4627 19.243 10.5951C19.5466 10.862 19.5466 11.2928 19.2454 11.5597Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_304_15672" x="-0.4" y="1.6" width="33.8" height="29.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_304_15672"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_304_15672" result="shape"/>
</filter>
<linearGradient id="paint0_linear_304_15672" x1="-0.5" y1="-2.5" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#22AFFF"/>
<stop offset="0.550734" stop-color="#3665FF"/>
</linearGradient>
<clipPath id="clip0_304_15672">
<rect width="32" height="32" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>

<template>
  <div class="w-full h-full">
    <div
      class="title text-center h-[66px]"
      style="
        background-color: rgba(54, 101, 255, 0.1);
        line-height: 66px;
        color: #3665ff;
      "
    >
      {{ menuTitle }}
    </div>
    <div>
      <el-menu :default-active="$route.fullPath" router>
        <template v-for="item in menuList" :key="item.path">
          <subMenuListBox
            v-if="item.children && item.children.length > 0"
            :sub-menu-list="item.children"
          />
          <el-menu-item v-else :index="item.path">{{ item.name }}</el-menu-item>
        </template>
      </el-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouteStore } from '@/store/modules/route'
import subMenuListBox from './SubMenuListBox.vue'

const emit = defineEmits(['openingMenuList'])

const routeStore = useRouteStore()

const $route = useRoute()

const route = useRoute()

const menuTitle = computed(() => {
  return routeStore.haveMenuRoutes.find(
    (item: any) => item.path == '/' + route.fullPath.split('/')[1]
  ).name
})

const menuList = computed(() => {
  return routeStore.ChildrenMenuRoutes as any[]
})
</script>

<style lang="scss" scoped>
.active {
  background-color: rgba(54, 101, 255, 0.1);
}

.title {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
}

.menu-item {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #3665ff;
  cursor: pointer;
}

.el-menu {
  --el-menu-base-level-padding: 60px;
  --active-color: #3665ff;
  --el-menu-border-color: #fff;
  border-right: 0;
  .el-menu-item {
    height: 42px !important;
    margin: 10px 0 !important;
    &.is-active {
      background-color: rgba(54, 101, 255, 0.1) !important;
      color: #3665ff !important;
    }
  }

  :deep(.el-sub-menu__title) {
    height: 42px !important;
    margin: 10px 0 !important;
    .el-icon {
      display: none !important;
    }
  }
  .is-active {
    :deep(.el-sub-menu__title) {
      background-color: rgba(54, 101, 255, 0.1) !important;
      color: #3665ff !important;
    }
  }

  .el-menu--inline {
    > .is-active {
      background: #fff !important;
      color: #3665ff !important;
    }
  }
}
</style>

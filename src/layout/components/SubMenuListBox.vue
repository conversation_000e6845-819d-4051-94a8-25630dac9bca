<template>
  <template v-for="item in subMenuList" :key="item.path">
    <el-sub-menu
      v-if="item.children && item.children.length > 0"
      :index="item.path"
    >
      <template #title>
        <span>{{ item.name }}</span>
      </template>
      <template>
        <subMenuListBox :sub-menu-list="item.children" />
      </template>
    </el-sub-menu>
    <el-menu-item v-else :index="item.path">{{ item.name }}</el-menu-item>
  </template>
</template>

<script setup lang="ts">
defineProps<{
  subMenuList: any[]
}>()
</script>

<style scoped></style>

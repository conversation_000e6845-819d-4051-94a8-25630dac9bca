<!-- layout-head -->
<template>
  <div class="w-full h-full flex items-center justify-end relative header-box">
    <div class="flex flex-1 items-center h-full">
      <!-- logo -->
      <!-- <img
        class="logo w-[36px] h-[36px] mr-[8px]"
        src="@/assets/logo.png"
        alt=""
      /> -->
      <div class="app-name cursor-pointer min-w-[300px]">
        {{ systemTitle }}
      </div>
      <div class="flex w-full h-full flex-1">
        <div
          class="console mx-[24px] text-[20px] cursor-pointer min-w-[60px] relative df aic justify-center men-tit"
          :class="flag != '首页' ? '' : 'active'"
          @click="handleMenuClick({ path: '/home' }, '首页')"
        >
          <!-- <svg-icon name="menu-home" :color="flag != '首页' ? '#ffffff' : '#3665FF'"
            style="width: 20px; height: 20px; margin-right: 5px"></svg-icon> -->
          首页
        </div>
        <div
          class="console mx-[24px] h-full min-w-[60px] relative flex items-center"
          v-for="item in headerMenuList?.slice(0, 4)"
          :class="flag != item.name ? '' : 'active'"
          @click="handleMenuClick(item, item.name)"
        >
          <div
            class="men-tit h-[30px] w-full text-[20px] flex items-center justify-center cursor-pointer"
          >
            <!-- <svg-icon :name="item?.meta?.icon" :color="flag != item.name ? '#ffffff' : '#3665FF'"
              style="width: 20px; height: 20px; margin-right: 5px"></svg-icon> -->
            {{ item.name }}
          </div>
        </div>
        <div class="absolute left-[-25px] w-[300px] h-[90%] z-1">
          <img class="h-full ml-[0px]" src="@/assets/header_other.png" alt="" />
        </div>
        <div
          class="console mx-[24px] h-full min-w-[60px] relative flex items-center"
          v-for="item in headerMenuList?.slice(-4, -2)"
          :class="flag != item.name ? '' : 'active'"
          @click="handleMenuClick(item, item.name)"
        >
          <div
            class="men-tit h-30px w-full flex text-[20px] items-center justify-center cursor-pointer"
          >
            <!-- <svg-icon :name="item?.meta?.icon" :color="flag != item.name ? '#ffffff' : '#3665FF'"
              style="width: 20px; height: 20px; margin-right: 5px"></svg-icon> -->
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="time">
      <span class="text-[#DDD]">
        {{ yaer }}
      </span>
      <span class="px-1"></span>
      <span class="font-bold text-[16px]">
        {{ formattedTime }}
      </span>
    </div>
    <div
      class="h-[60px] relative flex items-center w-[100px]"
      style="margin: 0 -50px 0 -16px"
      v-for="item in headerMenuList?.slice(-2, -1)"
      @click="handleMenuClick(item, item.name)"
    >
      <div
        class="h-30px w-full flex items-center justify-center cursor-pointer"
      >
        <svg-icon
          name="icon_notification"
          style="width: 20px; height: 20px; margin-right: 5px"
        ></svg-icon>
      </div>
    </div>
    <!-- 导航栏尾部 用户模块区域 -->
    <div class="ml-[24px] flex items-center justify-end">
      <!-- 用户头像 name -->
      <div class="user-info h-[42px] flex items-center mr-16px">
        <!-- <el-dropdown>
          <span class="el-dropdown-link flex items-center justify-center">
            <img class="w-[42px] h-[42px] rounded-[50%]" :src="avatarUrl" alt="" />
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="changeSystem" v-if="platform_type">切换平台</el-dropdown-item>
              <el-dropdown-item @click="goToPersonalCenter">个人中心</el-dropdown-item>
              <el-dropdown-item @click="changePassword">修改密码</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
        <!-- <span class="name ml-[8px]">{{ userName }}</span> -->
        <el-menu
          default-active="0"
          class="bg-transparent h-[60px]"
          mode="horizontal"
          :ellipsis="false"
        >
          <el-sub-menu index="1" class="h-[60px]">
            <template #title>
              <span class="el-dropdown-link flex items-center justify-center">
                <img
                  class="w-[42px] h-[42px]"
                  src="@/assets/user-head.png"
                  alt=""
                />
              </span>
              <span class="name ml-[8px]">{{ userName }}</span>
            </template>
            <el-sub-menu index="2-1" v-if="platform_type">
              <template #title>当前角色:组织使用者</template>
              <el-menu-item index="2-1-1" @click="changeSystem"
                >切换为运营管理员</el-menu-item
              >
            </el-sub-menu>
            <el-menu-item index="2-2" @click="goToPersonalCenter"
              >个人中心</el-menu-item
            >
            <el-menu-item index="2-3" @click="changePassword"
              >修改密码</el-menu-item
            >
          </el-sub-menu>
        </el-menu>
      </div>
      <!-- 退出登陆 -->
      <div class="login-out cursor-pointer mr-[25px]" @click="loginOutEve">
        <!-- <img class="w-[20px] h-[20px]" src="@/assets/login-out.png" alt="" /> -->
        <svg-icon name="login-out" style="width: 20px; height: 20px"></svg-icon>
      </div>
    </div>

    <dialog-view
      ref="dialogViewRef"
      title="修改密码"
      width="20%"
      @confirm="changePassEve(dialogViewRef)"
    >
      <el-form
        :model="passForm"
        ref="passFormRef"
        :rules="passRules"
        label-width="auto"
        style="max-width: 600px"
      >
        <el-form-item label="原密码" prop="password">
          <el-input v-model="passForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newpassword">
          <el-input
            v-model="passForm.newpassword"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmpassword">
          <el-input
            v-model="passForm.confirmpassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>
    </dialog-view>
    <!-- 退出登录确认框 -->
    <del-dialog
      ref="loginOutRef"
      icon="wen"
      content="确定退出系统？"
      @confirm="logOut"
    >
    </del-dialog>
  </div>
  <!-- 弹窗 -->
</template>

<script setup lang="ts">
import delDialog from '@/components/DelDialog/index.vue'
import DialogView from '@/components/DialogView/index.vue'
import { useUserStore } from '@/store/modules/user'
import { useRouteStore } from '@/store/modules/route'
import { useLogin } from '@/hooks/login'
import dayjs from 'dayjs'
import { $getMenuList } from '@/api/menu'
import userHead from '@/assets/user-head.png'
import { Local } from '@/utils/storage'
import { pauseAllIntervals } from '@/utils/timerTools'
import { useNow } from '@vueuse/core'

const now = useNow()

const yaer = dayjs(new Date()).format('YYYY-MM-DD')

const formattedTime = computed(() => {
  const date = now.value
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${hours}:${minutes}:${seconds}`
})

const systemTitle = import.meta.env.VITE_SYS_NAME
const resourceUrl = import.meta.env.VITE_RESOURCE_URL

const { logOut, passRules, passForm, passFormRef, changePassEve } = useLogin()

let platform_type =
  JSON.parse(localStorage.getItem('userInfo') as any).platform_type.find(
    (item: any) => item.name == '运营平台'
  ) || null

function changeSystem(params: any) {
  if (window.$wujie) {
    pauseAllIntervals()
    window.$wujie?.props?.changeSys('/operationPlatform/login')
  }
}

const userName = ref('')

const avatarUrl = ref<string | undefined>()

watch(
  () => useUserStore().userBaseInfo,
  (value: any) => {
    userName.value = value.name
    avatarUrl.value = value.avatar
      ? `${resourceUrl}/admin/sys-file/oss/file?fileName=${value.avatar}`
      : userHead
  },
  {
    immediate: true,
    deep: true
  }
)

const $route = useRoute()
const router = useRouter()
console.log("🚀 ~ router:", router.getRoutes())

const routeStore = useRouteStore()

const routes = ref<Array<any>>([])

watch(
  () => $route.fullPath,
  async (value) => {
    let res: any = await $getMenuList()
    if (res.ok) {
      routes.value = res.data
      routeStore.setRoutes(routes.value)
    }
    routeStore.setBreadcrumbRoutes(value)
  },
  {
    immediate: true
  }
)

const headerMenuList = computed(() => {
  if (routes.value == null) {
    logOut()
    return
  }
  let headerRoute = routes.value?.reduce((arr: any, key: any) => {
    arr.push({
      path: key.path,
      name: key.name,
      meta: key.meta
    })
    return arr
  }, [])
  console.log('🚀 ~ headerRoute ~ headerRoute:', headerRoute)
  return headerRoute
})

const flag = computed(() => {
  const routePath = '/' + $route.fullPath.split('/')[1]
  if (routePath == '/home') return '首页'
  return headerMenuList.value.find((it: any) => it.path == routePath)?.name
})

const loginOutRef = ref<any>()

const loginOutEve = async () => {
  await nextTick()
  loginOutRef.value?.open()
}

const dialogViewRef = ref<any>(null)

function changePassword() {
  dialogViewRef.value.open()
}

const handleMenuClick = async (it: any, title: any) => {
  await nextTick()
  router.push(it.path)
}

const goToPersonalCenter = () => {
  router.push({ path: '/serverPersonalCenter' })
}
</script>

<style lang="scss" scoped>
.app-name {
  font-family: HuXiaoBo-NanShen, HuXiaoBo-NanShen;
  font-weight: bold;
  font-size: 24px;
  color: #fff;
  line-height: 36px;
  letter-spacing: 3px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  z-index: 2;
  left: 70px;
}

.header-box {
  background: #0c193d;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  color: #cdeaf0;
}

.console {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;
}

.men-tit {
  &:hover {
    color: #3665ff;
  }
}

.active {
  font-size: 20px;
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 500;
  color: #3665ff;
  &::after {
    background: url('@/assets/act_menu_icon.png') no-repeat center/cover;
    width: 88px;
    height: 23px;
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}

.user-info {
  // :deep(.el-dropdown-link:focus) {
  //   outline: none;
  // }
  :deep(.el-menu) {
    height: 60px;

    .name {
      color: #fff;
      font-size: 16px;
    }

    background-color: transparent;

    .el-sub-menu__title:hover {
      background-color: transparent !important;
    }

    .el-menu-item .is-active {
      color: #000 !important;
    }
  }
}

.act {
  color: #3665ff;
  font-weight: 600;
}

.time {
  @apply relative mr-4;
  &::after {
    content: '';
    position: absolute;
    display: block;
    width: 1px;
    height: 16px;
    top: 50%;
    right: -18px;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 50%;
  }
}
:deep(.el-menu--horizontal.el-menu) {
  border: none !important;
}
</style>

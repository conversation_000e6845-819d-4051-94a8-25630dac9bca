<template>
  <div class="cont" id="layout_container">
    <el-container class="w-full h-full">
      <el-header style="height: 80px !important; padding: 0 !important">
        <layoutHeader></layoutHeader>
      </el-header>
      <!-- 面包蟹 -->
      <div
        v-if="
          routeStore.getBreadcrumbRoutes &&
          routeStore.getBreadcrumbRoutes?.length > 0
        "
        class="h-[40px] w-full breadcrumb flex px-[40px] flex-shrink-0"
      >
        <div class="flex items-center text-[#ABB0C2]" style="line-height: 22px">
          当前位置：
        </div>
        <el-breadcrumb
          separator-icon="ArrowRight"
          class="h-[100%] flex items-center"
        >
          <el-breadcrumb-item
            v-for="(item, index) of routeStore.getBreadcrumbRoutes"
            :key="index"
            :class="{
              isLink: index !== routeStore.getBreadcrumbRoutes.length - 1
            }"
            @click="
              handleBreadcrumb(
                item,
                index == routeStore.getBreadcrumbRoutes.length - 1
              )
            "
            >{{ item.name }}</el-breadcrumb-item
          >
        </el-breadcrumb>
      </div>
      <el-container class="w-full" style="height: calc(100vh - 90px)">
        <el-aside
          v-if="showMenuList"
          width="186px"
          style="height: calc(100% - 40px)"
        >
          <div class="w-full h-full">
            <transition name="el-fade-in-linear">
              <MenuListBox></MenuListBox>
            </transition>
          </div>
        </el-aside>
        <el-main style="background-color: #f2f4f5">
          <router-view v-slot="{ Component }">
            <transition name="my-transition">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { autoAdapt } from '@/utils/Tools.js'
import layoutHeader from './components/LayoutHeader.vue'
import MenuListBox from './components/MenuListBox.vue'
import { useRouteStore } from '@/store/modules/route'

const routeStore = useRouteStore()

onMounted(() => {
  autoAdapt()
})

window.onresize = () => {
  try {
    autoAdapt()
  } catch (error) {
    console.log(error)
  }
}
const $route = useRoute()
const showMenuList = computed(() => {
  return routeStore.haveMenuRoutes
    ?.map((item: any) => item.path)
    .includes('/' + $route.fullPath.split('/')[1])
})

const $router = useRouter()

const handleBreadcrumb = (route: any, isLast: boolean) => {
  if (!isLast) {
    $router.push({ path: route.path })
  }
}
</script>

<style lang="scss" scoped>
.cont {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
}

.breadcrumb {
  border-bottom: 1px solid #e2e2e2;
  border-top: 1px solid #e2e2e2;
  :deep(.el-breadcrumb__inner) {
    color: #2274ff !important;
    cursor: pointer;
  }

  :deep(.isLink .el-breadcrumb__inner) {
    color: #a9a9a9 !important;
  }
}

.el-main {
  padding: 0 !important;
}

.my-transition-enter-active {
  transition: all 0.5s ease;
}
.my-transition-leave-active {
  transition: all 0.5s ease;
}

.my-transition-enter-from {
  opacity: 0;
}

.my-transition-enter-to {
  opacity: 1;
}

.my-transition-leave-from {
  opacity: 1;
}

.my-transition-leave-to {
  opacity: 0;
}

.my-transition-enter-active {
  transition-delay: 0.5s;
}
</style>

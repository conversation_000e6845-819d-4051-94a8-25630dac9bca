import { cloneDeep } from 'lodash-es'
import type { RouteRecordRaw } from 'vue-router'

// 引入views下所有vue文件
const viewsModules = import.meta.glob('@/views/**/*.vue')
console.log('🚀 ~ viewsModules:', viewsModules)

// 模板地址
const Layout = () => import('@/layout/index.vue')

const basicalRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: '登录',
    component: () => import('@/views/Login/index.vue')
  },
  {
    path: '/',
    redirect: '/home',
    name: 'Layout',
    component: Layout,
    children: [
      {
        path: '/home',
        name: '首页',
        component: () => import('@/views/Home/index.vue')
      }
    ]
  },
  {
    path: '/upgrade',
    name: '系统升级',
    component: () => import('@/views/upgrade/index.vue')
  },
  {
    name: 'Error 404',
    path: '/404',
    component: () => import('@/views/error/notFound.vue')
  }
]

// 加载路由信息
const loadRouteComponent = (route: any, url?: string) => {
  let path = ''
  let copyRoutePath = cloneDeep(route.path)
  if (!url) {
    path = copyRoutePath + '/index'
  } else {
    path = copyRoutePath
  }
  let obj = {} as any
  if (route.children && route.children.length > 0) {
    obj = {
      ...route,
      redirect: route.children[0].path,
      component: Layout,
      children: route.children.map((item: any) =>
        loadRouteComponent(item, route.path)
      )
    }
  } else {
    obj = {
      ...route,
      component: viewsModules[`/src/views${path}.vue`]
    }
  }
  return obj
}

// 初始化路由
export const initRoutes = (routes: any[]) => {
  const routePath: any[] = []
  routes?.reduce((arr, key) => {
    arr.push(loadRouteComponent(key))
    return arr
  }, routePath)
  return routePath
}

// 过滤目标路
export const removeNodesByPath = (tree: any[], path: string) => {
  const filterNodes = (nodes: any) => {
    return nodes.reduce((filtered: any, node: any) => {
      if (node.path === path) {
        return filtered
      }
      if (node.children && node.children.length > 0) {
        const filteredChildren = filterNodes(node.children)
        if (filteredChildren.length > 0) {
          filtered.push({ ...node, children: filteredChildren })
        } else {
          filtered.push({ ...node, children: [] })
        }
      } else {
        filtered.push(node)
      }
      return filtered
    }, [])
  }
  return filterNodes(tree)
}

export default basicalRoutes

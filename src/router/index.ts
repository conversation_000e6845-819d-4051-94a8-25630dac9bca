import {
  createRouter,
  createWebHashHistory,
  createWebHistory,
  RouterOptions,
  Router
} from 'vue-router'
// modules 目录用于定义各种路由，分开管理路由
import basicalRoutes, { initRoutes } from './utils/getRoutes'
import NProgress from '@/config/nprogress'
import { useRouteStore } from '@/store/modules/route'
import { Local } from '@/utils/storage'
import initSysData from '@/utils/initSysData.ts'

// 路由校验白名单
const whiteList: string[] = ['/login', '/upgrade', '/404']

/**
 * 路由实例
 */
const router: Router = createRouter({
  history: createWebHistory('/serviceWeb/'),
  routes: basicalRoutes
} as RouterOptions)

const flag = ref(true)

window.$wujie?.bus.$on('routeToLogin', (path: string) => {
  if (!path.includes('/servicePlatform')) {
    flag.value = false
  } else {
    flag.value = true
  }
})

router.beforeEach(async (to: any, from: any, next: any) => {
  let token = localStorage.getItem('token')
  if (window.$wujie) {
    if (to.path == '/login' && !token) {
      window.$wujie?.props?.jump('Login')
    }
    await initSysData()
  }

  const { isGetUserInfo, setUserInfoState, getChildrenMenuRoute } =
    useRouteStore()
  let routes = Local.get('routes')
  if (!NProgress.isStarted()) {
    NProgress.start()
  }
  if (to.name) {
    document.title =
      to.name == 'Error 404'
        ? to.name
        : `${import.meta.env.VITE_SYS_NAME}-` + to.name
  }
  if (!!token && flag.value) {
    await getChildrenMenuRoute('/' + to.path.split('/')[1])
    if (isGetUserInfo == false) {
      const routeArray = await initRoutes(routes)
      if (routeArray.length > 0) {
        routeArray.forEach((item) => {
          router.addRoute('Layout', item)
        })
        router.addRoute({
          path: '/:pathMatch(.*)*',
          redirect: '/404'
        })
      }
      if (window.$wujie) {
        router.removeRoute('login')
      }
      setUserInfoState(true)
      next({ ...to, replace: true })
    } else {
      next()
    }
  } else {
    if (whiteList.includes(to.path)) {
      next()
    } else {
      next('/login')
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

router.onError(() => {
  NProgress.done()
})

export default router

<template>
  <div class="w-full h-full flex flex-col">
    <el-table
      v-bind="$attrs"
      ref="tableRef"
      class="flex-1"
      @selection-change="selectChange"
      @select="selectBox"
      @select-all="selectAll"
    >
      <template v-for="(item, index) in tableTitle" :key="index">
        <!-- 选择框 -->
        <el-table-column
          :fixed="item.fixed || false"
          v-if="item.type && item.type == 'selection'"
          type="selection"
          :width="item.width"
        />
        <!-- 序号 -->
        <el-table-column
          :fixed="item.fixed || false"
          v-if="item.type && item.type == 'index'"
          type="index"
          :width="item.width || '80'"
          label="序号"
          :align="item?.align ? item.align : 'left'"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        ></el-table-column>
        <!-- Text列表 -->
        <el-table-column
          :fixed="item.fixed || false"
          :label="item.label"
          :sortable="item.sortable"
          :prop="item.prop"
          v-if="item.type && item.type == 'text'"
          :align="item?.align ? item.align : 'left'"
          :width="item?.width ? item?.width : ''"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        >
          <template #default="{ row }">
            <span
              v-if="
                (row[item.prop] == null || row[item.prop] == '') &&
                row[item.prop] != 0
              "
              >--</span
            >
            <span v-else>{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <!-- 时间列表 -->
        <el-table-column
          :fixed="item.fixed || false"
          :label="item.label"
          :sortable="item.sortable"
          :prop="item.prop"
          v-if="item.type && item.type == 'time'"
          :align="item?.align ? item.align : 'left'"
          :width="item?.width ? item?.width : ''"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        >
          <template #default="{ row }">
            <span
              v-if="
                (row[item.prop] == null || row[item.prop] == '') &&
                row[item.prop] != 0
              "
              >--</span
            >
            <span v-else>{{ format(row[item.prop]) }}</span>
          </template>
        </el-table-column>
        <!-- 状态栏列表 -->
        <el-table-column
          :fixed="item.fixed || false"
          :label="item.label"
          :sortable="item.sortable"
          :prop="item.prop"
          v-if="item.type && item.type == 'status'"
          :align="item?.align ? item.align : 'left'"
          :width="item?.width ? item?.width : ''"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        >
          <template #default="{ row }">
            <el-link
              :type="
                item.options.find((v: any) => v.value == row[item.prop])?.type
              "
              >{{
                item.options.find((v: any) => v.value == row[item.prop])?.text
              }}</el-link
            >
          </template>
        </el-table-column>
        <!-- switch -->
        <el-table-column
          :fixed="item.fixed || false"
          :label="item.label"
          :sortable="item.sortable"
          :prop="item.prop"
          v-if="item.type && item.type == 'switch'"
          :align="item?.align ? item.align : 'left'"
          :width="item?.width ? item?.width : ''"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        >
          <template #default="{ row }">
            <el-switch
              v-model="row[item.prop]"
              :loading="row.loading"
              style="
                --el-switch-on-color: #13ce66;
                --el-switch-off-color: #ff4949;
              "
              :active-value="item.activeValue || '0'"
              :inactive-value="item.inactiveValue || '9'"
              @change="(val: any) => item.click(val, row)"
            />
          </template>
        </el-table-column>
        <!-- 图片展示列表 -->
        <el-table-column
          v-if="item.type && item.type == 'image'"
          :fixed="item.fixed"
          :label="item.label"
          :width="item?.width ? item?.width : ''"
          :align="item?.align ? item?.align : 'left'"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        >
          <template #default="{ row }">
            <div
              v-if="row[item.prop]"
              style="display: flex; justify-content: left"
            >
              <div
                v-if="Array.isArray(row[item.prop])"
                style="
                  display: flex;
                  flex-wrap: wrap;
                  justify-content: center;
                  width: 100%;
                "
              >
                <el-image
                  style="width: 50px; margin: 5px"
                  v-for="it in row[item.prop]"
                  @click="
                    handlePictureCardPreview(
                      baseUrl + '/admin/sys-file/oss/file?fileName=' + it
                    )
                  "
                  :style="item.style"
                  :src="baseUrl + '/admin/sys-file/oss/file?fileName=' + it"
                />
              </div>
              <div v-else>
                <el-image
                  @click="
                    handlePictureCardPreview(
                      baseUrl +
                        '/admin/sys-file/oss/file?fileName=' +
                        row[item.prop]
                    )
                  "
                  :style="item.style"
                  :src="
                    baseUrl +
                    '/admin/sys-file/oss/file?fileName=' +
                    row[item.prop]
                  "
                />
              </div>
            </div>
            <span v-else style="font-size: 10px">暂未上传图片</span>
          </template>
        </el-table-column>
        <!-- 动态自定义内容列表 -->
        <el-table-column
          v-if="item.type && item.type == 'custom'"
          :fixed="item.fixed"
          :label="item.label"
          :width="item?.width ? item?.width : ''"
          :align="item?.align ? item?.align : 'left'"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        >
          <template #default="{ row, column, $index }">
            <slot :name="item.name" :data="{ row, column, $index }"></slot>
          </template>
        </el-table-column>
        <!-- 操作列表 -->
        <el-table-column
          v-if="item.type && item.type == 'operate'"
          :fixed="item.fixed"
          :label="item.label"
          :width="item?.width ? item?.width : ''"
          :align="item?.align ? item?.align : 'left'"
          :headerAlign="item.headerAlign ? item.headerAlign : 'left'"
        >
          <template #default="{ row, column }">
            <el-button
              @click="() => v.click(row, column)"
              :type="v.type"
              :size="v.size ? v.size : 'mini'"
              v-for="(v, i) in item.actions.filter((v: any) => !v.isLink)"
              :key="i"
            >
              {{ v.name }}
            </el-button>
            <template
              v-for="(v, i) in item.actions.filter((v: any) => v.isLink)"
              :key="i"
            >
              <div
                style="margin: 0 5px; display: inline-block"
                class="father pr-2"
                v-permission-button="'1'"
                v-if="v.name !== '删除' && v.name !== '触发器'"
              >
                <el-link
                  @click="() => v.click(row, column)"
                  :type="v.type"
                  :style="v.style ? v.style(row) : null"
                >
                  {{ v.name }}
                </el-link>
              </div>
              <!-- 触发器单独处理 -->
              <div
                style="margin: 0 5px; display: inline-block"
                v-if="v.name !== '删除' && v.name == '触发器'"
              >
                <el-link
                  @click="() => v.click(row, column)"
                  :type="v.type"
                  :style="v.style"
                >
                  {{ v.options.find((f: any) => f.value == row[v.prop])?.text }}
                </el-link>
              </div>
              <!-- 删除 -->
              <div
                style="margin: 0 5px; display: inline-block"
                v-if="v.name == '删除' && (v.showDel ? v.showDel(row) : true)"
              >
                <el-link
                  @click="() => openDelDialog(row)"
                  :type="v.type"
                  :style="v.style"
                >
                  {{ v.name }}
                </el-link>
              </div>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页栏 -->
    <div
      v-if="showPagination && pageConfig"
      class="pageConfig flex-shrink-0 h-60px"
    >
      <el-pagination
        :current-page="pageConfig.currentPage"
        :page-size="pageConfig.pageSize"
        :page-sizes="pageConfig.pageSizes"
        :small="pageConfig.small || false"
        :disabled="pageConfig.disabled || false"
        :background="pageConfig.background"
        :layout="pageConfig.layout"
        :total="parseInt(pageConfig.total as any, 10)"
        @size-change="(e: any) => pageConfig?.handleSizeChange(e)"
        @current-change="(e: any) => pageConfig?.handleCurrentChange(e)"
      />
    </div>

    <!-- 图片预览 -->
    <el-dialog v-model="dialogVisible">
      <div style="width: 90%; margin: 0 auto">
        <img style="width: 100%" :src="dialogImageUrl" alt="预览图片" />
      </div>
    </el-dialog>
    <!-- 删除弹窗 -->
    <auto-del
      ref="delRef"
      :delMethod="props?.delMethod"
      :refreshMethod="props?.refreshMethod"
    />
  </div>
</template>

<script setup lang="ts">
import autoDel from '@/components/DelDialog/autoDel.vue'
import { useAttrs } from 'vue'
import { format } from '@/utils/dayjs'

const $attr = useAttrs()

export interface PageConfigType {
  currentPage: number
  pageSize: number
  pageSizes: Array<number>
  layout: string
  total: number | string
  background: boolean
  disabled?: boolean
  small?: boolean
  handleSizeChange: Function | any
  handleCurrentChange: Function | any
  [key: string]: any
}
const baseUrl = import.meta.env.VITE_RESOURCE_URL
const props = withDefaults(
  defineProps<{
    tableTitle: Array<Object | any>
    showPagination?: boolean
    // 删除方法
    delMethod?: Function
    // 刷新方法
    refreshMethod?: Function
    // 删除参数特殊处理(不传默认是当前行的id)
    delParams?: Function
    pageConfig?: Partial<PageConfigType>
  }>(),
  {
    showPagination: true
  }
)

// 删除弹窗实例
const delRef = ref<any>(null)

// 打开删除弹窗
const openDelDialog = (row: any) => {
  // 处理删除弹窗参数
  let param = null
  // 特殊处理
  if (props.delParams) {
    param = props.delParams(row)
  } else {
    // 否则默认传id
    param = row.id
  }
  delRef.value.open(param)
}

const { pageConfig } = toRefs(props)

// 图片预览
const dialogVisible = ref<boolean>(false)
const dialogImageUrl = ref<string>('')

const handlePictureCardPreview = (url: string) => {
  dialogImageUrl.value = url
  dialogVisible.value = true
}

const emits = defineEmits(['selectChange', 'selectBox', 'selectAll'])
const selectChange = (val: any) => {
  emits('selectChange', val)
}
const selectBox = (selection: any, row: any) => {
  emits('selectBox', selection, row)
}
const selectAll = (selection: any) => {
  emits('selectAll', selection)
}

const tableRef = ref<any>()

watch(
  () => $attr.data,
  (val: any) => {
    if (props.tableTitle.find((it: any) => it.type == 'switch')) {
      val.forEach((item: any) => {
        item.loading = false
      })
    }
  }
)

// 获取table数据
const tableRefData = () => {
  const data: any = {}
  const entries = Object.entries(tableRef.value as Array<Object>)
  for (const [key, value] of entries) {
    data[key] = value
  }
  return data
}

const clearSelectionFun = () => {
  tableRef.value?.clearSelection()
}

const toggleRowSelectionFun = (row: any, boolean: boolean) => {
  tableRef.value?.toggleRowSelection(row, boolean)
}

defineExpose({
  clearSelectionFun,
  toggleRowSelectionFun,
  tableRefData
})
</script>

<style lang="scss" scoped>
.pageConfig {
  width: 100%;
  margin-top: 15px;
  display: flex;
  justify-content: right;
}

.el-table {
  width: 100%;

  :deep(.is-leaf) {
    background-color: #f8f8f8;
    color: #333;
  }

  :deep(.el-table__cell) {
    //     text-align: left;
    // text-align: left !important;
  }
}
:deep(.left) {
  text-align: left !important;
}
:deep(.left) {
  text-align: left !important;
}

:deep(.is-leaf) {
  background-color: #f8f8f8 !important;
}

.el-pagination {
  :deep(.is-active) {
    background-color: #e5eeff !important;
    color: #3665ff !important;
  }
  :deep(.el-pagination__total) {
    font-size: 16px !important;
    font-weight: 600 !important;
  }
}

.el-table {
  --el-table-header-bg-color: #f8f8f8 !important;
  .father {
    position: relative;
    .borr {
      &::after {
        content: '';
        position: absolute;
        height: 8px;
        width: 1px;
        right: 1px;
        top: 50%;
        border-right: 1px solid #cccccc;
        transform: translateY(-50%);
      }
    }
  }
}
</style>

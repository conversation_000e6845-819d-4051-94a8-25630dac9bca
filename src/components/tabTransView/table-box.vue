<template>
  <div class="w-full h-full flex flex-col">
    <!-- <title-box :num="isSelectNum ? DefaultSelect.length : tableTotal" :title="tableTitle" /> -->
    <!-- 搜索 -->
    <div class="w-full h-66px flex items-center">
      <slot name='search' :tableObj="tableProps" />
    </div>
    <!-- 表格 -->
    <div class="w-full flex-1 overflow-hidden">
      <table-view ref="tableRef" :tableData="tableData" :total="tableTotal" :currentPage="tablePagination.current"
        :max-height="props.maxHeight ? props.maxHeight : null" :reserveSelection="true" :pageSize="tablePagination.size"
        @handleSizeChange="handleSizeChange" :rowKey="props.rowKey" @handleCurrentChange="handleCurrentChange"
        @handleSelectBox="handleSelectBox" @handleSelectionChange="handleSelect" v-loading="tableLoading"
        :loadingBgColor="loadingBgColor" @clearSelection="clearSelectListEve" style="padding:  0 0 30px 0">
        <!-- 插槽 -->
        <slot />
      </table-view>
    </div>
  </div>
</template>

<script setup>
import TableView from '@/components/tableView/index.vue'
import { ref, onMounted, watch } from 'vue'
import { useTableData } from '@/hooks/tableData'
import titleBox from './title-box.vue'
import { rowKey } from 'element-plus/es/components/table-v2/src/common';

const props = defineProps({
  // 表格请求接口 必须传递
  apiFn: {
    type: Function,
    required: true,
  },
  tableTitle: {
    type: String,
    default: '待分配'
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  maxHeight: {
    type: [Number, String],
    default: null,
    validator: (value) => {
      if (value === null) return true;
      // 允许数字或者带px的字符串
      return !isNaN(value) || /^\d+px$/.test(value);
    }
  },
  isSelectNum: {
    type: Boolean,
    default: false
  },
  DefaultSelect: {
    type: Array,
    default: []
  }
})

const tableRef = ref(null)

// 表格数据相关hooks
const {
  tableData,
  selectList,
  tableTotal,
  tablePagination,
  addReqParams,
  getTableData,
  handleSizeChange,
  handleCurrentChange,
  handleSelect,
  tableLoading,
  loadingBgColor,
  clearEve,
  resetTable,
} = useTableData({
  reqFn: props.apiFn,
  fn: (res) => {
    tableData.value = res.data.records
    console.log(tableData.value)
    tableTotal.value = res.data.total - 0;
  },
});

const tableProps = ref({
  tableData,
  selectList,
  tableTotal,
  tablePagination,
  addReqParams,
  getTableData,
  handleSizeChange,
  handleCurrentChange,
  tableLoading,
  clearEve,
  resetTable,
  loadingBgColor,
})

watch(() => props.apiFn, () => {
  getTableData(tablePagination.value)
},
  {
    immediate: true
  }
)

watch(() => tablePagination.value, (val) => {
  tableProps.value.tablePagination = val
})

watch(tableData, async (val) => {
  await nextTick()
  tableRef.value.setSelection(props.DefaultSelect)
})

// 清除多选
const clearSelectListEve = () => {
  tableRef.value.clearSelection()
}

// 清除某一项选中状态
const clearRowSelection = (row, type = false) => {
  tableRef.value.clearRowSelection(row, type)
}

const $emit = defineEmits(['handleSelectBox'])


const handleSelectBox = async (selection, row, allList) => {
  $emit('handleSelectBox', selection, row, allList)
}

onMounted(() => {
})

defineExpose({
  getTableData,
  tablePagination,
  selectList,
  clearSelectListEve,
  clearRowSelection
})
</script>

<style lang="scss" scoped></style>

<!-- 双表格穿梭框 -->
<template>
  <div class="w-full h-full flex" :style="{height: props.height}">
    <!-- 树 -->
    <div class="tree-box h-full w-181px mr-6 flex-shrink-0" v-if="$slots.tree">
      <slot name='tree'/>
    </div>
    <div class="left flex-1 h-full overflow-hidden">
      <!-- 表格 -->
      <div class="table-box h-full w-full overflow-hidden border-1 p-2">
        <slot name='left-table' />
      </div>
    </div>
    <!-- 按钮 -->
    <div class="w-[56px] flex fdc aic jcc flex-shrink-0">
      <!-- <img src="@/assets/tree_right_arrow.png" class="w-[32px] cursor-pointer" @click="moveRight"/> -->
      <el-button type="primary" :icon="ArrowRightBold" @click="moveRight" :loading="rigthLoading"/>
      <div style="width: 20px; height: 20px"></div>
      <!-- <img src="@/assets/tree_left_arrow_white.png" class="h-[32px] cursor-pointer" @click="moveLeft" /> -->
      <el-button :icon="ArrowLeftBold" @click="moveLeft" :loading="leftLoading"/>
    </div>
    <div class="right flex-1 h-full overflow-hidden">
      <!-- 表格 -->
      <div class="table-box h-full w-full overflow-hidden border-1 p-2">
        <slot name='right-table'/>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useSlots } from 'vue'
import {
  ArrowLeftBold,
  ArrowRightBold
} from '@element-plus/icons-vue'
const props = defineProps({
  height: {
    type: String,
    default: '500px'
  }
})

const $emit = defineEmits(['moveEvent'])

// 右侧表格加载状态
const rigthLoading = ref(false)
// 左侧表格加载状态
const leftLoading = ref(false)

// 查看插槽
const $slots = useSlots()

// 左右表格切换
const moveRight = () => {
  $emit('moveEvent', 'right')
}
const moveLeft = () => {
  $emit('moveEvent', 'left')
}

defineExpose({
  rigthLoading,
  leftLoading
})
</script>

<style lang="scss" scoped>
</style>

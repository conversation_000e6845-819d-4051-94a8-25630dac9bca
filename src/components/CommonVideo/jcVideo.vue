<!-- 杰西卡布视频播放组件 -->
<template>
  <div class="cc relative z-9999 w-full h-full bg-[#000]">
    <div class="card-body vidd" :key="kyy" :class="kyy" :style="{width: width, height: height}"></div>
    <!-- 加载动画 -->
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted, watch, nextTick } from "vue";
import { v4 as uuidv4 } from "uuid";
import { $getVideoPlayUrl } from "@/api/videoCenter";
import { success, warning } from "@/utils/toast";

const kyy = ref('cc' + uuidv4());
const props = defineProps({
  //视频流地址
  srcObj: {
    type: Object,
    required: true,
  },
  width: {
      type: String,
      default: "100%"
  },
  height: {
      type: String,
      default: "100%"
  },
  sn: {
    type: String,
    default: ""
  }
})
let vidd = null
const jessibuca = ref(null)
const videoInfo = ref({
  width: 0,
  height: 0,
  framerate: 0,
  videodatarate: 0,
  encoder: '-',

})

const $emit = defineEmits(['instandeEve', 'dataInfo', 'loadingEve', 'kbpsEve', 'playingEve', 'timeOut', 'playOrPause'])
const init = () => {
  jessibuca.value = new Jessibuca({
    container: vidd,
    // videoBuffer: 3,
    // todo
    isResize: true,
    // hiddenAutoPause: true,
    loadingText: "加载中",
    hasAudio: true,
    useWCS: true,
    decoder: "/serviceWeb/decoder.js",
    wcsUseVideoRender: false,
    isNotMute: false,
    isFlv: true
  });

  // jessibuca.value.on("load", function () {
  //   console.log('231231313123123')
  //   // 开始加载了 加载中 出现加载动画
  //   $emit('loadingEve', true)
  // })
  $emit('loadingEve', true)
  if(jessibuca.value.loaded){
    $emit('loadingEve', true)
  }
  else {
    jessibuca.value.on("load", function () {
      $emit('loadingEve', true)
    })
  }
  jessibuca.value.on("start", function () {
    // isLoading.value = false
    $emit('loadingEve', false)
    $emit('playingEve', true)
  })
  jessibuca.value.on("timeout", function (error) {
    $emit('timeOut')
    jessibuca.value.destroy()
    jessibuca.value = null
  })
  jessibuca.value.on("videoInfo", function (data) {
    videoInfo.value.width = data.width
    videoInfo.value.height = data.height
    videoInfo.value.encoder = data.encType
  })
  jessibuca.value.on("stats", function (s) {
    videoInfo.value.framerate = s.fps
    videoInfo.value.videodatarate = s.vbps
  })
  jessibuca.value.on("pause", function () {
  });
  jessibuca.value.on("play", function () {
  });
  jessibuca.value.on("error", function (error) {
    $emit('timeOut')
    jessibuca.value.destroy()
    jessibuca.value = null
  })
  jessibuca.value.on("kBps", function (data) {
    $emit('kbpsEve', data)
  })
}

watch(() => videoInfo.value, (newVal, oldVal) => {
  $emit('dataInfo', newVal)
},
{
  deep: true
}
)

  
const setVideoUrl = async () => {
  if (!props.sn || props.sn == 0) {
    videoUrl.value = "";
    return;
  }
  try {
    const res = await $getVideoPlayUrl({ sn: props.sn });
    if (res.code !== 0) {
      if (res.code === 403) {
        warning("错误的视频地址！");
      }
      return;
    }

    videoUrl.value = res.data.url;

    if (videoUrl.value) {
      if (!jessibuca.value) {
        vidd = document.querySelector(`.${kyy.value}`);
        init();
      } else {
        await jessibuca.value.destroy();
        jessibuca.value = null;
        init();
      }
      jessibuca.value.play(videoUrl.value);
    }
  } catch (error) {
    if(error.message.includes("Jessibuca")) {
      warning("Jessibuca 加载失败，请检查是否引入成功！");
    } else {
      warning("获取视频地址失败，请稍后再试！");
    }
  }
};


const videoUrl = ref(null)

watch(props, () => {
  setVideoUrl();
}, { deep: true });

// 播放器暂停事件
const pauseVideo = () => {
  if(jessibuca.value) {
    jessibuca.value.pause().then(() => {
    }).catch((e) => {
        console.log('pause error', e);
    })
  }
}

// 播放器播放事件
const playVideo = () => {
  if(jessibuca.value) {
    jessibuca.value.play()
  }
}

// 播放器静音事件
const muteVideo = (val) => {
  console.log("🚀 ~ muteVideo ~ val:", val)
  if(jessibuca.value) {
    if(val) {
      jessibuca.value.mute()
      return
    }
    jessibuca.value.cancelMute()
  }
}

// 视频旋转事件
const rotateVideo = (val) => {
  if(jessibuca.value) {
    jessibuca.value.setRotate(val)
  }
}

// 视频截图事件
const screenshotEve = () => {
  if(jessibuca.value) {
    jessibuca.value.screenshot()
  }
}

onMounted(()=> {
  setVideoUrl()
})

onUnmounted(async ()=> {
  if(!!jessibuca.value) {
    await jessibuca.value.destroy()
    jessibuca.value = null
  }
})

defineExpose({
  pauseVideo,
  playVideo,
  muteVideo,
  rotateVideo,
  screenshotEve,
  jessibuca,
})
</script>

<style lang="scss" scoped>
.vidd {
  width: 100%;
  height: 100%;
}
  .self_data {
  }
  .showMess {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%);
  }

</style>
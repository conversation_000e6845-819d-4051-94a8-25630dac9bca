<template>
  <div :id="playerId" class="player-container w-full h-full">
    <!-- 没有 src 时的封面 -->
    <!-- <div v-if="!props.src" class="no-src cover flex justify-center items-center">
    </div> -->
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'
import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  }
})

const playerId = uuidv4()
const player = ref(null)

watch(
  () => props.src,
  (newVal) => {
    console.log('src changed', newVal)
    if (player.value) {
      player.value.src = newVal
      player.value.play()
    }
    if (!newVal) {
      player.value.pause()
    }
  }
)

onMounted(() => {
  player.value = new Player({
    id: playerId,
    url: props.src,
    width: props.width,
    // fluid: true,
    height: props.height,
    lang: 'zh-cn',
    playbackRate: props.playbackRate || [0.5, 0.75, 1, 1.25, 1.5, 2, 5, 10, 15],
    closeVideoClick: true,
    cssFullscreen: false
  })
})

onUnmounted(() => {
  if (player.value) {
    console.log('destroy player')
    player.value.destroy() // 清理播放器实例，避免内存泄漏
  }
})
</script>

<style lang="scss" scoped>
.player-container {
  position: relative;
  .no-src {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: url('../../assets/noVideo.png') no-repeat center / 100% 100%;
    background-color: #d9d9d9;
    z-index: 999;
  }
}
</style>

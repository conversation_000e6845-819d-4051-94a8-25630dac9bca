<template>
  <div
    class="w-full h-full flex flex-col items-center justify-center"
    v-loading="loading"
  >
    <div class="display_single w-full h-full" ref="cjj">
      <canvas class="cc" :id="conta"></canvas>
    </div>
    <!-- <div class="flex-shrink-0"> -->
    <!-- <button @click="startConnect">开始</button>
      <button @click="stopConnect">停止</button>
      <select v-model="frameRate">
        <option v-for="rate in frameRates" :key="rate" :value="rate">{{ rate }}帧</option>
      </select>
      <button @click="queryGbDevice">查询国标设备</button> -->
    <!-- </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
// import WsStreamClient from '@/utils/video';
// import WsStreamClient from '@/utils/webSocket';
import WsStreamClient from '@/utils/wsclient'
import { v4 as uuidv4 } from 'uuid'

import { useResizeObserver } from '@vueuse/core'

const loading = ref(true)

const cjj = ref(null)

const conta = 'cont' + uuidv4()

// 获取当前视频区域高宽 不然 canvas 默认高宽是 300 150 画出来会模糊
// useResizeObserver(cjj, (entries) => {
//   console.log(entries)
//   const entry = entries[0]
//   const { width, height } = entry.contentRect
// })

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

const $emit = defineEmits(['infoCallback'])

// // 帧数选择
// const frameRates = ref([30, 45, 60, 90, 120]);
// // 默认帧数
// const frameRate = ref(frameRates.value[0]);

// 视频播放器实例
// let client = null;

// ws 实例
let client = null

// 开始连接
const startConnect = (data) => {
  console.log('23232323232323232')
  if (!client) {
    console.log('232323233333333')
    const canvas = document.getElementById(conta)
    client = new WsStreamClient(canvas, openLoading, null, closeLoading)
    
    client.liveview(data.ip, data.port, data.sn)
    client.onVideoInfo(videoInfoCallback)
    console.log("🚀 ~ startConnect ~ data:", data)
  }

  function openLoading() {
    loading.value = false
    // console.log('关闭加载',Date.now())
  }

  function closeLoading() {
    loading.value = true
    // console.log('开始加载',Date.now())
  }

  function videoInfoCallback(data) {
    // console.log('视频信息', data);
    $emit('infoCallback', data)
  }
  // if(!!window.VideoDecoder) {
  //   console.log(props.data)
  //   const canvas = document.getElementById(conta);
  //   canvas.width = props.width;
  //   canvas.height = props.height - 36;
  //   if (!client) {
  //     client = new WsStreamClient(canvas);
  //   }
  //   // 播放
  //   client.liveview("************", port, token);
  // } else {
  //   alert('不支持VideoDecoder！！');
  // }
}

// 停止连接
const stopConnect = () => {
  if (client) {
    client.shutdown()
    client = null
    loading.value = true
  }
}

// 查询GB设备
const queryGbDevice = () => {
  fetch('http://192.168.0.201:8088/syncgbdevice', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      GBDeviceID: '53230000001180000001'
    })
  })
    .then((res) => res.json())
    .then((data) => {
      console.log(data)
    })
}

// watch(() => props.data, () => {
//   if(!!props.data.token) {
//     console.log('启动');
//     // startConnect()
//   }
// },{deep: true})

// // 初始化
// const init = () => {
//   const container = document.getElementById(conta);
//   container.style.display = 'flex';
//   container.style.alignItems = 'center';
//   container.style.justifyContent = 'center';
// }

onMounted(() => {})

onUnmounted(() => {
  console.log('销毁vIDEO')
  stopConnect()
})

defineExpose({
  startConnect,
  stopConnect,
  queryGbDevice
})
</script>

<style scoped>
.display_single {
  position: relative;
  width: 100%;
  height: 100%;
  .cc {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
</style>

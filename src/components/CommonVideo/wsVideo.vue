<template>
  <div>
    <video
      id="videoElement"
      class="video-js vjs-default-skin"
      width="640"
      height="360"
      controls
    ></video>
    <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import videojs from 'video.js' // 引入 Video.js
import 'video.js/dist/video-js.css' // 引入 Video.js 样式
import WsStreamClient from '@/utils/webSocket' // 确保导入路径正确

const errorMessage = ref('')
const proxyIP = '************' // 替换为实际代理IP
const proxyPort = '5102' // 替换为实际代理端口
const token = 'd2cUAotTlrBwnDft' // 替换为实际token

let videoPlayer = null
let mediaSource = null
let sourceBuffer = null
let bufferQueue = []
let wsClient = null

const initializeVideoPlayer = () => {
  videoPlayer = videojs('videoElement', {
    autoplay: true,
    controls: true,
    sources: []
  })

  mediaSource = new MediaSource()
  videoPlayer.src({ src: URL.createObjectURL(mediaSource), type: 'video/mp4' })

  mediaSource.addEventListener('sourceopen', () => {
    console.log('MediaSource opened')
    try {
      sourceBuffer = mediaSource.addSourceBuffer(
        'video/mp4; codecs="avc1.4d1029"'
      )
      sourceBuffer.mode = 'sequence'
      sourceBuffer.addEventListener('updateend', processBufferQueue)

      wsClient.liveview(proxyIP, proxyPort, token, handleWebSocketMessage)
    } catch (e) {
      console.error('Error adding source buffer:', e)
      handleError('初始化SourceBuffer时出错。')
    }
  })

  mediaSource.addEventListener('sourceended', () => {
    console.log('MediaSource ended')
  })

  mediaSource.addEventListener('sourceclose', () => {
    console.log('MediaSource closed')
  })
}

const handleWebSocketMessage = (data) => {
  if (!sourceBuffer || !mediaSource) return
  let bytes = new Uint8Array(data)
  let lenBytes = bytes.subarray(0, 2)
  let headLen = new Uint32Array(lenBytes)[0]
  let headBytes = bytes.subarray(2, headLen + 2)
  let bodyBytes = bytes.subarray(headLen + 2)
  const strJson = new TextDecoder().decode(headBytes)
  let packa = JSON.parse(strJson)
  packa['body'] = bodyBytes
  const { type, ...other } = packa
  console.log('WebSocket message received:', type, other)
  bufferQueue.push(packa.body)
  processBufferQueue()
}

const processBufferQueue = () => {
  if (!sourceBuffer || !mediaSource) return

  if (
    bufferQueue.length > 0 &&
    !sourceBuffer.updating &&
    mediaSource.readyState === 'open'
  ) {
    const buffer = bufferQueue.shift()
    try {
      console.log('Appending buffer, buffer queue length:', bufferQueue.length)
      sourceBuffer.appendBuffer(buffer)
    } catch (error) {
      console.error('追加缓冲区数据出错:', error)
      handleError('视频流处理错误，请检查网络。')
    }
  }
}

const handleError = (message) => {
  errorMessage.value = message
}

onMounted(() => {
  wsClient = new WsStreamClient()
  initializeVideoPlayer()
})

onBeforeUnmount(() => {
  if (sourceBuffer && mediaSource) {
    sourceBuffer.removeEventListener('updateend', processBufferQueue)
    if (mediaSource.readyState === 'open') {
      mediaSource.endOfStream()
    }
  }
  wsClient.shutdown()
  if (videoPlayer) {
    videoPlayer.dispose()
  }
})
</script>

<style scoped>
.error-message {
  color: red;
}
</style>

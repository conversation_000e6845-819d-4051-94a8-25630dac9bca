<template>
  <div :style="{ width: width, height: height }">
    <div ref="lMap" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script setup>
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import 'leaflet.markercluster/dist/MarkerCluster.css'
import 'leaflet.markercluster/dist/MarkerCluster.Default.css'
import { MarkerClusterGroup } from 'leaflet.markercluster'
import { type } from 'windicss/utils'

const props = defineProps({
  // props
  mapLng: {
    type: Number,
    default: null
  },
  mapLat: {
    type: Number,
    default: null
  },
  width: {
    type: String,
    default: '800px'
  },
  height: {
    type: String,
    default: '400px'
  },
  type: {
    type: String,
    default: 'point'
  }
})

const $emit = defineEmits(['mapLeftClick', 'update:mapLat', 'update:mapLng'])

//坐标转换
L.CoordConver = function () {
  /**百度转84*/
  this.bd09_To_gps84 = function (lng, lat) {
    var gcj02 = this.bd09_To_gcj02(lng, lat)
    var map84 = this.gcj02_To_gps84(gcj02.lng, gcj02.lat)
    return map84
  }
  /**84转百度*/
  this.gps84_To_bd09 = function (lng, lat) {
    var gcj02 = this.gps84_To_gcj02(lng, lat)
    var bd09 = this.gcj02_To_bd09(gcj02.lng, gcj02.lat)
    return bd09
  }
  /**84转火星*/
  this.gps84_To_gcj02 = function (lng, lat) {
    var dLat = transformLat(lng - 105.0, lat - 35.0)
    var dLng = transformLng(lng - 105.0, lat - 35.0)
    var radLat = (lat / 180.0) * pi
    var magic = Math.sin(radLat)
    magic = 1 - ee * magic * magic
    var sqrtMagic = Math.sqrt(magic)
    dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * pi)
    dLng = (dLng * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * pi)
    var mgLat = lat + dLat
    var mgLng = lng + dLng
    var newCoord = {
      lng: mgLng,
      lat: mgLat
    }
    return newCoord
  }
  /**火星转84*/
  this.gcj02_To_gps84 = function (lng, lat) {
    var coord = transform(lng, lat)
    var lontitude = lng * 2 - coord.lng
    var latitude = lat * 2 - coord.lat
    var newCoord = {
      lng: lontitude,
      lat: latitude
    }
    return newCoord
  }
  /**火星转百度*/
  this.gcj02_To_bd09 = function (x, y) {
    var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi)
    var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi)
    var bd_lng = z * Math.cos(theta) + 0.0065
    var bd_lat = z * Math.sin(theta) + 0.006
    var newCoord = {
      lng: bd_lng,
      lat: bd_lat
    }
    return newCoord
  }
  /**百度转火星*/
  this.bd09_To_gcj02 = function (bd_lng, bd_lat) {
    var x = bd_lng - 0.0065
    var y = bd_lat - 0.006
    var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi)
    var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi)
    var gg_lng = z * Math.cos(theta)
    var gg_lat = z * Math.sin(theta)
    var newCoord = {
      lng: gg_lng,
      lat: gg_lat
    }
    return newCoord
  }

  var pi = 3.1415926535897932384626
  var a = 6378245.0
  var ee = 0.00669342162296594323
  var x_pi = (pi * 3000.0) / 180.0
  var R = 6378137

  function transform(lng, lat) {
    var dLat = transformLat(lng - 105.0, lat - 35.0)
    var dLng = transformLng(lng - 105.0, lat - 35.0)
    var radLat = (lat / 180.0) * pi
    var magic = Math.sin(radLat)
    magic = 1 - ee * magic * magic
    var sqrtMagic = Math.sqrt(magic)
    dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * pi)
    dLng = (dLng * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * pi)
    var mgLat = lat + dLat
    var mgLng = lng + dLng
    var newCoord = {
      lng: mgLng,
      lat: mgLat
    }
    return newCoord
  }

  function transformLat(x, y) {
    var ret =
      -100.0 +
      2.0 * x +
      3.0 * y +
      0.2 * y * y +
      0.1 * x * y +
      0.2 * Math.sqrt(Math.abs(x))
    ret +=
      ((20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0) /
      3.0
    ret +=
      ((20.0 * Math.sin(y * pi) + 40.0 * Math.sin((y / 3.0) * pi)) * 2.0) / 3.0
    ret +=
      ((160.0 * Math.sin((y / 12.0) * pi) + 320 * Math.sin((y * pi) / 30.0)) *
        2.0) /
      3.0
    return ret
  }

  function transformLng(x, y) {
    var ret =
      300.0 +
      x +
      2.0 * y +
      0.1 * x * x +
      0.1 * x * y +
      0.1 * Math.sqrt(Math.abs(x))
    ret +=
      ((20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0) /
      3.0
    ret +=
      ((20.0 * Math.sin(x * pi) + 40.0 * Math.sin((x / 3.0) * pi)) * 2.0) / 3.0
    ret +=
      ((150.0 * Math.sin((x / 12.0) * pi) + 300.0 * Math.sin((x / 30.0) * pi)) *
        2.0) /
      3.0
    return ret
  }
}

L.coordConver = function () {
  return new L.CoordConver()
}

L.GridLayer.include({
  _setZoomTransform: function (level, _center, zoom) {
    var center = _center
    if (center != undefined && this.options) {
      if (this.options.corrdType == 'gcj02') {
        center = L.coordConver().gps84_To_gcj02(_center.lng, _center.lat)
      } else if (this.options.corrdType == 'bd09') {
        center = L.coordConver().gps84_To_bd09(_center.lng, _center.lat)
      }
    }
    var scale = this._map.getZoomScale(zoom, level.zoom),
      translate = level.origin
        .multiplyBy(scale)
        .subtract(this._map._getNewPixelOrigin(center, zoom))
        .round()

    if (L.Browser.any3d) {
      L.DomUtil.setTransform(level.el, translate, scale)
    } else {
      L.DomUtil.setPosition(level.el, translate)
    }
  },
  _getTiledPixelBounds: function (_center) {
    var center = _center
    if (center != undefined && this.options) {
      if (this.options.corrdType == 'gcj02') {
        center = L.coordConver().gps84_To_gcj02(_center.lng, _center.lat)
      } else if (this.options.corrdType == 'bd09') {
        center = L.coordConver().gps84_To_bd09(_center.lng, _center.lat)
      }
    }
    var map = this._map,
      mapZoom = map._animatingZoom
        ? Math.max(map._animateToZoom, map.getZoom())
        : map.getZoom(),
      scale = map.getZoomScale(mapZoom, this._tileZoom),
      pixelCenter = map.project(center, this._tileZoom).floor(),
      halfSize = map.getSize().divideBy(scale * 2)

    return new L.Bounds(
      pixelCenter.subtract(halfSize),
      pixelCenter.add(halfSize)
    )
  }
})

const lMap = ref(null)
let map = null
const marker = ref(null)

// 将地图动态更新
onMounted(() => {
  console.log('加载地图')
  let layer = L.tileLayer(
    'http://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
    {
      // let layer = L.tileLayer('/static/1721721424935/{z}/{x}/{y}.png', {
      subdomains: ['1', '2', '3', '4'],
      corrdType: 'gps84',
      minZoom: 8,
      maxZoom: 18
    }
  )
  let mapOptions = {
    layers: [layer],
    // crs: L.CRS.EPSG4326,
    zoomControl: false,
    /* center:[纬度,经度] */
    trackResize: true,
    center: [25.045247919055374, 101.52817726135255],
    zoom: 14
    // minZoom: 1,
    // maxZoom: 19,
  }
  if (props.mapLng && props.mapLat) {
    mapOptions.center = [props.mapLat, props.mapLng]
  }
  map = L.map(lMap.value, mapOptions)
  console.log('map', map)

  //回填渲染点位
  if (props.mapLng && props.mapLat) {
    marker.value = addPoint(props.mapLat, props.mapLng)
  }
  //点击事件
  map.on('click', (data) => {
    console.log('click', data)
    // addPoint(data.latlng.lat, data.latlng.lng)
    $emit('mapLeftClick', data)
    $emit('update:mapLat', data.latlng.lat.toFixed(6))
    $emit('update:mapLng', data.latlng.lng.toFixed(6))
    // isToggle = !isToggle;
    // if (isToggle) {
    //     map.removeLayer(layer)
    // } else {
    //     map.addLayer(layer)
    // }
  })
  // drawPolygon(map).startDraw()
  setTimeout(function () {
    map.invalidateSize(true)
  }, 10)
})

//动态绘制多边形
function drawPolygon(map) {
  var points = [],
    geometry = []
  var lines = new L.polyline([])
  var tempLines = new L.polyline([], { dashArray: 5 })

  //map.off(....) 关闭该事件

  function onClick(e) {
    points.push([e.latlng.lat, e.latlng.lng])
    lines.addLatLng(e.latlng)
    map.addLayer(tempLines)
    map.addLayer(lines)
    const node = L.circle(e.latlng, {
      color: '#ff0000',
      fillColor: 'ff0000',
      fillOpacity: 1
    })
    map.addLayer(node)
    geometry.push(node)
  }
  function onMove(e) {
    if (points.length > 0) {
      ls = [points[points.length - 1], [e.latlng.lat, e.latlng.lng], points[0]]
      tempLines.setLatLngs(ls)
      // map.addLayer(tempLines)
    }
  }

  function onDoubleClick(e) {
    if (points.length > 3) {
      geometry.push(L.polygon(points).addTo(map))
      points = []
      //map.removeLayer(tempLines)
      //tempLines.remove()
      lines.remove()
      tempLines.remove()
      lines = new L.polyline([])
    }
  }

  function startDraw() {
    map.on('click', onClick) //点击地图
    map.on('dblclick', onDoubleClick)
    map.on('mousemove', onMove) //双击地图
  }

  function stopDraw() {
    map.off('click', onClick) //点击地图
    map.off('dblclick', onDoubleClick)
    map.off('mousemove', onMove) //双击地图
  }
  return {
    startDraw,
    stopDraw
  }
}

const markerStyle1 = setMarkerStyle(
  '/operationWeb/static/mapIcon/wei.png',
  null
)
const markerStyle2 = setMarkerStyle('/static/mapIcon/video.png', null)

function addPoint(lat, lng, type = 1) {
  let marker = null
  if (props.type == 'point') {
    marker = L.marker([lat, lng], {
      alt: 'marker',
      title: 'marker',
      draggable: true,
      autoPan: true,
      icon: markerStyle1
    })
  } else {
    marker = L.marker([lat, lng], {
      alt: 'marker',
      title: 'marker',
      draggable: true,
      autoPan: true,
      icon: markerStyle2
    })
  }
  map.addLayer(marker)
  marker.on('click', function (data) {
    console.log('marker click', data)
  })
  marker.on('dragend', function (data) {
    console.log('marker dragend', data)
    //保留小数点后六位
    $emit('update:mapLat', data.target._latlng.lat.toFixed(6))
    $emit('update:mapLng', data.target._latlng.lng.toFixed(6))
  })
  return marker
}

//生成marker
function getMarker(lat, lng, options, data) {
  let marker = L.marker([lat, lng], options)
  marker.data = data
  return marker
}

//移除marker
function removePoint(marker) {
  map.removeLayer(marker)
}

//移除指定图层
function removeLayer(layer) {
  map.removeLayer(layer)
}

//生成聚合点图层
function getClusterLayer(options) {
  let layer = new MarkerClusterGroup()
  // map.addLayer(layer);
  console.log('图层', layer)
  layer.addTo(map)
  return layer
}

//设置marker样式
function setMarkerStyle(url, shadowUrl = null) {
  return L.icon({
    iconUrl: url,
    shadowUrl: shadowUrl,
    iconSize: [74, 74], // icon的大小
    shadowSize: [50, 64], // 阴影的大小
    iconAnchor: [37, 74], // icon的渲染的位置（相对与marker）
    shadowAnchor: [4, 62], // shadow的渲染的位置（相对于marker）
    popupAnchor: [-3, -74] //若是绑定了popup的popup的打开位置（相对于icon）
  })
}

//设置中心点和层级
function setCenterAndZoom([lng, lat], zoom) {
  map.flyTo([lat, lng], zoom)
}

function setPopups(popup) {
  popup.openOn(map)
}

defineExpose({
  addPoint, // 向地图添加Point的方法
  drawPolygon, // 向地图绘制Polygon的方法
  removePoint, // 移除Point的方法
  getMarker, // 生成marker的方法
  getClusterLayer, // 生成聚合点图层的方法
  removeLayer, // 移除指定图层的方法
  marker,
  setPopups, // 设置弹窗的方法
  setMarkerStyle, // 设置marker样式的方法
  setCenterAndZoom // 设置中心点和层级的方法
})
</script>

<style lang="scss" scoped></style>

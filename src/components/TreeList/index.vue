<template>
    <div class="df fdc h-full w-full bg-[#FFF]">
        <div class=' df fdc jcsb pr-[5px]' style="align-items: end;">
            <div class="df aic cursor-pointer text-[#606266]" v-if="showChange">
                <div class="mr-[4px]" @click="handleChangeOrg(!checked)" :style="{ color: checked ? '#3665FF' : '' }">
                    显示当前组织所有</div>
                <el-checkbox v-model="checked" @change="handleChange" />
            </div>
            <div class="df aic mb-[5px] text-[#606266] cursor-pointer" v-if="!showSwitch">
                <div class="mr-[8px]" @click="handelSwitchChange1(!switches)"
                    :style="{ color: switches ? '#3665FF' : '' }">仅显示当前组织</div>
                <el-checkbox v-model="switches" @change="handelSwitchChange" />
            </div>
        </div>
        <div class="h-[40px]" v-if="!hiddenSearch">
            <el-input v-model="search" placeholder="">
                <template #suffix>
                    <el-icon class="el-input__icon" @click="handelSelect">
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>
        <div class="f1 pb-[5px]" style="overflow: hidden;">
            <el-scrollbar>
                <el-tree ref="treeRef" highlight-current node-key="id" :data="treeList || treeData"
                    :default-expanded-keys="openNodeList" :props="treeProps" :current-node-key="changeNode" accordion
                    :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick"
                    :filter-node-method="filterNode">
                    <template #default="{ node, data }">
                        <slot :node="node" :data="data" :tree="treeList || treeData" v-if="slots.default" />
                        <div v-else class="el-tree-node__label w-[120px] truncate" :title="data.name">{{ data.name }}
                        </div>
                    </template>
                </el-tree>
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { computed, nextTick, ref, watch, useSlots } from 'vue'
import { $getOrganizationPageTree } from '@/api/user-organization/index'


const slots = useSlots()

const props = defineProps({
    // 树数据
    treeList: { //树数据 可不填 自动获取
        type: Array,
        default: null,
    },
    type: { //选择树类型   1行政区域 0组织机构 2行政区域、政府机构
        type: String,
        default: null,
        required: true,
    },
    treeProps: { //映射
        type: Object,
        default: () => ({
            children: "children",
            label: "name",
            value: "id",
        }),
    },
    // 是否展示 本级按钮
    showSwitch: {
        type: Boolean,
        default: false
    },
    // 是否展示搜索框
    hiddenSearch: {
        type: Boolean,
        default: false
    },
    includeEnterprise: {
        type: Boolean,
        default: false
    },
    openNode: { //默认展开节点
        type: Array,
        default: () => [],
    },
    fillId: { //父级fullId
        type: String,
        default: null,
    },
    showChange: {
        type: Boolean,
        default: false,
    }
});

const $emit = defineEmits(['nodeClick', 'switchChange', 'showOtherTree'])

//选中节点
const changeNode = ref(null);
const treeRef = ref(null);
const switches = ref(false);
const checked = ref(false);
const search = ref('');

const openNodeList = computed(() => {
    if (props.openNode.length > 0) {
        return props.openNode
    } else {
        return treeData.value?.map(item => item.id)
    }
})

function handelSelect() {
    if (!treeRef.value) return
    treeRef.value.filter(search.value)
}

//树节点过滤
function filterNode(value, data) {
    if (!value) return true
    // console.log('节点过滤', value, data);
    return data.name.includes(value)
}

watch(() => search.value, (val) => {
    if (!treeRef.value) return
    treeRef.value.filter(val)
})

//默认选择第一个节点
watch(() => props.treeList, () => {
    if (props.treeList?.length <= 0) return
    nextTick(() => {
        if (props.treeList?.length > 0) {
            changeNode.value = props.treeList[0].id;
        } else {
            changeNode.value = null;
        }
    })
}, { deep: true, immediate: true })

// 节点恢复到默认选择第一个节点
const defaultNode = () => {
    if (props.treeList?.length > 0) {
        changeNode.value = props.treeList[0].id;
    } else {
        changeNode.value = treeData.value[0].id;
    }
}

//节点点击事件
function handleNodeClick(data, node, treeNode, event) {
    openNodeList.value = [data.id];
    changeNode.value = data.id;
    $emit('nodeClick', { data, node, treeNode, event })
}

function handelSwitchChange() {
    getTreeData()
    $emit('switchChange', switches.value)
}
function handelSwitchChange1() {
    switches.value = !switches.value
    getTreeData()
    $emit('switchChange', switches.value)
}

function handleChange() {
    $emit('showOtherTree', checked.value)
}

const treeData = ref([]);

watch(() => props.fillId, () => {
    getTreeData()
}, { deep: true, immediate: true })

getTreeData()
function getTreeData() {
    let path = props.type;
    let option = {
        isCurLevel: switches.value,
    }
    if (props.fillId) {
        option.parentFullId = props.fillId;
    }
    if(props.includeEnterprise){
        option.includeEnterprise = props.includeEnterprise
    }
    $getOrganizationPageTree(path, option).then(res => {
        treeData.value = res.data;
        nextTick(() => {
            setTimeout(() => {
                if (treeData.value?.length > 0) {
                    changeNode.value = treeData.value[0].id;
                } else {
                    changeNode.value = null;
                }
                $emit('nodeClick', { data: treeData.value[0], node: null, treeNode: null, event: null })
            }, 500)
        })
    })
}

function handleChangeOrg(val) {
    checked.value = val;
    $emit('showOtherTree', val)
}

const refushTreeData = () => {
    let path = props.type;
    let option = {
        isCurLevel: switches.value,
    }
    if (props.fillId) {
        option.fillId = props.fillId;
    }
    $getOrganizationPageTree(path, option).then(res => {
        treeData.value = res.data;
    })
}

const changeItem = computed(() => {
    if (changeNode.value) {
        nextTick(() => {
            // $emit('nodeClick', { data: treeData.value[0], node: null, treeNode: null, event: null })

            return treeRef.value.getCurrentNode()
        })
    } else {
        return {}
    }
})

function getChangeItem() {
    return treeRef.value.getCurrentNode()
}

defineExpose({
    treeData, //树数据
    switches, //本级按钮状态
    changeItem, //当前节点数据
    getChangeItem,
    getTreeData,
    refushTreeData,
    defaultNode, //恢复默认节点
})

</script>

<style lang="scss" scoped>
.active {
    color: #3665FF;
}

.el-input {
    --el-input-bg-color: #F4F5F7;

    :deep(.el-input__suffix) {
        cursor: pointer;
        padding-right: 11px;
        ;
    }

    :deep(.el-input__wrapper) {
        padding-right: 0;
    }
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    // 设置颜色
    background-color: rgba(135, 206, 235, 0.2); // 透明度为0.2的skyblue，作者比较喜欢的颜色 
    color: #409eff; // 节点的字体颜色
    font-weight: bold; // 字体加粗
}
</style>
<template>
  <div class="w-full h-full flex flex-col overflow-hidden" v-loading="loading">
    <div :style="{ height: tabList.length > 0 ? '66px' : '0' }">
      <el-tabs
        closable
        class="h-full"
        v-model="activeName"
        @tab-change="handleTabClick"
        @tab-remove="deleteTab"
      >
        <el-tab-pane
          v-for="(item, index) in tabList"
          :key="item.id"
          :label="item.name"
          :name="item.index"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <div class="function h-[60px] w-full flex pt-[20px] bg-[#FFF]">
      <!-- 添加新语法 -->
      <el-dropdown @command="(val: any) => handleNewLanguage(val, {})">
        <span class="el-dropdown-link" style="outline: none; cursor: pointer">
          <svg-icon
            name="add"
            class="mr-[26px] cursor-pointer"
            style="width: 24px; height: 24px"
          ></svg-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="it in props.optionsData"
              :command="it.value"
              >{{ it.description }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- 运行 -->
      <svg-icon
        name="play"
        v-if="tabList.length > 0"
        class="mr-[26px] cursor-pointer"
        style="width: 24px; height: 24px"
        @click="runEve"
      ></svg-icon>
      <!-- 保存 -->
      <svg-icon
        name="save"
        v-if="tabList.length > 0"
        class="mr-[26px] cursor-pointer"
        style="width: 24px; height: 24px"
        @click="saveEve"
      ></svg-icon>
      <!-- 撤销 -->
      <svg-icon
        name="undo"
        v-if="tabList.length > 0"
        class="mr-[26px] cursor-pointer"
        style="width: 24px; height: 24px"
        @click="undoEve"
      ></svg-icon>
      <!-- 恢复 -->
      <svg-icon
        name="redo"
        v-if="tabList.length > 0"
        class="mr-[26px] cursor-pointer"
        style="width: 24px; height: 24px"
        @click="redoEve"
      ></svg-icon>
    </div>
    <Codemirror
      v-if="tabList.length > 0 && activeName >= 0"
      v-model="tabList[activeName].code"
      class="code"
      style="flex: 1; overflow-x: hidden; transition: height 0.3s ease-in-out"
      :mode="mode"
      :spellcheck="true"
      :autofocus="tabList.length > 0 ? true : false"
      :indent-with-tab="true"
      :tabSize="4"
      :extensions="extensions"
    />
    <div v-else class="w-full flex-1 bg-[#F3F3F3]"></div>
  </div>
</template>

<script setup lang="ts">
import { Codemirror } from 'vue-codemirror'
import { javascript } from '@codemirror/lang-javascript'
import { python } from '@codemirror/lang-python'
import { sql } from '@codemirror/lang-sql'
import { shell } from '@codemirror/legacy-modes/mode/shell'
import { EditorView } from '@codemirror/view'
import { oneDark } from '@codemirror/theme-one-dark'
import { StreamLanguage } from '@codemirror/language'
import { undo, redo } from '@codemirror/commands'
import { warning } from '@/utils/toast'

let code = ref('')
let mode = ref('')
let extensions: any[] = []

const loading = ref(false)

// 自定义白色主题
const whiteTheme = EditorView.theme(
  {
    '&': {
      backgroundColor: '#F3F3F3',
      color: '#000000'
    },
    '.cm-content': {
      caretColor: '#000000',
      fontSize: '18px'
    },
    '&.cm-focused .cm-cursor': {
      borderLeftColor: '#000000'
    },
    '&.cm-focused .cm-selectionBackground, ::selection': {
      backgroundColor: '#d9d9d9'
    }
  },
  { dark: false }
)

// 定义从父组件接收的属性
const props = withDefaults(
  defineProps<{
    language: string
    optionsData: any
  }>(),
  {
    language: '0',
    optionsData: [] as any[]
  }
)

// 定义语言变量
const coedLanguage = ref<any>(null)

const $emit = defineEmits(['saveEve', 'runEve'])

// 编辑器初始化 / 切换语言
const initCode = (val: any) => {
  if (val == '0') {
    coedLanguage.value = 'python'
    extensions = [python(), whiteTheme]
    return
  }
  if (val == '1') {
    coedLanguage.value = 'sql'
    extensions = [sql(), whiteTheme]
    return
  }
  if (val == '2') {
    coedLanguage.value = 'shell'
    extensions = [StreamLanguage.define(shell), whiteTheme]
    return
  }
}

watch(
  () => props.language,
  (newVal) => {
    initCode(newVal)
  },
  {
    immediate: true
  }
)

const activeName = ref(0)

// 新建语法事件
const handleNewLanguage = (val: any, content: any = {}) => {
  tabNum.value[props.optionsData.find((item: any) => item.value == val).label]++
  tabList.value.push({
    label: val,
    name:
      content.name ||
      '新建' +
        props.optionsData.find((item: any) => item.value == val).description +
        tabNum.value[
          props.optionsData.find((item: any) => item.value == val).label
        ],
    code:
      val == '0'
        ? content.content
          ? content.content
          : '#!/usr/bin/python\n'
        : content.content
          ? content.content
          : null,
    index: tabList.value.length,
    id: content.id || '',
    type: val
  })
  const index = tabList.value.length > 0 ? tabList.value.length - 1 : 0
  activeName.value = tabList.value[index].index
  initCode(val)
}

// 切换tabs事件
const handleTabClick = (tab: any, event: any) => {
  console.log(tab)
  activeName.value = tab
  initCode(tabList.value[tab].label)
}

// 删除tabs事件
const deleteTab = (tab: any) => {
  tabList.value.splice(tab, 1)
  tabList.value.forEach((item: any, index: number) => {
    item.index = index
  })
  if (tabList.value.length > 0) {
    activeName.value = tabList.value[tabList.value.length - 1].index
  } else {
    activeName.value = 0
  }
  if (tabList.value.length === 0) return
  initCode(tabList.value[activeName.value].label)
}

const tabNum = ref<any>({
  py: 0,
  hql: 0,
  sh: 0
})

// 顶部 tabs 标签数组
const tabList = ref<any[]>([])

// 保存事件
const saveEve = () => {
  console.log(tabList.value[activeName.value].code)
  $emit('saveEve', tabList.value[activeName.value])
}

// 运行事件
const runEve = () => {
  // 这里可以写运行代码的逻辑
  if (!tabList.value[activeName.value].code) return warning('执行代码不能为空')
  $emit('runEve', tabList.value[activeName.value])
}

// 撤销事件
const undoEve = () => {
  undo(EditorView.findFromDOM(document.querySelector('.code') as any) as any)
}

// 恢复事件
const redoEve = () => {
  redo(EditorView.findFromDOM(document.querySelector('.code') as any) as any)
}

// 导出方法
defineExpose({
  tabList,
  handleNewLanguage,
  activeName,
  loading
})
</script>
<style scoped>
:deep(.cm-line) {
  text-align: left !important;
}
:deep(.cm-activeLine) {
  text-align: left !important;
}
:deep(.el-tabs__header) {
  margin: 0 !important;
}
:deep(.el-tabs__item) {
  height: 66px !important;
  line-height: 66px !important;
}
:deep(.el-tabs__nav-wrap) {
  height: 66px !important;
}
:deep(.el-tabs) {
  background-color: #fff !important;
}
</style>

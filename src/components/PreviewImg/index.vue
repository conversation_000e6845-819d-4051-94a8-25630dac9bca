<!-- 传入图片名称 显示真实图片 -->
<template>
  <el-image
    preview-teleported
    :style="{ width: props.width, height: props.height }"
    :src="url"
    :preview-src-list="imgList"
    :initial-index="idx"
    :fit="fill"
  />
</template>

<script setup lang="ts">
const resourceUrl = import.meta.env.VITE_RESOURCE_URL

const props = defineProps({
  // 图片宽度
  width: {
    type: String,
    default: '100%'
  },
  fill: {
    type: String,
    default: 'fill'
  },
  // 图片高度
  height: {
    type: String,
    default: '100%'
  },
  // 图片在服务器的名称
  name: {
    type: String,
    default: ''
  },
  // 点击预览的序号
  idx: {
    type: Number,
    default: 0
  },
  // 预览图片列表
  previewList: {
    type: Array,
    default: []
  }
})

const url = ref('')

const imgList = computed(() => {
  return props.previewList.length > 0 ? props.previewList : [url.value]
})

// 根据传入的名字获取真实 url
watchEffect(() => {
  if (props.name) {
    url.value = `${resourceUrl}/oss/file/preview?fileName=${props.name}`
  }
})
</script>

<style lang="scss" scoped></style>

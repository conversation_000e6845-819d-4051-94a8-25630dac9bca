<template>
  <div class="ccj">
    <el-dialog
      align-center
      v-model="dialogVisible"
      :width="width"
      style="height: 268px"
      :before-close="close"
      :style="{ paddingBottom: paddingBottom }"
      :close-on-click-modal="closeOnClickModal"
    >
      <div class="flex items-center justify-center flex-col">
        <div class="w-[68px] h-[68px] flex-shrink-0">
          <svg-icon
            :name="props.icon"
            style="width: 100%; height: 100%"
          ></svg-icon>
        </div>
        <div
          class="mt-5 text-[#333333] bold flex items-center justify-center tracking-wider text-[16px]"
          v-html="props.content"
        ></div>
        <div
          class="subtit mt-5 h-[19px] flex-shrink-0"
          v-if="props.icon == 'del'"
        >
          该操作不可逆，请谨慎操作！
        </div>
        <div
          class="subtit mt-5 h-[19px] flex-shrink-0"
          v-if="props.icon == 'wen'"
        ></div>
      </div>
      <template #footer v-if="footer">
        <div class="dialog-footer pb-[36px]">
          <el-button color="#F2F3F5" @click="close">取消</el-button>
          <el-button type="danger" @click="dialogConfirmEve" :loading="loading">
            {{ okBtnText }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
const dialogVisible = ref(false)

const loading = ref(false)

const $emit = defineEmits(['confirm', 'cancel'])

const props = defineProps({
  // 内容
  content: {
    type: String,
    default: '确定删除吗？'
  },
  icon: {
    type: String,
    default: 'del'
  },
  width: {
    type: String,
    default: '573px'
  },
  // 是否显示底部按钮
  footer: {
    type: Boolean,
    default: true
  },
  okBtnText: {
    type: String,
    default: '确定'
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
})

// 计算弹窗底部 padding
const paddingBottom = computed(() => {
  return props.footer ? '36px' : '0'
})

// 关闭之前的操作
const close = () => {
  dialogVisible.value = false
  data.value = null
  $emit('cancel')
}
const beforeClose = (done) => {
  dialogVisible.value = false
  $emit('cancel')
  done()
}

// 确认按钮事件
const dialogConfirmEve = () => {
  $emit('confirm', data.value)
}

const data = ref(null)

// 打开弹窗
const open = (datas = null) => {
  data.value = datas
  dialogVisible.value = true
}
// 导出方法
defineExpose({
  open,
  loading,
  close
})
</script>

<style lang="scss" scoped>
.ccj {
  :deep(.el-dialog) {
    padding: 0 !important;
    .el-dialog__header {
      background-color: #fff !important;
    }
  }
  :deep(.el-dialog .el-dialog__body) {
    padding: 12px 35px 24px !important;
  }
}
.subtit {
  font-weight: 400;
  font-size: 14px;
  color: #52585f;
  line-height: 19px;
}
</style>

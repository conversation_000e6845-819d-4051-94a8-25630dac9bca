<template>
    <div class="df fdc h-full w-full">
        <div class='h-[48px] df jcsb' v-if="showType">
            <div class="df aic  cursor-pointer">
                <div class="mr-[4px]" :class="{ 'active': checked1 }" @click="handleChange1(!checked1)">行政区域</div>
                <el-checkbox v-model="checked1" @change="handleChange1" />
            </div>
            <div class="df aic cursor-pointer">
                <div class="mr-[4px]" :class="{ 'active': checked2 }" @click="handleChange2(!checked2)">组织机构</div>
                <el-checkbox v-model="checked2" @change="handleChange2" />
            </div>
        </div>
        <div class="df aic mb-[5px] justify-end" v-if="showSwitches">
            <div class="mr-[8px]">仅显示本级</div>
            <el-checkbox v-model="switches" @change="handelSwitchChange" />
        </div>
        <div class="h-[40px]" v-if="showSearch">
            <el-input v-model="search" placeholder="">
                <template #suffix>
                    <el-icon class="el-input__icon" @click="handelSelect">
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>
        <div class="f1 pb-[5px]" style="overflow: hidden;">
            <el-scrollbar>
                <el-tree ref="treeRef" highlight-current node-key="id" :data="treeList || treeData"
                    :default-expanded-keys="openNodeList" :props="treeProps" :current-node-key="changeNode" accordion
                    :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick"
                    :filter-node-method="filterNode">
                    <template #default="{ node, data }">
                        <slot :node="node" :data="data" :tree="treeList || treeData" v-if="slots.default" />
                        <span v-else class="el-tree-node__label">{{ data.name }}</span>
                    </template>
                </el-tree>
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { computed, nextTick, ref, watch, useSlots } from 'vue'
import { $getOrganizationPageTree } from '@/api/user-organization/index'


const slots = useSlots()
const props = defineProps({
    // 树数据
    treeList: { //树数据 可不填 自动获取
        type: Array,
        default: null,
    },
    type: { //选择树类型   1行政区域 0组织机构 2行政区域、政府机构
        type: String,
        default: null,
        required: true,
    },
    showType: { // 是否显示切换树类型按钮
        type: Boolean,
        default: true,
    },
    treeProps: { //映射
        type: Object,
        default: () => ({
            children: "children",
            label: "name",
            value: "id",
        }),
    },
    openNode: { //默认展开节点
        type: Array,
        default: () => [],
    },
    showSwitches: { //是否显示本级按钮
        type: Boolean,
        default: true,
    },
    showSearch: { //是否显示搜索框
        type: Boolean,
        default: true,
    },
});

const treeRef = ref(null);

const treeData = ref([]);

const openNodeList = computed(()=>{
    if(props.openNode.length>0){
        return props.openNode
    }else{
        return treeData.value?.map(item => item.id)
    }
})


const $emit = defineEmits(['nodeClick','switchChange'])

function handelSelect() {
    if (!treeRef.value) return
    treeRef.value.filter(search.value)
}

// const defaultProps = {}

const search = ref('');


const switches = ref(false);

function handelSwitchChange(){
    $emit('switchChange', switches.value)
}

//选中节点
const changeNode = ref(null);


//设置激活点位
function setChangeNode(id = null) {
    console.log('设置激活点位', id);
    if(!id) {
        console.log(treeData.value[0].id)
        setTimeout(() => {
            changeNode.value = treeData.value[0].id;
        }, 300)
    }
    changeNode.value = id;
}

//默认选择第一个节点
watch(() => props.treeList, () => {
    if (props.treeList?.length <= 0) return
    nextTick(() => {
        if (props.treeList?.length > 0) {
            changeNode.value = props.treeList[0].id;
        } else {
            changeNode.value = null;
        }
    })
}, { deep: true, immediate: true })

//节点点击事件
function handleNodeClick(data, node, treeNode, event) {
    openNodeList.value = [data.id];
    changeNode.value = data.id;
    $emit('nodeClick', { data, node, treeNode, event })
}

//行政区域
const checked1 = ref(false);
//组织机构
const checked2 = ref(false);

//计算树类型参数
const flag = computed(() => {
    if (checked1.value && checked2.value) {
        return'2'
    } else if (checked1.value) {
        return '1'
    } else if (checked2.value) {
        return '0'
    }
})

//选中行政区域
function handleChange1(val) {
    // console.log('选中行政区域', val);
    if (checked2.value) {
        checked1.value = val;
        getTreeData()
    } else {
        checked1.value = true;
    }
}

//选中组织机构
function handleChange2(params) {
    if (checked1.value) {
        checked2.value = params;
        getTreeData()
    } else {
        checked2.value = true;
    }
}

//行政区域、政府机构
watch(()=>props.type, (newVal) => {
    if (newVal == '1') {
        checked1.value = true;
        checked2.value = false;
    } else if (newVal == '0') {
        checked1.value = false;
        checked2.value = true;
    } else if (newVal == '2') {
        checked1.value = true;
        checked2.value = true;
    }
    getTreeData()
}, { immediate: true })


function getTreeData() {
    let params = flag.value || props.type;
    $getOrganizationPageTree(params).then(res => {
        treeData.value = res.data;
        nextTick(() => {
            if (treeData.value?.length > 0) {
                changeNode.value = treeData.value[0].id;
            } else {
                changeNode.value = null;
            }
            $emit('nodeClick', { data: treeData.value[0], node: null, treeNode: null, event: null })
        })
    })
}

const refushTreeData = () => {
    let params = flag.value || props.type;
    $getOrganizationPageTree(params).then(res => {
        treeData.value = res.data;
    })
}

watch(()=>search.value, (val) => {
    if(!treeRef.value) return
    treeRef.value.filter(val)
})

//树节点过滤
function filterNode(value, data) {
    if (!value) return true
    // console.log('节点过滤', value, data);
    return data.name.includes(value)
}

const changeItem = computed(()=>{
    if(changeNode.value){
        nextTick(() => {
            // $emit('nodeClick', { data: treeData.value[0], node: null, treeNode: null, event: null })
            return treeRef.value.getCurrentNode()
        })
    }else{
        return {}
    }
})

defineExpose({
    treeData, //树数据
    switches, //本级按钮状态
    changeItem, //当前节点数据
    setChangeNode, //设置激活节点（参数为节点id）
    getTreeData,
    refushTreeData
})

</script>

<style lang="scss" scoped>
.active {
    color: #3665FF;
}

.el-input {
    --el-input-bg-color: #F4F5F7;

    :deep(.el-input__suffix) {
        cursor: pointer;
        padding-right: 11px;
        ;
    }

    :deep(.el-input__wrapper) {
        padding-right: 0;
    }
}
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    // 设置颜色
    background-color: rgba(135, 206, 235, 0.2); // 透明度为0.2的skyblue，作者比较喜欢的颜色 
    color: #409eff; // 节点的字体颜色
    font-weight: bold; // 字体加粗
}
:deep(.el-tree-node__content) {
    width: 250px !important;
    overflow: hidden !important;
    .titt {
      transition: all .3s ease;
    }
    .edit {
      width: 0px;
      display: none !important;
    }
    &:hover .edit {
      margin-left: 20px;
      display: flex !important;
    }
    &:hover .titt {
      width: 0px !important;
    }
  }
  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    // 设置颜色
    background-color: rgba(135, 206, 235, 0.2); // 透明度为0.2的skyblue，作者比较喜欢的颜色 
    color: #409eff; // 节点的字体颜色
    font-weight: bold; // 字体加粗
  }
</style>
<!-- 弹窗组件 -->
<template>
  <div class="tt">
    <el-drawer
      v-model="dialogVisible"
      :title="title"
      :size="width"
      :before-close="close"
      destroy-on-close
      :style="{ paddingBottom: paddingBottom }"
      :close-on-click-modal="closeOnClickModal"
      @open="openMounted"
    >
      <slot />
      <template #footer v-if="footer">
        <div class="dialog-footer pb-[36px]">
          <el-button color="#F2F3F5" @click="close">{{
            cancelBtnText
          }}</el-button>
          <el-button
            v-if="needOkBtn"
            type="primary"
            @click="dialogConfirmEve"
            :loading="loading"
          >
            {{ okBtnText }}
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
const dialogVisible = ref(false)

const loading = ref(false)

const $emit = defineEmits(['confirm', 'cancel', 'openMounted'])

const props = defineProps({
  title: {
    type: String,
    default: '弹窗标题'
  },
  width: {
    type: String,
    default: '30%'
  },
  height: {
    type: String
  },
  // 是否显示底部按钮
  footer: {
    type: Boolean,
    default: true
  },
  okBtnText: {
    type: String,
    default: '确定'
  },
  cancelBtnText: {
    type: String,
    default: '取消'
  },
  // 是否需要确认按钮
  needOkBtn: {
    type: Boolean,
    default: true
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
})

// 计算弹窗底部 padding
const paddingBottom = computed(() => {
  return props.footer ? '36px' : '0'
})

// 关闭之前的操作
const close = () => {
  dialogVisible.value = false
  data.value = null
  $emit('cancel')
}
const beforeClose = (done) => {
  dialogVisible.value = false
  $emit('cancel')
  done()
}

// 确认按钮事件
const dialogConfirmEve = () => {
  $emit('confirm', data.value)
}

const data = ref(null)

// 打开弹窗
const open = (datas = null) => {
  data.value = datas
  dialogVisible.value = true
}
const openMounted = () => {
  $emit('openMounted')
}
// 导出方法
defineExpose({
  open,
  loading,
  dialogVisible,
  close
})
</script>

<style lang="scss" scoped>
.tt {
  .el-dialog {
    padding: 0 !important;
    overflow: hidden !important;
  }
  :deep(.el-dialog__header) {
    background: rgba(36, 104, 255, 0.1) !important;
  }
  :deep(.el-dialog__body) {
    padding: 35px !important;
  }
}
</style>

<template>
  <div class="df">
    <el-date-picker
      v-model="formInline.startAt"
      type="datetime"
      @change="handelChangeStart"
      placeholder="请选择日期"
      format="YYYY/MM/DD"
      value-format="YYYY-MM-DD HH:mm:ss"
      :prefix-icon="customPrefix"
      ref="startAtRef"
      :disabled-date="disabledDateStart"
      :disabled="disabledStartTime"
    />
    <div class="mx-[5px] bg-transparent">-</div>
    <el-date-picker
      v-model="formInline.endAt"
      type="datetime"
      @change="handelChangeEnd"
      placeholder="请选择日期"
      format="YYYY/MM/DD"
      value-format="YYYY-MM-DD HH:mm:ss"
      :prefix-icon="customPrefix"
      ref="endAtRef"
      :disabled-date="disabledDateEnd"
      :disabled="disabledEndTime"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  startAt: {
    type: String,
    default: ''
  },
  endAt: {
    type: String,
    default: ''
  },
  showStartAfterTime: {
    type: Boolean,
    default: false
  },
  showEndAfterTime: {
    type: Boolean,
    default: false
  },
  disabledStartTime: {
    type: Boolean,
    default: false
  },
  disabledEndTime: {
    type: Boolean,
    default: false
  }
})

// 计算属性用于处理日期显示
const startDate = computed({
  get: () => props.startAt,
  set: (value) => $emit('update:startAt', value)
})

const endDate = computed({
  get: () => props.endAt,
  set: (value) => $emit('update:endAt', value)
})
const formInline = ref({
  startAt: props.startAt,
  endAt: props.endAt
})

const $emit = defineEmits(['update:startAt', 'update:endAt', 'change'])

function handelChangeStart(val) {
  // let datelist = {...props.dateData, startAt: val}
  // formInline.value.startAt = val
  console.log('开始时间', val)
  $emit('update:startAt', val || '')
  if (val && props.endAt) {
    $emit('change')
  }
}

function handelChangeEnd(val) {
  // let datelist = {...props.dateData, endAt: val}
  // formInline.value.endAt = val
  console.log('结束时间', val)
  $emit('update:endAt', val || '')
  if (props.startAt && val) {
    $emit('change')
  }
}

watch(
  () => [props.startAt, props.endAt],
  (newVal, oldVal) => {
    if (!newVal[0] && !newVal[1]) {
      $emit('update:startAt', '')
      $emit('update:endAt', '')
      $emit('change')
    }
  }
)

function disabledDateStart(date) {
  // console.log(date, formInline.value.endAt, new Date(formInline.value.endAt).getTime() < new Date(date).getTime());
  if (props.endAt && !props.showStartAfterTime) {
    return new Date(props.endAt).getTime() < new Date(date).getTime()
  } else if (props.showStartAfterTime) {
    if (props.endAt) {
      return new Date(date).getTime() > new Date(props.endAt).getTime()
    }
    return false
  } else {
    return date.getTime() > Date.now()
  }
}

function disabledDateEnd(date) {
  if (props.startAt && !props.showEndAfterTime) {
    return (
      new Date(props.startAt).getTime() > new Date(date).getTime() ||
      date.getTime() > Date.now()
    )
  } else if (props.showEndAfterTime) {
    if (props.startAt) {
      return new Date(date).getTime() < new Date(props.startAt).getTime()
    }
    return false
  } else {
    return date.getTime() > Date.now()
  }
}

// 清空值
const clearValue = () => {
  formInline.value.startAt = ''
  formInline.value.endAt = ''
  $emit('update:startAt', '')
  $emit('update:endAt', '')
  $emit('change')
}

const startAtRef = ref(null)
const endAtRef = ref(null)

const handleFocus = () => {
  console.log('focus startAt', startAtRef.value)
  startAtRef.value.handleOpen()
}

const handleFocus1 = () => {
  endAtRef.value.handleOpen()
}
watch(
  props.startAt,
  (newVal, oldVal) => {
    formInline.value.startAt = newVal
  },
  { deep: true }
)
watch(
  props.endAt,
  (newVal, oldVal) => {
    formInline.value.endAt = newVal
  },
  { deep: true }
)
defineExpose({
  clearValue
})
</script>

<style lang="scss" scoped></style>

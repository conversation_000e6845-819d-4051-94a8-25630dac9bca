<template>
  <div class="ccj">
    <el-dialog align-center v-model="dialogVisible" width="573px" style="height: 268px;" :before-close="close">
      <div class="flex items-center justify-center flex-col bg-[#FFF]">
        <div class="w-[68px] h-[68px] flex-shrink-0 " v-if="status === 'success'">
          <svg-icon name="ok" style="width: 100%; height: 100%;"></svg-icon>
        </div>
        <div class="w-[68px] h-[68px] flex-shrink-0 " v-if="status === 'error'">
          <svg-icon name="error" style="width: 100%; height: 100%;"></svg-icon>
        </div>
        <div class="w-[68px] h-[68px] flex-shrink-0 " v-if="status === 'pendding'">
          <el-progress style="width: 100%; height: 100%" :show-text="false" type="circle" :stroke-width="10" :percentage="process" color="#3665FF" />
        </div>
        <div class="mt-5 text-[#333333] bold flex items-center justify-center tracking-wider text-[16px]">{{ content }}</div>
        <div class="subtit mt-5 h-[19px] flex-shrink-0">{{ subTit }}</div>
      </div>
      <template #footer>
        <div class="dialog-footer pb-[36px]">
          <el-button color="#F2F3F5" v-if="status === 'pendding'" @click="stopExport">取消导出</el-button>
          <el-button color="#F2F3F5" v-if="status !== 'pendding'" @click="exportEve(reqObj)">重新导出</el-button>
          <el-button type="primary" @click="close">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import FileSaver from 'file-saver';
import { computed, ref } from "vue"
const props = defineProps({
  // 导出接口
  exportReq: {
    required: true,
  },
  // 导出文件名称
  name: {
    type: String,
    default: '请传入导出文件名称'
  },
  //导出文件类型
  suffix:{
    type:String,
    default:'.xlsx'
  }
})
// 状态
const status = ref('pendding')

// 上传进度
const process = ref(0)

const dialogVisible = ref(false)

const $emit = defineEmits(['confirm', 'cancel'])


const content = computed(() => {
  if (status.value === 'pendding') {
    return '正在导出'
  }
  if(status.value.success) {
    return '导出成功'
  }
  if(status.value.error) {
    return '导出失败'
  }
})

const subTit = computed(() => {
  if (status.value === 'pendding') {
    return '数据导出过程可能需要一段时间，请耐心等候'
  }
  if(status.value === 'success') {
    return '数据导出成功'
  }
  if(status.value === 'error') {
    return '数据导出失败'
  }
})

// 导出进度回调函数
const downSpeed = (progressEvent) => {
  console.log('213213213', progressEvent)
  process.value = (progressEvent.progress * 100).toFixed(2) + 1;
}

// 取消请求相关
let controller = null;

let reqObj = null

// 打开弹窗
const open = (data) => {
  reqObj = data
  dialogVisible.value = true
  exportEve(data)
}

// 导出方法
const exportEve = (val) => {
  controller = new AbortController();
  status.value = 'pendding'
  process.value = 0
  props.exportReq(val, downSpeed, controller.signal).then(res => {
    console.log("🚀 ~ props.exportReq ~ res:", res)
    FileSaver.saveAs(res, `${props.name}${props.suffix}`)
    status.value = 'success'
  }).catch(err => {
    status.value = 'error'
  })
}

// 取消导出
const stopExport = () => {
  controller.abort()
  status.value = 'error'
}


// 关闭之前的操作
const close = () => {
  dialogVisible.value = false
}


// 导出方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.ccj {
  :deep(.el-dialog) {
    padding: 0 !important;
    .el-dialog__header {
      background-color: #fff !important;
    }
  }
  :deep(.el-dialog__body) {
    padding: 12px 35px 24px !important;
    color: #FFF !important;
  }
  :deep(.el-progress-circle) {
    width: 100% !important;
    height: 100% !important;
  }
}
.subtit {
  font-weight: 400;
  font-size: 14px;
  color: #52585F;
  line-height: 19px;
}
</style>
import request from '@/utils/request'

// 分页查询操作日志
export const $_getLogPage = (params: any) => {
  return request({
    method: 'get',
    url: `/log/logManager/page`,
    params
  })
}

// 导出日志记录
export const $_exportLog = (params: any) => {
  return request({
    method: 'get',
    url: `/log/logManager/export`,
    params,
    responseType: 'blob'
  }).then((res: any) => {
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'logs.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
  })
}

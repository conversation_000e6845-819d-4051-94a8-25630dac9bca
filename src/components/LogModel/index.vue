<template>
    <dialog-view ref="logRef" title="用户日志" width="50%" :needOkBtn="false" cancelBtnText="关闭">
        <div class="log-content">
            <div class="content-header w-full flex items-center gap-x-[20px]">
                <el-input placeholder="操作账号" v-model="form.keyword" clearable style="width: 166px"
                    @change="changeEve" />
                <SelectByDict v-model="form.operType" style="width: 166px" placeholder="操作类型"
                    :reqFn="() => $getDictType('log_oper_type')" :multiple="false"
                    :defaultOptions="{ key: 'value', label: 'label', value: 'value' }" @change="changeEve" />
                <date-picker v-model:startAt="form.startAt" v-model:endAt="form.endAt" :showStartAfterTime="false"
                    :showEndAfterTime="false" @change="changeEve" ref="dataPickerRef" />
                <el-button type="primary" @click="resetForm">重置</el-button>
                <el-button type="primary" @click="exportLog">导出</el-button>
            </div>
            <div class="content-body flex flex-col gap-y-[20px]">
                <div class="log-info relative" v-for="(item, index) of logData" :key="index">
                    <div class="flex flex-col gap-y-[30px] w-full">
                        <div class="w-full">操作时间：{{ item.createTime || item.updateTime }}</div>
                        <div class="flex items-center gap-x-[150px] w-full">
                            <div>账号：{{ item.createName }}</div>
                            <div>操作类型：{{ item.operTypeMark }}</div>
                        </div>
                        <div class="w-full">操作内容：{{ item.title }}</div>
                    </div>
                        <div class="mt-[30px]" v-show="item.showInfo">
                            <div class="grid grid-cols-2 gap-x-[20px]">
                                <div class="flex flex-col gap-y-[10px]">
                                    <div class="font-bold mb-[10px]">操作前：</div>
                                    <div v-if="item.oldValue" class="flex flex-col gap-y-[10px]">
                                        <div v-for="(field, index) of formatLogValue(item.oldValue)" :key="index"
                                             class="grid grid-cols-[120px,1fr] gap-x-[10px]">
                                            <span class="text-gray-500">{{ field.key }}：</span>
                                            <span>{{ field.value }}</span>
                                        </div>
                                    </div>
                                    <div v-else class="text-gray-400">无修改记录</div>
                                </div>
                                <div class="flex flex-col gap-y-[10px]">
                                    <div class="font-bold mb-[10px]">操作后：</div>
                                    <div v-if="item.result" class="flex flex-col gap-y-[10px]">
                                        <div v-for="(field, index) of formatLogValue(item.result)" :key="index"
                                             class="grid grid-cols-[120px,1fr] gap-x-[10px]">
                                            <span class="text-gray-500">{{ field.key }}：</span>
                                            <span>{{ field.value }}</span>
                                        </div>
                                    </div>
                                    <div v-else class="text-gray-400">无修改记录</div>
                                </div>
                            </div>
                        </div>
                    <div class="absolute right-[20px] bottom-[20px] cursor-pointer">
                        <div class="flex items-center gap-x-[10px]" v-if="item.showInfo"
                            @click="item.showInfo = !item.showInfo">
                            <el-icon>
                                <ArrowDown />
                            </el-icon>
                            <div>展开查看详情</div>
                        </div>
                        <div class="flex items-center gap-x-[10px] cursor-pointer" v-else
                            @click="item.showInfo = !item.showInfo">
                            <el-icon>
                                <ArrowUp />
                            </el-icon>
                            <div>折叠</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-footer">
                <div class="fn df items-bottom flex-shrink-0" style="padding-top: 40px; height: 70px;">
                    <div class="f1"></div>
                    <el-pagination class="fn" :current-page="pageConfig.currentPage" :page-size="pageConfig.pageSize"
                        :page-sizes="pageConfig.pageSizes" :small="pageConfig.small" :disabled="pageConfig.disabled"
                        :background="pageConfig.background" :layout="pageConfig.paginationOptions"
                        :total="pageConfig.total * 1" @size-change="handleSizeChange"
                        @current-change="handleCurrentChange">
                        <template #default>
                            <div>
                                共 <span class="text-[#3665FF] font-bold">{{ total }}</span> 条
                            </div>
                        </template>
                    </el-pagination>
                </div>
            </div>
        </div>
    </dialog-view>
</template>

<script setup>
import dialogView from '@/components/DialogView/index.vue';
import datePicker from '@/components/DatePicker/index.vue';
import SelectByDict from '../SelectByDict/index.vue';
import { $getDictType } from '@/api/dict'
import { $_getLogPage, $_exportLog } from './api/index'
import { ref } from 'vue';

const props = defineProps({
    modular: {
        type: String,
        default: ''
    }
});
const logDataId = ref()

// 日志筛选条件
const form = ref({
    keyword: '',
    operType: '',
    startAt: '',
    endAt: ''
})

const pageConfig = ref({
    currentPage: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 50, 100],
    small: false,
    disabled: false,
    background: true,
    paginationOptions: 'total, sizes, prev, pager, next'
});

// 处理操作前后的数据格式
const formatLogValue = (value) => {
    if (!value) return [];
    try {
        // 移除开头和结尾的方括号，并解析字符串
        const cleanStr = value.replace(/^\[|\]$/g, '');
        // 将字符串分割成键值对
        const pairs = cleanStr.split(',').map(pair => {
            // 处理每个键值对
            const matches = pair.match(/"([^"]+)":"?([^"]*)"?/);
            if (matches) {
                const [, key, value] = matches;
                // 过滤掉一些不需要显示的系统字段
                if (['serialVersionUID', 'entityClass'].includes(key)) {
                    return null;
                }
                // 格式化时间戳
                if (key.includes('时间') && !isNaN(value)) {
                    const date = new Date(parseInt(value));
                    return {
                        key,
                        value: date.toLocaleString()
                    };
                }
                return {
                    key,
                    value: value || '-'
                };
            }
            return null;
        }).filter(item => item !== null);

        return pairs;
    } catch (error) {
        console.error('格式化日志数据失败:', error);
        return [];
    }
};

const logData = ref();

// 获取日志记录
const getLogData = async () => {
    const data = {
        modular: props.modular,
        logDataId: logDataId.value,
        ...form.value,
        current: pageConfig.value.currentPage,
        size: pageConfig.value.pageSize
    }
    Object.keys(data).forEach(key => {
        if (data[key] === '' || data[key] === null || data[key] === undefined) {
            delete data[key];
        }
    });
    try {
        const response = await $_getLogPage({ ...data });
        logData.value = response.data.records;
        pageConfig.value.total = response.data.total;
    } catch (error) {
        console.error('获取日志数据失败:', error);
    }
};

const handleSizeChange = (size) => {
    pageConfig.value.pageSize = size;
    getLogData();
};

const handleCurrentChange = (page) => {
    pageConfig.value.currentPage = page;
    getLogData();
};

const changeEve = () => {
    pageConfig.value.currentPage = 1;
    getLogData();
};

const resetForm = () => {
    form.value = {
        keyword: '',
        operType: '',
        startAt: '',
        endAt: ''
    };
    getLogData();
};

const logRef = ref();

const open = (id) => {
    logDataId.value = id
    getLogData()
    logRef.value.open()
}

// 导出日志
const exportLog = async () => {
    // 导出日志的逻辑
    const data = {
        modular: props.modular,
        logDataId: logDataId.value,
        ...form.value,
        current: 1,
        size: 99999
    }
    Object.keys(data).forEach(key => {
        if (data[key] === '' || data[key] === null || data[key] === undefined) {
            delete data[key];
        }
    });
    try {
        const response = await $_exportLog({ ...data });
    } catch (error) {
        console.error('导出日志失败:', error);
    }
};

defineExpose({
    open
})
</script>

<style lang="scss" scoped>
.log-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .content-header {
        background-color: #f8fafc;
        display: flex;
        align-items: center;
        padding: 16px 24px;
        box-sizing: border-box;
        margin-bottom: 24px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .content-body {
        flex: 1;
        background-color: #fff;
        overflow-y: auto;
        padding: 0 12px;
    }

    .log-info {
        padding: 24px;
        box-sizing: border-box;
        border-radius: 8px;
        background-color: #ffffff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 16px;
        padding-bottom: 80px;
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        position: relative;

        &:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .text-gray-500 {
            color: #6b7280;
        }

        .font-bold {
            font-weight: 600;
            color: #374151;
        }

        .grid {
            background-color: #f9fafb;
            padding: 16px;
            border-radius: 6px;
            
            &-cols-2 {
                gap: 24px;
            }
        }

        .cursor-pointer {
            color: #3b82f6;
            font-size: 14px;
            
            &:hover {
                color: #2563eb;
            }

            .el-icon {
                transition: transform 0.3s ease;
            }

            &:hover .el-icon {
                transform: translateY(-2px);
            }
        }
    }

    .content-footer {
        padding: 20px 0;
    }
}
</style>

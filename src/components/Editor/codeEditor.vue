<template>
  <div
    :class="props.bord ? 'bord' : ''"
    style="margin-top: 10px; z-index: 99999999999999"
    :style="{ height: height }"
    class="flex h-full w-full flex-col"
  >
    <Toolbar
      :style="{ width: width }"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="props.mode"
      class="flex-shrink-0"
    />
    <Editor
      :defaultConfig="editorConfig"
      :mode="props.mode"
      v-model="valueHtml"
      class="flex-1"
      :style="{ width: width, height: '80%' }"
      @onCreated="handleCreated"
      @onChange="handleChange"
      @onDestroyed="handleDestroyed"
      @onFocus="handleFocus"
      @onBlur="handleBlur"
      @customAlert="customAlert"
      @customPaste="customPaste"
    />
  </div>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'
import { useAppStore } from '@/store/modules/app'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { storeToRefs } from 'pinia'
import { $upLoadFile } from '@/api/file'
const resourceUrl = import.meta.env.VITE_RESOURCE_URL
const { userInfo } = storeToRefs(useAppStore())
const props = defineProps({
  // 是否需要菜单
  needMenu: {
    type: Boolean,
    default: true
  },
  // 自定义菜单项
  menu: {
    type: Array,
    default: () => []
  },
  // 编辑器高度
  height: {
    type: String,
    default: '100%'
  },
  // 编辑器宽度
  width: {
    type: String,
    default: '100%'
  },
  // 是否禁用编辑器
  bord: {
    type: Boolean,
    default: true
  },
  // 模式
  mode: {
    type: String,
    default: 'default'
  },
  // 是否只读
  readOnly: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  }
})

const valueHtml = defineModel('htmlValue')

// 编辑器实例，必须用 shallowRef，重要！
const editorRef = shallowRef()

const toolbarConfig = ref<any>({
  toolbarKeys: [
    'headerSelect',
    'blockquote',
    '|',
    'bold',
    'underline',
    'italic',
    {
      key: 'group-more-style',
      title: '更多',
      iconSvg:
        '<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',
      menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle']
    },
    'color',
    'bgColor',
    '|',
    'fontSize',
    'fontFamily',
    'lineHeight',
    '|',
    'bulletedList',
    'numberedList',
    'todo',
    {
      key: 'group-justify',
      title: '对齐',
      iconSvg:
        '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
      menuKeys: [
        'justifyLeft',
        'justifyRight',
        'justifyCenter',
        'justifyJustify'
      ]
    },
    {
      key: 'group-indent',
      title: '缩进',
      iconSvg:
        '<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',
      menuKeys: ['indent', 'delIndent']
    },
    '|',
    'emotion',
    'insertLink',
    {
      key: 'group-image',
      title: '图片',
      iconSvg:
        '<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',
      menuKeys: ['insertImage', 'uploadImage']
    },
    // {
    //     "key": "group-video",
    //     "title": "视频",
    //     "iconSvg": "<svg viewBox=\"0 0 1024 1024\"><path d=\"M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z\"></path></svg>",
    //     "menuKeys": [
    //         "insertVideo",
    //         "uploadVideo"
    //     ]
    // },
    'insertTable',
    'codeBlock',
    'divider',
    '|',
    'undo',
    'redo',
    '|',
    'fullScreen'
  ]
})

const editorConfig = ref<any>({
  placeholder: props.placeholder,
  readOnly: props.readOnly,
  scroll: true,
  MENU_CONF: {}
})

editorConfig.value.MENU_CONF['uploadImage'] = {
  async customUpload(file: any, insertFn: any) {
    const formData = new FormData()
    formData.append('file', file)
    const res = await $upLoadFile(formData)
    const url = `${resourceUrl}${res.data.url}`
    insertFn(url)
  }
}

// 组件销毁时，也及时销毁编辑器，重要！
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return

  editor.destroy()
})

// 编辑器回调函数
const handleCreated = (editor: any) => {
  editorRef.value = editor // 记录 editor 实例，重要！
}
const handleChange = (editor: any) => {
  // console.log("change:", editor.getHtml());
}
const handleDestroyed = (editor: any) => {}
const handleFocus = (editor: any) => {}
const handleBlur = (editor: any) => {}
const customAlert = (info: any, type: any) => {
  alert(`【自定义提示】${type} - ${info}`)
}
const customPaste = (editor: any, event: any, callback: any) => {
  // console.log("ClipboardEvent 粘贴事件对象", event);

  // 自定义插入内容
  // editor.insertText("xxx");

  // 返回值（注意，vue 事件的返回值，不能用 return）
  // callback(false); // 返回 false ，阻止默认粘贴行为
  callback(true) // 返回 true ，继续默认的粘贴行为
}

const insertText = () => {
  const editor = editorRef.value
  if (editor == null) return

  editor.insertText('hello world')
}

const printHtml = () => {
  const editor = editorRef.value
  if (editor == null) return
}

const disable = () => {
  const editor = editorRef.value
  if (editor == null) return
  editor.disable()
}

onMounted(() => {
  // 菜单配置
  if (props.menu.length != 0) {
    toolbarConfig.value.toolbarKeys = props.menu
  }
  // 是否需要菜单
  if (props.needMenu == false) {
    toolbarConfig.value.toolbarKeys = []
  }
  // setTimeout(() => {
  //   const toolbar = DomEditor.getToolbar(editorRef.value);
  //   const curToolbarConfig = toolbar.getConfig();
  //   console.log("所有的工具栏配置", curToolbarConfig.toolbarKeys);
  // }, 1000);
})
</script>
<style>
.bord {
  border: 1px solid #ccc !important;
}
</style>

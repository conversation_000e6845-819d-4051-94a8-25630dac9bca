declare module '@wangeditor/editor-for-vue' {
  import { Component } from 'vue'

  interface EditorProps {
    // 定义 Editor 组件的 props 类型
    value: string
    onChange: (value: string) => void
  }

  interface ToolbarProps {
    // 定义 Toolbar 组件的 props 类型
    editor: Editor
  }

  class Editor extends Component<EditorProps> {
    // 定义 Editor 组件的类型
  }

  class Toolbar extends Component<ToolbarProps> {
    // 定义 Toolbar 组件的类型
  }

  export { Editor, Toolbar }
}

<template>
  <svg aria-hidden="true">
    <use :href="symbolId" :xlink:href="symbolId" :fill="color" />
  </svg>
</template>

<script setup>
const props = defineProps({
  prefix: {
    type: String,
    default: 'icon'
  },
  name: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: '#333'
  }
})

const symbolId = computed(() => `#${props.prefix}-${props.name}`)
</script>

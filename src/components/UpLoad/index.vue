<template>
  <div class="cont-box flex flex-wrap relative logo">
    <!-- 上传文件 -->
    <input
      ref="fileInput"
      type="file"
      :key="kyy"
      @change="handleFileChange"
      :accept="fileType"
      class="up"
    />
    <!-- 上传区域 -->
    <div
      v-if="fileUrl.length < max"
      @click="handleClick"
      class="up-box w-[90px] h-[90px] flex items-center justify-center mr-2 mb-2"
    >
      <el-icon><Plus /></el-icon>
    </div>
    <!-- 上传回显 -->
    <div
      v-if="fileUrl"
      v-for="(it, idx) in fileUrl"
      class="up-img w-[90px] h-[90px] flex items-center justify-center mr-2 mb-2 relative"
    >
      <el-image
        ref="imgList"
        :preview-src-list="fileUrl"
        :initial-index="idx"
        :src="it"
        alt="上传图片"
        class="w-full h-full object-cover"
      ></el-image>
      <!-- 图标
      <div
        class="active absolute top-0 right-0 w-[90px] h-[30px] flex items-center justify-around"
      >
        <el-icon @click="handleDelete(idx)" size="28"
          ><Delete color="#fff"
        /></el-icon>
      </div> -->
      <!-- 进度 -->
      <div
        v-if="progressList[idx] >= 0 && progressList[idx] <= 100"
        class="absolute top-0 right-0 w-[90px] h-[90px] flex items-center justify-center"
      >
        <el-progress width="60" type="circle" :percentage="progressList[idx]" />
      </div>
      <!-- 预览图标
      <div @click="handlePreview(idx)" class="absolute top-0 left-0 w-[20px] h-[20px] bg-pink-300 flex items-center justify-center">
        <el-icon><ZoomIn /></el-icon>
      </div> -->
      <!-- 删除 -->
      <div
        class="active absolute top-0 right-0 w-full h-[30px] flex items-center justify-around z-999"
      >
        <el-icon @click="handleDelete" size="28">
          <Delete color="#fff" />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { $upLoadFile } from '@/api/file'
import { warning } from '@/utils/toast'
import { v4 as uuidv4 } from 'uuid'
import { Delete } from '@element-plus/icons-vue'
const resourceUrl = import.meta.env.VITE_RESOURCE_URL

const kyy = ref(uuidv4())
const $emit = defineEmits(['update:modelValue'])

const props = defineProps({
  // 上传文件类型
  type: {
    type: String,
    default: ''
  },
  // 上传文件数量
  max: {
    type: Number,
    default: 1
  },
  modelValue: {
    type: String
  }
})

const fileType = computed(() => {
  if (props.type === 'image') {
    return 'image/*'
  } else if (props.type === 'video') {
    return 'video/*'
  } else if (props.type === 'zip') {
    return '.zip'
  } else {
    return ''
  }
})

// 文件格式名称计算
const fileName = computed(() => {
  if (props.type === 'image') {
    return '图片'
  }
})

const imgList = ref([])

// 上传文件回显
const fileUrl = ref<any>([])

const fileInput = ref<any>(null)

watch(
  () => props.modelValue,
  (val) => {
    fileUrl.value = [`${resourceUrl}/admin/sys-file/oss/file?fileName=${val}`]
  },
  {
    once: true
  }
)

// 点击上传文件
const handleClick = () => {
  if (!!fileInput.value) {
    fileInput.value?.click()
  }
}

// 上传文件
const handleFileChange = async (e: any) => {
  kyy.value = uuidv4()
  const file = e.target.files[0]
  if (!file) {
    console.warn('没有选择文件')
    return
  }
  if (!file.type.includes(props.type)) {
    warning(`请上传${fileName.value}类型文件！`)
    return
  }
  // if()
  try {
    const url = await convertFileToBase64(file)
    fileUrl.value.push(url)
    handleUpload(file)
  } catch (error) {
    console.error('文件转换或处理过程中出现问题:', error)
  }
}

// 上传进度
const progressList = ref<any[]>([])

// 获取上传请求
const handlePreview = (e: any) => {
  progressList.value[fileUrl.value.length - 1] =
    (e.progress * 100).toFixed(2) + 1
}

// 发送上传请求
const handleUpload = (file: any) => {
  const formData = new FormData()
  formData.append('file', file)
  $upLoadFile(formData, handlePreview)
    .then((res: any) => {
      console.log(res)
      $emit('update:modelValue', res.data.fileName)
    })
    .catch((err: any) => {
      fileUrl.value.splice(-1, 1)
    })
}

// 上传文件格式转换
const convertFileToBase64 = (file: any) => {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader()
      reader.onload = (e: any) => {
        const base64String = e.target.result // 这里包含了"data:image/*;base64,"前缀，根据需要处理
        resolve(base64String) // 读取成功后，通过resolve传递Base64字符串
      }
      reader.onerror = (error) => {
        reject(error) // 读取失败时，通过reject传递错误
      }
      reader.readAsDataURL(file) // 开始读取文件
    } catch (error) {
      reject(error) // 捕获并传递任何立即发生的错误
    }
  })
}

// 删除上传文件
const handleDelete = (idx: any) => {
  fileUrl.value.splice(idx, 1)
  $emit('update:modelValue', '')
}
</script>

<style lang="scss" scoped>
.up {
  display: none;
}
.up-box {
  border: 1px solid #dedfe0;
  cursor: pointer;
}
.up-img {
  cursor: pointer;
  .active {
    display: none;
    background-color: rgba(0, 0, 0, 0.4);
  }
  &:hover .active {
    display: flex;
  }
}
.logo {
  .active {
    background-color: rgba(0, 0, 0, 0.5);
    // display: none;
  }

  &:hover .active {
    display: flex;
    cursor: pointer;
  }
}
</style>

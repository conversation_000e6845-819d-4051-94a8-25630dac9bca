<template>
  <!-- 依据字典接口下拉框 -->
  <el-select
    :model-value="value"
    :placeholder="placeholder"
    @change="handleChange"
    :multiple="multiple"
    clearable
  >
    <el-option
      v-for="item in options"
      :key="item[defaultOptions.key]"
      :label="item[defaultOptions.label]"
      :value="item[defaultOptions.value]"
    />
  </el-select>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue'

const props = defineProps({
  value: {
    required: true
  },
  showname: {
    default: null
  },
  placeholder: {
    type: String,
    default: ''
  },
  defaultOptions: {
    type: Object,
    default: {
      key: 'key',
      label: 'label',
      value: 'value'
    }
  },
  reqFn: {
    type: Function,
    required: true
  },
  assignmentFn: {
    type: Function,
    default: (res) => res.data
  },
  multiple: {
    type: Boolean,
    default: false
  }
})

const $emit = defineEmits(['update:value', 'update:showname', 'change'])

const options = ref([])

// watch(() => props.value, (newVal) => {
//     if (newVal) {
//         options.value = []
//         initOptions()
//     }
// }, { immediate: true, deep: true })

onMounted(() => {
  options.value = []
  initOptions()
})

function initOptions() {
  props.reqFn().then((res) => {
    if (res.code == 0) {
      console.log('请求字典成功')
      $emit('defaultValue', res.data)
      options.value = props.assignmentFn(res)
    } else {
      console.error('请求字典失败')
      options.value = []
    }
  })
}

//选中值
function handleChange(value) {
  $emit('update:value', value)
  // 多选  必须有值
  if (props.multiple && !!value) {
    $emit(
      'update:showname',
      options.value
        .filter((item) => value.includes(item[props.defaultOptions.value]))
        .map((item) => item[props.defaultOptions.label])
        .join(',')
    )
  } else {
    $emit('update:showname', '')
  }
  // 单选  必须有值
  if (!props.multiple && !!value) {
    $emit(
      'update:showname',
      options.value.find((item) => item[props.defaultOptions.value] == value)[
        props.defaultOptions.label
      ]
    )
  } else {
    $emit('update:showname', '')
  }
  $emit('change')
}
</script>

<style lang="scss" scoped></style>

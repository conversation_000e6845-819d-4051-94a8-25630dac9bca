<!-- 表格组件(平台 老款用) -->
<template>
  <div class="df fdc h-full w-full flex-shrink-0 box-border" ref="tableEl">
    <el-table
      ref="tableRef"
      :data="tableData"
      :style="{ width: '100%' }"
      :row-key="rowKey"
      class="flex-1 overflow-hidden"
      :reserve-selection="true"
      @select="handleSelect"
      @select-all="
        (selection) => {
          handleSelect(selection, null, tableData)
        }
      "
      :max-height="props.maxHeight ? props.maxHeight : null"
      @selection-change="handleSelectionChange"
      @current-change="handleTableCurrentChange"
    >
      <slot></slot>
    </el-table>
    <div
      class="fn df flex-shrink-0 pr-[20px] box-border"
      style="margin-top: 15px; height: 60px"
      v-if="needPagination"
    >
      <div class="f1"></div>
      <el-pagination
        class="fn"
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :small="small"
        :disabled="disabled"
        :background="background"
        :layout="paginationOptions"
        :total="total * 1"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #default>
          <div>
            共 <span class="text-[#3665FF] font-bold">{{ total }}</span> 条
          </div>
        </template>
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { useElementSize } from '@vueuse/core'
import { ref } from 'vue'

const tableRef = ref()

const props = defineProps({
  tableData: {
    // 表格数据
    type: Array,
    required: true
  },
  rowKey: {
    // 行数据的唯一标识
    type: String,
    default: 'id'
  },
  total: {
    // 总条数
    type: Number,
    required: false
  },
  currentPage: {
    // 当前页
    type: Number,
    default: 1
  },
  pageSize: {
    // 每页显示条数
    type: Number,
    default: 10
  },
  pageSizes: {
    // 每页显示条数
    type: Array,
    default: [10, 20, 30, 40, 50, 100]
  },
  maxHeight: {
    type: [Number, String],
    default: null,
    validator: (value) => {
      if (value === null) return true
      // 允许数字或者带px的字符串
      return !isNaN(value) || /^\d+px$/.test(value)
    }
  },
  disabled: {
    // 是否禁用分页
    type: Boolean,
    default: false
  },
  small: {
    // 是否显示小型分页
    type: Boolean,
    default: false
  },
  background: {
    // 是否分页显示背景色
    type: Boolean,
    default: false
  },
  paginationOptions: {
    // 自定义分页配置
    type: String,
    default: 'slot, prev, pager, next ,sizes'
  },
  height: {
    type: Number,
    default: 600
  },
  needPagination: {
    type: Boolean,
    default: true
  }
})

const tableEl = ref(null)

const $emit = defineEmits([
  'handleCurrentChange',
  'handleSizeChange',
  'handleSelectionChange'
])

const { width, height } = useElementSize(document.querySelector('#app'))

function handleSizeChange(params) {
  $emit('handleSizeChange', params)
}

function handleCurrentChange(params) {
  $emit('handleCurrentChange', params)
}

function handleTableCurrentChange(params) {
  $emit('handleTableCurrentChange', params)
}

function handleSelectionChange(params) {
  $emit('handleSelectionChange', params)
}
// 清楚筛选条件
function clearFilterEve() {
  tableRef.value.clearFilter()
}

// 清除某一行的选中状态
function clearRowSelection(row, type = false) {
  // 先将该行设置为选中状态（如果未选中）
  tableRef.value.toggleRowSelection(row, type)
  // 再次调用以切换状态为未选中
  // tableRef.value.toggleRowSelection(row, false)
}

const clearSelection = () => {
  tableRef.value.clearSelection()
}

function handleSelect(selection, row, allList) {
  $emit('handleSelectBox', selection, row, allList)
}

// 设置已选中行
const setSelection = (rows) => {
  if (!Array.isArray(rows)) return
  // 先清除所有选中状态
  tableRef.value?.clearSelection()

  // 等待数据更新完成
  nextTick(async () => {
    // 确保表格实例存在
    if (!tableRef.value) return

    // 遍历需要选中的行
    for (const row of rows) {
      if (!row) continue
      // 在表格数据中查找匹配的行
      const matchRow = props.tableData.find(
        (item) => item[props.rowKey] === row[props.rowKey]
      )
      if (matchRow) {
        await tableRef.value.toggleRowSelection(matchRow, true)
      }
    }
  })
}

defineExpose({
  clearSelection,
  clearFilterEve,
  clearRowSelection,
  setSelection
})
</script>

<style lang="scss" scoped>
.el-table {
  width: 100%;

  :deep(.is-leaf) {
    background-color: #f8f8f8;
    text-align: left !important;
    color: #333;
  }

  :deep(.el-table__cell) {
    //     text-align: center;
    // text-align: left !important;
  }
}

:deep(.is-leaf) {
  background-color: #f8f8f8 !important;
  text-align: left !important;
}

.el-pagination {
  :deep(.is-active) {
    background-color: #e5eeff !important;
    color: #3665ff !important;
  }

  :deep(.el-pagination__total) {
    font-size: 16px !important;
    font-weight: 600 !important;
  }
}

.el-table {
  --el-table-header-bg-color: #f8f8f8 !important;
}
</style>

const Rex = /\.vue$/
export default function viteSrcReplace() {
  let publicPath = ''
  let command = ''
  return {
    name: 'vite-plugin-vue-src-replace',
    config(config: { server: { port: string } }, env: { command: string }) {
      publicPath = 'http://localhost:' + config.server.port
      command = env.command
    },
    transform(code: string, src: string) {
      if (Rex.test(src) && command === 'serve') {
        const s = code.replace(/<img\s+[^>]*src="([^"]*)"[^>]*>/g, (m, p1) => {
          return m.replace(p1, `${publicPath}${p1}`);
        });
        return {
          code: s
        }
      }
      if (/\.(scss|css)$/.test(src) && command === 'serve') {
        const s = code.replace(/(?<=url\(")([^"]*)(?="\))/g, `${publicPath}$1`)
        return {
          code: s
        }
      }
      return {
        code
      }
    }
  }
}

// vite.config.ts
import { defineConfig, loadEnv } from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/vite/dist/node/index.js";
import { resolve } from "node:path";
import vue from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import AutoImport from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/unplugin-auto-import/dist/vite.js";
import compression from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/vite-plugin-compression/dist/index.mjs";

// plugins/srcReplace.ts
var Rex = /\.vue$/;
function viteSrcReplace() {
  let publicPath = "";
  let command = "";
  return {
    name: "vite-plugin-vue-src-replace",
    config(config, env) {
      publicPath = "http://localhost:" + config.server.port;
      command = env.command;
    },
    transform(code, src) {
      if (Rex.test(src) && command === "serve") {
        const s = code.replace(/<img\s+[^>]*src="([^"]*)"[^>]*>/g, (m, p1) => {
          return m.replace(p1, `${publicPath}${p1}`);
        });
        return {
          code: s
        };
      }
      if (/\.(scss|css)$/.test(src) && command === "serve") {
        const s = code.replace(/(?<=url\(")([^"]*)(?="\))/g, `${publicPath}$1`);
        return {
          code: s
        };
      }
      return {
        code
      };
    }
  };
}

// vite.config.ts
import { visualizer } from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import Inspect from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/vite-plugin-inspect/dist/index.mjs";
import { createSvgIconsPlugin } from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import WindiCSS from "file:///D:/%E6%96%B0%E6%98%B1/citybrain_serive/node_modules/vite-plugin-windicss/dist/index.mjs";
var __vite_injected_original_dirname = "D:\\\u65B0\u6631\\citybrain_serive";
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_AUTH, VITE_APP_PROXY_URL } = env;
  let proxy = {};
  const urlObject = {};
  if (VITE_APP_PROXY_URL && VITE_APP_PROXY_URL !== "") {
    proxy = Object.assign(urlObject, {
      [VITE_APP_AUTH]: {
        target: VITE_APP_PROXY_URL,
        changeOrigin: true,
        secure: false,
        rewrite: (p) => p.replace(VITE_APP_AUTH, "")
      }
    });
  }
  return {
    resolve: {
      alias: {
        "@/": `${resolve(__vite_injected_original_dirname, "src")}/`
      }
    },
    plugins: [
      vue(),
      viteSrcReplace(),
      WindiCSS(),
      // 自动导入方法
      AutoImport({
        imports: ["vue", "vue-router", "pinia", "@vueuse/core"],
        eslintrc: {
          enabled: true
        },
        dts: true,
        vueTemplate: false
      }),
      //svg插件配置
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [resolve(process.cwd(), "src/assets/icons")],
        // 指定symbolId格式
        symbolId: "icon-[dir]-[name]"
      }),
      // 打包体积预览
      visualizer({
        open: true
      }),
      // gzip压缩文件体积
      compression({
        algorithm: "gzip",
        // 指定压缩算法为gzip,[ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
        ext: ".gz",
        // 指定压缩后的文件扩展名为.gz
        threshold: 10240,
        // 仅对文件大小大于threshold的文件进行压缩，默认为10KB
        deleteOriginFile: false,
        // 是否删除原始文件，默认为false
        filter: /\.(js|css|json|html|ico|svg)(\?.*)?$/i,
        // 匹配要压缩的文件的正则表达式，默认为匹配.js、.css、.json、.html、.ico和.svg文件
        compressionOptions: { level: 9 },
        // 指定gzip压缩级别，默认为9（最高级别）
        verbose: true,
        //是否在控制台输出压缩结果
        disable: false
        //是否禁用插件
      }),
      Inspect()
    ],
    //scss预处理
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: "@import '@/styles/variables.scss';"
        }
      }
    },
    server: {
      hmr: true,
      // 启用热更新
      // 允许跨域
      cors: true,
      // 启动项目时自动打开
      open: false,
      port: 3e3,
      proxy
    },
    base: "/serviceWeb/",
    build: {
      terserOptions: {
        compress: {
          //是否删除console
          drop_console: true,
          //是否删除debugger
          drop_debugger: true,
          //是否删除无用代码
          unused: true,
          //是否保留所有注释
          keep_fargs: false
        }
      },
      // 设置最终构建的浏览器兼容目标
      target: "es2015",
      // 构建后是否生成 source map 文件
      sourcemap: false,
      //  chunk 大小警告的限制（以 kbs 为单位）
      chunkSizeWarningLimit: 2e3,
      // 启用/禁用 gzip 压缩大小报告
      reportCompressedSize: false,
      // 自定义底层的 Rollup 打包配置
      minify: false,
      rollupOptions: {
        output: {
          // 最小化拆分包
          manualChunks(id) {
            if (id.includes("node_modules")) {
              return id.toString().split("node_modules/")[1].split("/")[0].toString();
            }
          },
          // 对代码分割中产生的 chunk 自定义命名
          chunkFileNames: "static/js/[name]-[hash].js",
          // 指定 chunks 的入口文件模式
          entryFileNames: "static/js/[name]-[hash].js",
          // 自定义构建结果中的静态资源名称
          assetFileNames: "static/[ext]/[name]-[hash].[ext]",
          // 压缩 Rollup 产生的额外代码
          compact: true
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAicGx1Z2lucy9zcmNSZXBsYWNlLnRzIl0sCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRDpcXFxcXHU2NUIwXHU2NjMxXFxcXGNpdHlicmFpbl9zZXJpdmVcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkQ6XFxcXFx1NjVCMFx1NjYzMVxcXFxjaXR5YnJhaW5fc2VyaXZlXFxcXHZpdGUuY29uZmlnLnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9EOi8lRTYlOTYlQjAlRTYlOTglQjEvY2l0eWJyYWluX3Nlcml2ZS92aXRlLmNvbmZpZy50c1wiO2ltcG9ydCB7IGRlZmluZUNvbmZpZywgbG9hZEVudiB9IGZyb20gJ3ZpdGUnXHJcbmltcG9ydCB7IHJlc29sdmUgfSBmcm9tICdub2RlOnBhdGgnXHJcbmltcG9ydCB2dWUgZnJvbSAnQHZpdGVqcy9wbHVnaW4tdnVlJ1xyXG5cclxuXHJcbmltcG9ydCBBdXRvSW1wb3J0IGZyb20gJ3VucGx1Z2luLWF1dG8taW1wb3J0L3ZpdGUnXHJcbmltcG9ydCBjb21wcmVzc2lvbiBmcm9tICd2aXRlLXBsdWdpbi1jb21wcmVzc2lvbidcclxuaW1wb3J0IHZpdGVTcmNSZXBsYWNlIGZyb20gJy4vcGx1Z2lucy9zcmNSZXBsYWNlJ1xyXG5pbXBvcnQgeyB2aXN1YWxpemVyIH0gZnJvbSAncm9sbHVwLXBsdWdpbi12aXN1YWxpemVyJ1xyXG5cclxuaW1wb3J0IEluc3BlY3QgZnJvbSAndml0ZS1wbHVnaW4taW5zcGVjdCdcclxuXHJcbi8vIHN2Z1x1N0VDNFx1NEVGNlx1NjNEMlx1NEVGNlx1NUJGQ1x1NTE2NVxyXG5pbXBvcnQgeyBjcmVhdGVTdmdJY29uc1BsdWdpbiB9IGZyb20gJ3ZpdGUtcGx1Z2luLXN2Zy1pY29ucydcclxuXHJcbmltcG9ydCBXaW5kaUNTUyBmcm9tICd2aXRlLXBsdWdpbi13aW5kaWNzcydcclxuXHJcbi8vIGh0dHBzOi8vdml0ZWpzLmRldi9jb25maWcvXHJcbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZygoeyBtb2RlIH0pID0+IHtcclxuICBjb25zdCBlbnYgPSBsb2FkRW52KG1vZGUsIHByb2Nlc3MuY3dkKCkpXHJcblxyXG4gIGNvbnN0IHsgVklURV9BUFBfQVVUSCwgVklURV9BUFBfUFJPWFlfVVJMIH0gPSBlbnZcclxuICBsZXQgcHJveHkgPSB7fVxyXG5cclxuICBjb25zdCB1cmxPYmplY3QgPSB7fVxyXG4gIGlmIChWSVRFX0FQUF9QUk9YWV9VUkwgJiYgVklURV9BUFBfUFJPWFlfVVJMICE9PSAnJykge1xyXG4gICAgcHJveHkgPSBPYmplY3QuYXNzaWduKHVybE9iamVjdCwge1xyXG4gICAgICBbVklURV9BUFBfQVVUSF06IHtcclxuICAgICAgICB0YXJnZXQ6IFZJVEVfQVBQX1BST1hZX1VSTCxcclxuICAgICAgICBjaGFuZ2VPcmlnaW46IHRydWUsXHJcbiAgICAgICAgc2VjdXJlOiBmYWxzZSxcclxuICAgICAgICByZXdyaXRlOiAocDogc3RyaW5nKSA9PiBwLnJlcGxhY2UoVklURV9BUFBfQVVUSCwgJycpXHJcbiAgICAgIH1cclxuICAgIH0pXHJcbiAgfVxyXG4gIC8vIFx1OTE0RFx1N0Y2RVx1NTIyQlx1NTQwRFxyXG4gIHJldHVybiB7XHJcbiAgICByZXNvbHZlOiB7XHJcbiAgICAgIGFsaWFzOiB7XHJcbiAgICAgICAgJ0AvJzogYCR7cmVzb2x2ZShfX2Rpcm5hbWUsICdzcmMnKX0vYFxyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgcGx1Z2luczogW1xyXG4gICAgICB2dWUoKSxcclxuICAgICAgdml0ZVNyY1JlcGxhY2UoKSxcclxuICAgICAgV2luZGlDU1MoKSxcclxuICAgICAgLy8gXHU4MUVBXHU1MkE4XHU1QkZDXHU1MTY1XHU2NUI5XHU2Q0Q1XHJcbiAgICAgIEF1dG9JbXBvcnQoe1xyXG4gICAgICAgIGltcG9ydHM6IFsndnVlJywgJ3Z1ZS1yb3V0ZXInLCAncGluaWEnLCAnQHZ1ZXVzZS9jb3JlJ10sXHJcbiAgICAgICAgZXNsaW50cmM6IHtcclxuICAgICAgICAgIGVuYWJsZWQ6IHRydWVcclxuICAgICAgICB9LFxyXG4gICAgICAgIGR0czogdHJ1ZSxcclxuICAgICAgICB2dWVUZW1wbGF0ZTogZmFsc2VcclxuICAgICAgfSksXHJcbiAgICAgIC8vc3ZnXHU2M0QyXHU0RUY2XHU5MTREXHU3RjZFXHJcbiAgICAgIGNyZWF0ZVN2Z0ljb25zUGx1Z2luKHtcclxuICAgICAgICAvLyBcdTYzMDdcdTVCOUFcdTk3MDBcdTg5ODFcdTdGMTNcdTVCNThcdTc2ODRcdTU2RkVcdTY4MDdcdTY1ODdcdTRFRjZcdTU5MzlcclxuICAgICAgICBpY29uRGlyczogW3Jlc29sdmUocHJvY2Vzcy5jd2QoKSwgJ3NyYy9hc3NldHMvaWNvbnMnKV0sXHJcbiAgICAgICAgLy8gXHU2MzA3XHU1QjlBc3ltYm9sSWRcdTY4M0NcdTVGMEZcclxuICAgICAgICBzeW1ib2xJZDogJ2ljb24tW2Rpcl0tW25hbWVdJ1xyXG4gICAgICB9KSxcclxuICAgICAgLy8gXHU2MjUzXHU1MzA1XHU0RjUzXHU3OUVGXHU5ODg0XHU4OUM4XHJcbiAgICAgIHZpc3VhbGl6ZXIoe1xyXG4gICAgICAgIG9wZW46IHRydWVcclxuICAgICAgfSksXHJcbiAgICAgIC8vIGd6aXBcdTUzOEJcdTdGMjlcdTY1ODdcdTRFRjZcdTRGNTNcdTc5RUZcclxuICAgICAgY29tcHJlc3Npb24oe1xyXG4gICAgICAgIGFsZ29yaXRobTogJ2d6aXAnLCAvLyBcdTYzMDdcdTVCOUFcdTUzOEJcdTdGMjlcdTdCOTdcdTZDRDVcdTRFM0FnemlwLFsgJ2d6aXAnICwgJ2Jyb3RsaUNvbXByZXNzJyAsJ2RlZmxhdGUnICwgJ2RlZmxhdGVSYXcnXVxyXG4gICAgICAgIGV4dDogJy5neicsIC8vIFx1NjMwN1x1NUI5QVx1NTM4Qlx1N0YyOVx1NTQwRVx1NzY4NFx1NjU4N1x1NEVGNlx1NjI2OVx1NUM1NVx1NTQwRFx1NEUzQS5nelxyXG4gICAgICAgIHRocmVzaG9sZDogMTAyNDAsIC8vIFx1NEVDNVx1NUJGOVx1NjU4N1x1NEVGNlx1NTkyN1x1NUMwRlx1NTkyN1x1NEU4RXRocmVzaG9sZFx1NzY4NFx1NjU4N1x1NEVGNlx1OEZEQlx1ODg0Q1x1NTM4Qlx1N0YyOVx1RkYwQ1x1OUVEOFx1OEJBNFx1NEUzQTEwS0JcclxuICAgICAgICBkZWxldGVPcmlnaW5GaWxlOiBmYWxzZSwgLy8gXHU2NjJGXHU1NDI2XHU1MjIwXHU5NjY0XHU1MzlGXHU1OUNCXHU2NTg3XHU0RUY2XHVGRjBDXHU5RUQ4XHU4QkE0XHU0RTNBZmFsc2VcclxuICAgICAgICBmaWx0ZXI6IC9cXC4oanN8Y3NzfGpzb258aHRtbHxpY298c3ZnKShcXD8uKik/JC9pLCAvLyBcdTUzMzlcdTkxNERcdTg5ODFcdTUzOEJcdTdGMjlcdTc2ODRcdTY1ODdcdTRFRjZcdTc2ODRcdTZCNjNcdTUyMTlcdTg4NjhcdThGQkVcdTVGMEZcdUZGMENcdTlFRDhcdThCQTRcdTRFM0FcdTUzMzlcdTkxNEQuanNcdTMwMDEuY3NzXHUzMDAxLmpzb25cdTMwMDEuaHRtbFx1MzAwMS5pY29cdTU0OEMuc3ZnXHU2NTg3XHU0RUY2XHJcbiAgICAgICAgY29tcHJlc3Npb25PcHRpb25zOiB7IGxldmVsOiA5IH0sIC8vIFx1NjMwN1x1NUI5QWd6aXBcdTUzOEJcdTdGMjlcdTdFQTdcdTUyMkJcdUZGMENcdTlFRDhcdThCQTRcdTRFM0E5XHVGRjA4XHU2NzAwXHU5QUQ4XHU3RUE3XHU1MjJCXHVGRjA5XHJcbiAgICAgICAgdmVyYm9zZTogdHJ1ZSwgLy9cdTY2MkZcdTU0MjZcdTU3MjhcdTYzQTdcdTUyMzZcdTUzRjBcdThGOTNcdTUxRkFcdTUzOEJcdTdGMjlcdTdFRDNcdTY3OUNcclxuICAgICAgICBkaXNhYmxlOiBmYWxzZSAvL1x1NjYyRlx1NTQyNlx1Nzk4MVx1NzUyOFx1NjNEMlx1NEVGNlxyXG4gICAgICB9KSxcclxuICAgICAgSW5zcGVjdCgpXHJcbiAgICBdLFxyXG4gICAgLy9zY3NzXHU5ODg0XHU1OTA0XHU3NDA2XHJcbiAgICBjc3M6IHtcclxuICAgICAgcHJlcHJvY2Vzc29yT3B0aW9uczoge1xyXG4gICAgICAgIHNjc3M6IHtcclxuICAgICAgICAgIGFkZGl0aW9uYWxEYXRhOiBcIkBpbXBvcnQgJ0Avc3R5bGVzL3ZhcmlhYmxlcy5zY3NzJztcIlxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIHNlcnZlcjoge1xyXG4gICAgICBobXI6IHRydWUsIC8vIFx1NTQyRlx1NzUyOFx1NzBFRFx1NjZGNFx1NjVCMFxyXG4gICAgICAvLyBcdTUxNDFcdThCQjhcdThERThcdTU3REZcclxuICAgICAgY29yczogdHJ1ZSxcclxuICAgICAgLy8gXHU1NDJGXHU1MkE4XHU5ODc5XHU3NkVFXHU2NUY2XHU4MUVBXHU1MkE4XHU2MjUzXHU1RjAwXHJcbiAgICAgIG9wZW46IGZhbHNlLFxyXG4gICAgICBwb3J0OiAzMDAwLFxyXG4gICAgICBwcm94eVxyXG4gICAgfSxcclxuICAgIGJhc2U6ICcvc2VydmljZVdlYi8nLFxyXG4gICAgYnVpbGQ6IHtcclxuICAgICAgdGVyc2VyT3B0aW9uczoge1xyXG4gICAgICAgIGNvbXByZXNzOiB7XHJcbiAgICAgICAgICAvL1x1NjYyRlx1NTQyNlx1NTIyMFx1OTY2NGNvbnNvbGVcclxuICAgICAgICAgIGRyb3BfY29uc29sZTogdHJ1ZSxcclxuICAgICAgICAgIC8vXHU2NjJGXHU1NDI2XHU1MjIwXHU5NjY0ZGVidWdnZXJcclxuICAgICAgICAgIGRyb3BfZGVidWdnZXI6IHRydWUsXHJcbiAgICAgICAgICAvL1x1NjYyRlx1NTQyNlx1NTIyMFx1OTY2NFx1NjVFMFx1NzUyOFx1NEVFM1x1NzgwMVxyXG4gICAgICAgICAgdW51c2VkOiB0cnVlLFxyXG4gICAgICAgICAgLy9cdTY2MkZcdTU0MjZcdTRGRERcdTc1NTlcdTYyNDBcdTY3MDlcdTZDRThcdTkxQ0FcclxuICAgICAgICAgIGtlZXBfZmFyZ3M6IGZhbHNlXHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG4gICAgICAvLyBcdThCQkVcdTdGNkVcdTY3MDBcdTdFQzhcdTY3ODRcdTVFRkFcdTc2ODRcdTZENEZcdTg5QzhcdTU2NjhcdTUxN0NcdTVCQjlcdTc2RUVcdTY4MDdcclxuICAgICAgdGFyZ2V0OiAnZXMyMDE1JyxcclxuICAgICAgLy8gXHU2Nzg0XHU1RUZBXHU1NDBFXHU2NjJGXHU1NDI2XHU3NTFGXHU2MjEwIHNvdXJjZSBtYXAgXHU2NTg3XHU0RUY2XHJcbiAgICAgIHNvdXJjZW1hcDogZmFsc2UsXHJcbiAgICAgIC8vICBjaHVuayBcdTU5MjdcdTVDMEZcdThCNjZcdTU0NEFcdTc2ODRcdTk2NTBcdTUyMzZcdUZGMDhcdTRFRTUga2JzIFx1NEUzQVx1NTM1NVx1NEY0RFx1RkYwOVxyXG4gICAgICBjaHVua1NpemVXYXJuaW5nTGltaXQ6IDIwMDAsXHJcbiAgICAgIC8vIFx1NTQyRlx1NzUyOC9cdTc5ODFcdTc1MjggZ3ppcCBcdTUzOEJcdTdGMjlcdTU5MjdcdTVDMEZcdTYyQTVcdTU0NEFcclxuICAgICAgcmVwb3J0Q29tcHJlc3NlZFNpemU6IGZhbHNlLFxyXG4gICAgICAvLyBcdTgxRUFcdTVCOUFcdTRFNDlcdTVFOTVcdTVDNDJcdTc2ODQgUm9sbHVwIFx1NjI1M1x1NTMwNVx1OTE0RFx1N0Y2RVxyXG4gICAgICBtaW5pZnk6IGZhbHNlLFxyXG4gICAgICByb2xsdXBPcHRpb25zOiB7XHJcbiAgICAgICAgb3V0cHV0OiB7XHJcbiAgICAgICAgICAvLyBcdTY3MDBcdTVDMEZcdTUzMTZcdTYyQzZcdTUyMDZcdTUzMDVcclxuICAgICAgICAgIG1hbnVhbENodW5rcyhpZCkge1xyXG4gICAgICAgICAgICBpZiAoaWQuaW5jbHVkZXMoJ25vZGVfbW9kdWxlcycpKSB7XHJcbiAgICAgICAgICAgICAgLy8gXHU5MDFBXHU4RkM3XHU2MkM2XHU1MjA2XHU1MzA1XHU3Njg0XHU2NUI5XHU1RjBGXHU1QzA2XHU2MjQwXHU2NzA5XHU2NzY1XHU4MUVBbm9kZV9tb2R1bGVzXHU3Njg0XHU2QTIxXHU1NzU3XHU2MjUzXHU1MzA1XHU1MjMwXHU1MzU1XHU3MkVDXHU3Njg0Y2h1bmtcdTRFMkRcclxuICAgICAgICAgICAgICByZXR1cm4gaWRcclxuICAgICAgICAgICAgICAgIC50b1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAuc3BsaXQoJ25vZGVfbW9kdWxlcy8nKVsxXVxyXG4gICAgICAgICAgICAgICAgLnNwbGl0KCcvJylbMF1cclxuICAgICAgICAgICAgICAgIC50b1N0cmluZygpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICAvLyBcdTVCRjlcdTRFRTNcdTc4MDFcdTUyMDZcdTUyNzJcdTRFMkRcdTRFQTdcdTc1MUZcdTc2ODQgY2h1bmsgXHU4MUVBXHU1QjlBXHU0RTQ5XHU1NDdEXHU1NDBEXHJcbiAgICAgICAgICBjaHVua0ZpbGVOYW1lczogJ3N0YXRpYy9qcy9bbmFtZV0tW2hhc2hdLmpzJyxcclxuICAgICAgICAgIC8vIFx1NjMwN1x1NUI5QSBjaHVua3MgXHU3Njg0XHU1MTY1XHU1M0UzXHU2NTg3XHU0RUY2XHU2QTIxXHU1RjBGXHJcbiAgICAgICAgICBlbnRyeUZpbGVOYW1lczogJ3N0YXRpYy9qcy9bbmFtZV0tW2hhc2hdLmpzJyxcclxuICAgICAgICAgIC8vIFx1ODFFQVx1NUI5QVx1NEU0OVx1Njc4NFx1NUVGQVx1N0VEM1x1Njc5Q1x1NEUyRFx1NzY4NFx1OTc1OVx1NjAwMVx1OEQ0NFx1NkU5MFx1NTQwRFx1NzlGMFxyXG4gICAgICAgICAgYXNzZXRGaWxlTmFtZXM6ICdzdGF0aWMvW2V4dF0vW25hbWVdLVtoYXNoXS5bZXh0XScsXHJcbiAgICAgICAgICAvLyBcdTUzOEJcdTdGMjkgUm9sbHVwIFx1NEVBN1x1NzUxRlx1NzY4NFx1OTg5RFx1NTkxNlx1NEVFM1x1NzgwMVxyXG4gICAgICAgICAgY29tcGFjdDogdHJ1ZVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufSlcclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJEOlxcXFxcdTY1QjBcdTY2MzFcXFxcY2l0eWJyYWluX3Nlcml2ZVxcXFxwbHVnaW5zXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJEOlxcXFxcdTY1QjBcdTY2MzFcXFxcY2l0eWJyYWluX3Nlcml2ZVxcXFxwbHVnaW5zXFxcXHNyY1JlcGxhY2UudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0Q6LyVFNiU5NiVCMCVFNiU5OCVCMS9jaXR5YnJhaW5fc2VyaXZlL3BsdWdpbnMvc3JjUmVwbGFjZS50c1wiO2NvbnN0IFJleCA9IC9cXC52dWUkL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdml0ZVNyY1JlcGxhY2UoKSB7XG4gIGxldCBwdWJsaWNQYXRoID0gJydcbiAgbGV0IGNvbW1hbmQgPSAnJ1xuICByZXR1cm4ge1xuICAgIG5hbWU6ICd2aXRlLXBsdWdpbi12dWUtc3JjLXJlcGxhY2UnLFxuICAgIGNvbmZpZyhjb25maWc6IHsgc2VydmVyOiB7IHBvcnQ6IHN0cmluZyB9IH0sIGVudjogeyBjb21tYW5kOiBzdHJpbmcgfSkge1xuICAgICAgcHVibGljUGF0aCA9ICdodHRwOi8vbG9jYWxob3N0OicgKyBjb25maWcuc2VydmVyLnBvcnRcbiAgICAgIGNvbW1hbmQgPSBlbnYuY29tbWFuZFxuICAgIH0sXG4gICAgdHJhbnNmb3JtKGNvZGU6IHN0cmluZywgc3JjOiBzdHJpbmcpIHtcbiAgICAgIGlmIChSZXgudGVzdChzcmMpICYmIGNvbW1hbmQgPT09ICdzZXJ2ZScpIHtcbiAgICAgICAgY29uc3QgcyA9IGNvZGUucmVwbGFjZSgvPGltZ1xccytbXj5dKnNyYz1cIihbXlwiXSopXCJbXj5dKj4vZywgKG0sIHAxKSA9PiB7XG4gICAgICAgICAgcmV0dXJuIG0ucmVwbGFjZShwMSwgYCR7cHVibGljUGF0aH0ke3AxfWApO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBjb2RlOiBzXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGlmICgvXFwuKHNjc3N8Y3NzKSQvLnRlc3Qoc3JjKSAmJiBjb21tYW5kID09PSAnc2VydmUnKSB7XG4gICAgICAgIGNvbnN0IHMgPSBjb2RlLnJlcGxhY2UoLyg/PD11cmxcXChcIikoW15cIl0qKSg/PVwiXFwpKS9nLCBgJHtwdWJsaWNQYXRofSQxYClcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBjb2RlOiBzXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNvZGVcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBMFEsU0FBUyxjQUFjLGVBQWU7QUFDaFQsU0FBUyxlQUFlO0FBQ3hCLE9BQU8sU0FBUztBQUdoQixPQUFPLGdCQUFnQjtBQUN2QixPQUFPLGlCQUFpQjs7O0FDTjBRLElBQU0sTUFBTTtBQUMvUixTQUFSLGlCQUFrQztBQUN2QyxNQUFJLGFBQWE7QUFDakIsTUFBSSxVQUFVO0FBQ2QsU0FBTztBQUFBLElBQ0wsTUFBTTtBQUFBLElBQ04sT0FBTyxRQUFzQyxLQUEwQjtBQUNyRSxtQkFBYSxzQkFBc0IsT0FBTyxPQUFPO0FBQ2pELGdCQUFVLElBQUk7QUFBQSxJQUNoQjtBQUFBLElBQ0EsVUFBVSxNQUFjLEtBQWE7QUFDbkMsVUFBSSxJQUFJLEtBQUssR0FBRyxLQUFLLFlBQVksU0FBUztBQUN4QyxjQUFNLElBQUksS0FBSyxRQUFRLG9DQUFvQyxDQUFDLEdBQUcsT0FBTztBQUNwRSxpQkFBTyxFQUFFLFFBQVEsSUFBSSxHQUFHLFVBQVUsR0FBRyxFQUFFLEVBQUU7QUFBQSxRQUMzQyxDQUFDO0FBQ0QsZUFBTztBQUFBLFVBQ0wsTUFBTTtBQUFBLFFBQ1I7QUFBQSxNQUNGO0FBQ0EsVUFBSSxnQkFBZ0IsS0FBSyxHQUFHLEtBQUssWUFBWSxTQUFTO0FBQ3BELGNBQU0sSUFBSSxLQUFLLFFBQVEsOEJBQThCLEdBQUcsVUFBVSxJQUFJO0FBQ3RFLGVBQU87QUFBQSxVQUNMLE1BQU07QUFBQSxRQUNSO0FBQUEsTUFDRjtBQUNBLGFBQU87QUFBQSxRQUNMO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQ0Y7OztBRHRCQSxTQUFTLGtCQUFrQjtBQUUzQixPQUFPLGFBQWE7QUFHcEIsU0FBUyw0QkFBNEI7QUFFckMsT0FBTyxjQUFjO0FBZnJCLElBQU0sbUNBQW1DO0FBa0J6QyxJQUFPLHNCQUFRLGFBQWEsQ0FBQyxFQUFFLEtBQUssTUFBTTtBQUN4QyxRQUFNLE1BQU0sUUFBUSxNQUFNLFFBQVEsSUFBSSxDQUFDO0FBRXZDLFFBQU0sRUFBRSxlQUFlLG1CQUFtQixJQUFJO0FBQzlDLE1BQUksUUFBUSxDQUFDO0FBRWIsUUFBTSxZQUFZLENBQUM7QUFDbkIsTUFBSSxzQkFBc0IsdUJBQXVCLElBQUk7QUFDbkQsWUFBUSxPQUFPLE9BQU8sV0FBVztBQUFBLE1BQy9CLENBQUMsYUFBYSxHQUFHO0FBQUEsUUFDZixRQUFRO0FBQUEsUUFDUixjQUFjO0FBQUEsUUFDZCxRQUFRO0FBQUEsUUFDUixTQUFTLENBQUMsTUFBYyxFQUFFLFFBQVEsZUFBZSxFQUFFO0FBQUEsTUFDckQ7QUFBQSxJQUNGLENBQUM7QUFBQSxFQUNIO0FBRUEsU0FBTztBQUFBLElBQ0wsU0FBUztBQUFBLE1BQ1AsT0FBTztBQUFBLFFBQ0wsTUFBTSxHQUFHLFFBQVEsa0NBQVcsS0FBSyxDQUFDO0FBQUEsTUFDcEM7QUFBQSxJQUNGO0FBQUEsSUFDQSxTQUFTO0FBQUEsTUFDUCxJQUFJO0FBQUEsTUFDSixlQUFlO0FBQUEsTUFDZixTQUFTO0FBQUE7QUFBQSxNQUVULFdBQVc7QUFBQSxRQUNULFNBQVMsQ0FBQyxPQUFPLGNBQWMsU0FBUyxjQUFjO0FBQUEsUUFDdEQsVUFBVTtBQUFBLFVBQ1IsU0FBUztBQUFBLFFBQ1g7QUFBQSxRQUNBLEtBQUs7QUFBQSxRQUNMLGFBQWE7QUFBQSxNQUNmLENBQUM7QUFBQTtBQUFBLE1BRUQscUJBQXFCO0FBQUE7QUFBQSxRQUVuQixVQUFVLENBQUMsUUFBUSxRQUFRLElBQUksR0FBRyxrQkFBa0IsQ0FBQztBQUFBO0FBQUEsUUFFckQsVUFBVTtBQUFBLE1BQ1osQ0FBQztBQUFBO0FBQUEsTUFFRCxXQUFXO0FBQUEsUUFDVCxNQUFNO0FBQUEsTUFDUixDQUFDO0FBQUE7QUFBQSxNQUVELFlBQVk7QUFBQSxRQUNWLFdBQVc7QUFBQTtBQUFBLFFBQ1gsS0FBSztBQUFBO0FBQUEsUUFDTCxXQUFXO0FBQUE7QUFBQSxRQUNYLGtCQUFrQjtBQUFBO0FBQUEsUUFDbEIsUUFBUTtBQUFBO0FBQUEsUUFDUixvQkFBb0IsRUFBRSxPQUFPLEVBQUU7QUFBQTtBQUFBLFFBQy9CLFNBQVM7QUFBQTtBQUFBLFFBQ1QsU0FBUztBQUFBO0FBQUEsTUFDWCxDQUFDO0FBQUEsTUFDRCxRQUFRO0FBQUEsSUFDVjtBQUFBO0FBQUEsSUFFQSxLQUFLO0FBQUEsTUFDSCxxQkFBcUI7QUFBQSxRQUNuQixNQUFNO0FBQUEsVUFDSixnQkFBZ0I7QUFBQSxRQUNsQjtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBQUEsSUFDQSxRQUFRO0FBQUEsTUFDTixLQUFLO0FBQUE7QUFBQTtBQUFBLE1BRUwsTUFBTTtBQUFBO0FBQUEsTUFFTixNQUFNO0FBQUEsTUFDTixNQUFNO0FBQUEsTUFDTjtBQUFBLElBQ0Y7QUFBQSxJQUNBLE1BQU07QUFBQSxJQUNOLE9BQU87QUFBQSxNQUNMLGVBQWU7QUFBQSxRQUNiLFVBQVU7QUFBQTtBQUFBLFVBRVIsY0FBYztBQUFBO0FBQUEsVUFFZCxlQUFlO0FBQUE7QUFBQSxVQUVmLFFBQVE7QUFBQTtBQUFBLFVBRVIsWUFBWTtBQUFBLFFBQ2Q7QUFBQSxNQUNGO0FBQUE7QUFBQSxNQUVBLFFBQVE7QUFBQTtBQUFBLE1BRVIsV0FBVztBQUFBO0FBQUEsTUFFWCx1QkFBdUI7QUFBQTtBQUFBLE1BRXZCLHNCQUFzQjtBQUFBO0FBQUEsTUFFdEIsUUFBUTtBQUFBLE1BQ1IsZUFBZTtBQUFBLFFBQ2IsUUFBUTtBQUFBO0FBQUEsVUFFTixhQUFhLElBQUk7QUFDZixnQkFBSSxHQUFHLFNBQVMsY0FBYyxHQUFHO0FBRS9CLHFCQUFPLEdBQ0osU0FBUyxFQUNULE1BQU0sZUFBZSxFQUFFLENBQUMsRUFDeEIsTUFBTSxHQUFHLEVBQUUsQ0FBQyxFQUNaLFNBQVM7QUFBQSxZQUNkO0FBQUEsVUFDRjtBQUFBO0FBQUEsVUFFQSxnQkFBZ0I7QUFBQTtBQUFBLFVBRWhCLGdCQUFnQjtBQUFBO0FBQUEsVUFFaEIsZ0JBQWdCO0FBQUE7QUFBQSxVQUVoQixTQUFTO0FBQUEsUUFDWDtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBQUEsRUFDRjtBQUNGLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
